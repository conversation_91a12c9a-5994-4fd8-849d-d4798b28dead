package aegon.chrome.base.compat;

import aegon.chrome.base.ApiCompatibilityUtils;
import aegon.chrome.base.ContextUtils;
import android.app.PictureInPictureParams;
import android.content.ClipData;
import android.content.ClipDescription;
import android.os.Process;
import android.view.textclassifier.TextClassification;
import android.view.textclassifier.TextLinks;
import android.view.textclassifier.TextSelection;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class ApiHelperForS {
    private static final String TAG = "ApiHelperForS";

    private ApiHelperForS() {
    }

    public static boolean isStyleText(ClipDescription clipDescription) {
        return clipDescription.isStyledText();
    }

    public static float getConfidenceScore(ClipDescription clipDescription, String str) {
        return clipDescription.getConfidenceScore(str);
    }

    public static boolean isGetClassificationStatusIsComplete(ClipDescription clipDescription) {
        return clipDescription.getClassificationStatus() == 3;
    }

    public static TextLinks getTextLinks(ClipData.Item item) {
        return item.getTextLinks();
    }

    public static boolean hasBluetoothConnectPermission() {
        return ApiCompatibilityUtils.checkPermission(ContextUtils.getApplicationContext(), "android.permission.BLUETOOTH_CONNECT", Process.myPid(), Process.myUid()) == 0;
    }

    public static void setAutoEnterEnabled(PictureInPictureParams.Builder builder, boolean z) {
        builder.setAutoEnterEnabled(z);
    }

    public static TextSelection.Request.Builder setIncludeTextClassification(TextSelection.Request.Builder builder, boolean z) {
        return builder.setIncludeTextClassification(z);
    }

    public static TextClassification getTextClassification(TextSelection textSelection) {
        return textSelection.getTextClassification();
    }
}
