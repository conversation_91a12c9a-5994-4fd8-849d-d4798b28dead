package android.icu.impl.data;

import java.util.ListResourceBundle;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class HolidayBundle_el extends ListResourceBundle {
    private static final Object[][] fContents = {new Object[]{"Assumption", "15 Αύγούστου"}, new Object[]{"Boxing Day", "Δεύτερη μέρα τών Χριστουγέννων"}, new Object[]{"Christmas", "Χριστούγεννα"}, new Object[]{"Clean Monday", "Καθαρή Δευτέρα"}, new Object[]{"Easter Monday", "Δεύτερη μέρα τού Πάσχα"}, new Object[]{"Epiphany", "Έπιφάνεια"}, new Object[]{"Good Friday", "Μεγάλη Παρασκευή"}, new Object[]{"May Day", "Πρωτομαγιά"}, new Object[]{"New Year's Day", "Πρωτοχρονιά"}, new Object[]{"Ochi Day", "28 Όκτωβρίου"}, new Object[]{"Whit Monday", "Δεύτερη μέρα τού Πεντηκοστή"}};

    @Override // java.util.ListResourceBundle
    public synchronized Object[][] getContents() {
        return fContents;
    }
}
