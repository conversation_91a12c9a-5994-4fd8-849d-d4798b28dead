package aegon.chrome.base;

import aegon.chrome.base.TraceEvent;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class TraceEventJni implements TraceEvent.Natives {
    public static final JniStaticTestMocker<TraceEvent.Natives> TEST_HOOKS = new JniStaticTestMocker<TraceEvent.Natives>() { // from class: aegon.chrome.base.TraceEventJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(TraceEvent.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                TraceEvent.Natives unused = TraceEventJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static TraceEvent.Natives testInstance;

    TraceEventJni() {
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void registerEnabledObserver() {
        GEN_JNI.org_chromium_base_TraceEvent_registerEnabledObserver();
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void startATrace(String str) {
        GEN_JNI.org_chromium_base_TraceEvent_startATrace(str);
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void stopATrace() {
        GEN_JNI.org_chromium_base_TraceEvent_stopATrace();
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void setupATraceStartupTrace(String str) {
        GEN_JNI.org_chromium_base_TraceEvent_setupATraceStartupTrace(str);
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void instant(String str, String str2) {
        GEN_JNI.org_chromium_base_TraceEvent_instant(str, str2);
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void begin(String str, String str2) {
        GEN_JNI.org_chromium_base_TraceEvent_begin(str, str2);
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void end(String str, String str2) {
        GEN_JNI.org_chromium_base_TraceEvent_end(str, str2);
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void beginToplevel(String str) {
        GEN_JNI.org_chromium_base_TraceEvent_beginToplevel(str);
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void endToplevel(String str) {
        GEN_JNI.org_chromium_base_TraceEvent_endToplevel(str);
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void startAsync(String str, long j) {
        GEN_JNI.org_chromium_base_TraceEvent_startAsync(str, j);
    }

    @Override // aegon.chrome.base.TraceEvent.Natives
    public void finishAsync(String str, long j) {
        GEN_JNI.org_chromium_base_TraceEvent_finishAsync(str, j);
    }

    public static TraceEvent.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            TraceEvent.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.TraceEvent.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new TraceEventJni();
    }
}
