package aegon.chrome.base;

import android.os.Process;
import java.util.HashMap;
import java.util.Iterator;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class UserDataHost {
    private final long mThreadId = Process.myTid();
    private HashMap<Class<? extends UserData>, UserData> mUserDataMap = new HashMap<>();

    private static void checkArgument(boolean z) {
        if (!z) {
            throw new IllegalArgumentException("Neither key nor object of UserDataHost can be null.");
        }
    }

    private void checkThreadAndState() {
        if (this.mThreadId != Process.myTid()) {
            throw new IllegalStateException("UserData must only be used on a single thread.");
        }
        if (this.mUserDataMap == null) {
            throw new IllegalStateException("Operation is not allowed after destroy().");
        }
    }

    public final <T extends UserData> T setUserData(Class<T> cls, T t) {
        checkThreadAndState();
        checkArgument((cls == null || t == null) ? false : true);
        this.mUserDataMap.put(cls, t);
        return (T) getUserData(cls);
    }

    public final <T extends UserData> T getUserData(Class<T> cls) {
        checkThreadAndState();
        checkArgument(cls != null);
        return cls.cast(this.mUserDataMap.get(cls));
    }

    public final <T extends UserData> T removeUserData(Class<T> cls) {
        checkThreadAndState();
        checkArgument(cls != null);
        if (!this.mUserDataMap.containsKey(cls)) {
            throw new IllegalStateException("UserData for the key is not present.");
        }
        return cls.cast(this.mUserDataMap.remove(cls));
    }

    public final void destroy() {
        checkThreadAndState();
        HashMap<Class<? extends UserData>, UserData> map = this.mUserDataMap;
        this.mUserDataMap = null;
        Iterator<UserData> itIterator2 = map.values().iterator2();
        while (itIterator2.hasNext()) {
            itIterator2.mo35924next().destroy();
        }
    }
}
