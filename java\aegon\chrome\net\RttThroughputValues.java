package aegon.chrome.net;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@Retention(RetentionPolicy.SOURCE)
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public @interface RttThroughputValues {
    public static final int INVALID_RTT_THROUGHPUT = -1;
}
