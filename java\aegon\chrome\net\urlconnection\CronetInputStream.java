package aegon.chrome.net.urlconnection;

import java.nio.ByteBuffer;
import java.p654io.IOException;
import java.p654io.InputStream;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class CronetInputStream extends InputStream {
    private static final int READ_BUFFER_SIZE = 32768;
    private ByteBuffer mBuffer;
    private IOException mException;
    private final CronetHttpURLConnection mHttpURLConnection;
    private boolean mResponseDataCompleted;

    public CronetInputStream(CronetHttpURLConnection cronetHttpURLConnection) {
        this.mHttpURLConnection = cronetHttpURLConnection;
    }

    @Override // java.p654io.InputStream
    public int read() throws IOException {
        getMoreDataIfNeeded();
        if (hasUnreadData()) {
            return this.mBuffer.get() & 255;
        }
        return -1;
    }

    @Override // java.p654io.InputStream
    public int read(byte[] bArr, int i, int i2) throws IOException {
        if (i < 0 || i2 < 0 || i + i2 > bArr.length) {
            throw new IndexOutOfBoundsException();
        }
        if (i2 == 0) {
            return 0;
        }
        getMoreDataIfNeeded();
        if (!hasUnreadData()) {
            return -1;
        }
        int iMin = Math.min(this.mBuffer.limit() - this.mBuffer.position(), i2);
        this.mBuffer.get(bArr, i, iMin);
        return iMin;
    }

    @Override // java.p654io.InputStream
    public int available() throws IOException {
        if (this.mResponseDataCompleted) {
            IOException iOException = this.mException;
            if (iOException == null) {
                return 0;
            }
            throw iOException;
        }
        if (hasUnreadData()) {
            return this.mBuffer.remaining();
        }
        return 0;
    }

    void setResponseDataCompleted(IOException iOException) {
        this.mException = iOException;
        this.mResponseDataCompleted = true;
        this.mBuffer = null;
    }

    private void getMoreDataIfNeeded() throws IOException {
        if (this.mResponseDataCompleted) {
            IOException iOException = this.mException;
            if (iOException != null) {
                throw iOException;
            }
        } else {
            if (hasUnreadData()) {
                return;
            }
            if (this.mBuffer == null) {
                this.mBuffer = ByteBuffer.allocateDirect(32768);
            }
            this.mBuffer.clear();
            this.mHttpURLConnection.getMoreData(this.mBuffer);
            IOException iOException2 = this.mException;
            if (iOException2 != null) {
                throw iOException2;
            }
            ByteBuffer byteBuffer = this.mBuffer;
            if (byteBuffer != null) {
                byteBuffer.flip();
            }
        }
    }

    private boolean hasUnreadData() {
        ByteBuffer byteBuffer = this.mBuffer;
        return byteBuffer != null && byteBuffer.hasRemaining();
    }
}
