package aegon.chrome.net;

import aegon.chrome.base.ContextUtils;
import aegon.chrome.base.ThreadUtils;
import aegon.chrome.base.annotations.MainDex;
import android.telephony.PhoneStateListener;
import android.telephony.ServiceState;
import android.telephony.TelephonyManager;
import javax.annotation.CheckForNull;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class AndroidTelephonyManagerBridge {
    private static volatile AndroidTelephonyManagerBridge sInstance;
    private Listener mListener;

    @CheckForNull
    private volatile String mNetworkCountryIso;

    @CheckForNull
    private volatile String mNetworkOperator;

    @CheckForNull
    private volatile String mSimOperator;

    private AndroidTelephonyManagerBridge() {
    }

    public String getNetworkCountryIso() {
        if (this.mNetworkCountryIso == null) {
            TelephonyManager telephonyManager = getTelephonyManager();
            if (telephonyManager == null) {
                return "";
            }
            this.mNetworkCountryIso = telephonyManager.getNetworkCountryIso();
        }
        return this.mNetworkCountryIso;
    }

    public String getNetworkOperator() {
        if (this.mNetworkOperator == null) {
            TelephonyManager telephonyManager = getTelephonyManager();
            if (telephonyManager == null) {
                return "";
            }
            this.mNetworkOperator = telephonyManager.getNetworkOperator();
        }
        return this.mNetworkOperator;
    }

    public String getSimOperator() {
        if (this.mSimOperator == null) {
            TelephonyManager telephonyManager = getTelephonyManager();
            if (telephonyManager == null) {
                return "";
            }
            this.mSimOperator = telephonyManager.getSimOperator();
        }
        return this.mSimOperator;
    }

    private void update(@CheckForNull TelephonyManager telephonyManager) {
        if (telephonyManager == null) {
            return;
        }
        this.mNetworkCountryIso = telephonyManager.getNetworkCountryIso();
        this.mNetworkOperator = telephonyManager.getNetworkOperator();
        this.mSimOperator = telephonyManager.getSimOperator();
    }

    private void listenTelephonyServiceState(TelephonyManager telephonyManager) {
        ThreadUtils.assertOnUiThread();
        this.mListener = new Listener();
        telephonyManager.listen(this.mListener, 1);
    }

    @CheckForNull
    private static TelephonyManager getTelephonyManager() {
        return (TelephonyManager) ContextUtils.getApplicationContext().getSystemService("phone");
    }

    private static AndroidTelephonyManagerBridge create() {
        final AndroidTelephonyManagerBridge androidTelephonyManagerBridge = new AndroidTelephonyManagerBridge();
        ThreadUtils.runOnUiThread(new Runnable() { // from class: aegon.chrome.net.-$$Lambda$AndroidTelephonyManagerBridge$BTehZRE_yl14NaVBOlwnaAc_bwU
            @Override // java.lang.Runnable
            public final void run() {
                AndroidTelephonyManagerBridge.lambda$create$0(this.f$0);
            }
        });
        return androidTelephonyManagerBridge;
    }

    static /* synthetic */ void lambda$create$0(AndroidTelephonyManagerBridge androidTelephonyManagerBridge) {
        TelephonyManager telephonyManager = getTelephonyManager();
        if (telephonyManager != null) {
            androidTelephonyManagerBridge.listenTelephonyServiceState(telephonyManager);
        }
    }

    public static AndroidTelephonyManagerBridge getInstance() {
        AndroidTelephonyManagerBridge androidTelephonyManagerBridgeCreate = sInstance;
        if (androidTelephonyManagerBridgeCreate == null) {
            synchronized (AndroidTelephonyManagerBridge.class) {
                androidTelephonyManagerBridgeCreate = sInstance;
                if (androidTelephonyManagerBridgeCreate == null) {
                    androidTelephonyManagerBridgeCreate = create();
                    sInstance = androidTelephonyManagerBridgeCreate;
                }
            }
        }
        return androidTelephonyManagerBridgeCreate;
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    class Listener extends PhoneStateListener {

        @CheckForNull
        private ServiceState mServiceState;

        private Listener() {
        }

        @Override // android.telephony.PhoneStateListener
        public void onServiceStateChanged(ServiceState serviceState) {
            ServiceState serviceState2 = this.mServiceState;
            if (serviceState2 == null || !serviceState2.equals(serviceState)) {
                this.mServiceState = serviceState;
                AndroidTelephonyManagerBridge.this.update(AndroidTelephonyManagerBridge.getTelephonyManager());
            }
        }
    }
}
