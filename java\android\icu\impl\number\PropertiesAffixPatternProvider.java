package android.icu.impl.number;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class PropertiesAffixPatternProvider implements AffixPatternProvider {
    private final boolean isCurrencyPattern;
    private final String negPrefix;
    private final String negSuffix;
    private final String posPrefix;
    private final String posSuffix;

    public PropertiesAffixPatternProvider(DecimalFormatProperties properties) {
        String ppo = AffixUtils.escape(properties.getPositivePrefix());
        String pso = AffixUtils.escape(properties.getPositiveSuffix());
        String npo = AffixUtils.escape(properties.getNegativePrefix());
        String nso = AffixUtils.escape(properties.getNegativeSuffix());
        String ppp = properties.getPositivePrefixPattern();
        String psp = properties.getPositiveSuffixPattern();
        String npp = properties.getNegativePrefixPattern();
        String nsp = properties.getNegativeSuffixPattern();
        if (ppo != null) {
            this.posPrefix = ppo;
        } else if (ppp != null) {
            this.posPrefix = ppp;
        } else {
            this.posPrefix = "";
        }
        if (pso != null) {
            this.posSuffix = pso;
        } else if (psp != null) {
            this.posSuffix = psp;
        } else {
            this.posSuffix = "";
        }
        if (npo != null) {
            this.negPrefix = npo;
        } else if (npp != null) {
            this.negPrefix = npp;
        } else {
            String str = "-";
            if (ppp != null) {
                str = "-" + ppp;
            }
            this.negPrefix = str;
        }
        if (nso != null) {
            this.negSuffix = nso;
        } else if (nsp == null) {
            this.negSuffix = psp != null ? psp : "";
        } else {
            this.negSuffix = nsp;
        }
        this.isCurrencyPattern = AffixUtils.hasCurrencySymbols(ppp) || AffixUtils.hasCurrencySymbols(psp) || AffixUtils.hasCurrencySymbols(npp) || AffixUtils.hasCurrencySymbols(nsp);
    }

    @Override // android.icu.impl.number.AffixPatternProvider
    public char charAt(int flags, int i) {
        return getString(flags).charAt(i);
    }

    @Override // android.icu.impl.number.AffixPatternProvider
    public int length(int flags) {
        return getString(flags).length();
    }

    @Override // android.icu.impl.number.AffixPatternProvider
    public String getString(int flags) {
        boolean prefix = (flags & 256) != 0;
        boolean negative = (flags & 512) != 0;
        if (prefix && negative) {
            return this.negPrefix;
        }
        if (prefix) {
            return this.posPrefix;
        }
        if (negative) {
            return this.negSuffix;
        }
        return this.posSuffix;
    }

    @Override // android.icu.impl.number.AffixPatternProvider
    public boolean positiveHasPlusSign() {
        return AffixUtils.containsType(this.posPrefix, -2) || AffixUtils.containsType(this.posSuffix, -2);
    }

    @Override // android.icu.impl.number.AffixPatternProvider
    public boolean hasNegativeSubpattern() {
        if (this.negSuffix == this.posSuffix && this.negPrefix.length() == this.posPrefix.length() + 1) {
            String str = this.negPrefix;
            String str2 = this.posPrefix;
            if (str.regionMatches(1, str2, 0, str2.length()) && this.negPrefix.charAt(0) == '-') {
                return false;
            }
        }
        return true;
    }

    @Override // android.icu.impl.number.AffixPatternProvider
    public boolean negativeHasMinusSign() {
        return AffixUtils.containsType(this.negPrefix, -1) || AffixUtils.containsType(this.negSuffix, -1);
    }

    @Override // android.icu.impl.number.AffixPatternProvider
    public boolean hasCurrencySign() {
        return this.isCurrencyPattern;
    }

    @Override // android.icu.impl.number.AffixPatternProvider
    public boolean containsSymbolType(int type) {
        return AffixUtils.containsType(this.posPrefix, type) || AffixUtils.containsType(this.posSuffix, type) || AffixUtils.containsType(this.negPrefix, type) || AffixUtils.containsType(this.negSuffix, type);
    }

    @Override // android.icu.impl.number.AffixPatternProvider
    public boolean hasBody() {
        return true;
    }

    public String toString() {
        return super.toString() + " {" + this.posPrefix + "#" + this.posSuffix + ";" + this.negPrefix + "#" + this.negSuffix + "}";
    }
}
