package android.icu.impl.number.parse;

import android.icu.impl.StringSegment;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class CodePointMatcher implements NumberParseMatcher {

    /* renamed from: cp */
    private final int f82cp;

    public static CodePointMatcher getInstance(int cp) {
        return new CodePointMatcher(cp);
    }

    private CodePointMatcher(int cp) {
        this.f82cp = cp;
    }

    @Override // android.icu.impl.number.parse.NumberParseMatcher
    public boolean match(StringSegment segment, ParsedNumber result) {
        if (segment.startsWith(this.f82cp)) {
            segment.adjustOffsetByCodePoint();
            result.setCharsConsumed(segment);
            return false;
        }
        return false;
    }

    @Override // android.icu.impl.number.parse.NumberParseMatcher
    public boolean smokeTest(StringSegment segment) {
        return segment.startsWith(this.f82cp);
    }

    @Override // android.icu.impl.number.parse.NumberParseMatcher
    public void postProcess(ParsedNumber result) {
    }

    public String toString() {
        return "<CodePointMatcher U+" + Integer.toHexString(this.f82cp) + ">";
    }
}
