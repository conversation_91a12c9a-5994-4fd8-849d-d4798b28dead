package aegon.chrome.base;

import aegon.chrome.base.PowerMonitor;
import aegon.chrome.base.natives.GEN_JNI;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class PowerMonitorJni implements PowerMonitor.Natives {
    public static final JniStaticTestMocker<PowerMonitor.Natives> TEST_HOOKS = new JniStaticTestMocker<PowerMonitor.Natives>() { // from class: aegon.chrome.base.PowerMonitorJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(PowerMonitor.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                PowerMonitor.Natives unused = PowerMonitorJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static PowerMonitor.Natives testInstance;

    PowerMonitorJni() {
    }

    @Override // aegon.chrome.base.PowerMonitor.Natives
    public void onBatteryChargingChanged() {
        GEN_JNI.org_chromium_base_PowerMonitor_onBatteryChargingChanged();
    }

    public static PowerMonitor.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            PowerMonitor.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.PowerMonitor.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new PowerMonitorJni();
    }
}
