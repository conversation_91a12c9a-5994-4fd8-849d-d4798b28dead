package aegon.chrome.net.test;

import aegon.chrome.net.CronetEngine;
import aegon.chrome.net.ExperimentalCronetEngine;
import aegon.chrome.net.test.FakeCronetEngine;
import aegon.chrome.net.test.FakeUrlResponse;
import android.content.Context;
import com.xiaomi.stat.MiStat;
import java.net.HttpURLConnection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class FakeCronetController {
    private static final List<CronetEngine> sInstances = Collections.synchronizedList(new ArrayList());
    private final List<ResponseMatcher> mResponseMatchers = Collections.synchronizedList(new ArrayList());

    public final CronetEngine.Builder newFakeCronetEngineBuilder(Context context) {
        FakeCronetEngine.Builder builder = new FakeCronetEngine.Builder(context);
        builder.setController(this);
        return new ExperimentalCronetEngine.Builder(builder);
    }

    public final void addResponseForUrl(FakeUrlResponse fakeUrlResponse, String str) {
        addResponseMatcher(new UrlResponseMatcher(str, fakeUrlResponse));
    }

    public final void addResponseMatcher(ResponseMatcher responseMatcher) {
        this.mResponseMatchers.add(responseMatcher);
    }

    public final void removeResponseMatcher(ResponseMatcher responseMatcher) {
        this.mResponseMatchers.remove(responseMatcher);
    }

    public final void clearResponseMatchers() {
        this.mResponseMatchers.clear();
    }

    public final void addRedirectResponse(String str, String str2) {
        addResponseForUrl(new FakeUrlResponse.Builder().setHttpStatusCode(302).addHeader(MiStat.Param.LOCATION, str).build(), str2);
    }

    public final void addHttpErrorResponse(int i, String str) {
        addResponseForUrl(getFailedResponse(i), str);
    }

    public final void addSuccessResponse(String str, byte[] bArr) {
        addResponseForUrl(new FakeUrlResponse.Builder().setResponseBody(bArr).build(), str);
    }

    public static FakeCronetController getControllerForFakeEngine(CronetEngine cronetEngine) {
        if (cronetEngine instanceof FakeCronetEngine) {
            return ((FakeCronetEngine) cronetEngine).getController();
        }
        throw new IllegalArgumentException("Provided CronetEngine is not a fake CronetEngine");
    }

    public static List<CronetEngine> getFakeCronetEngines() {
        ArrayList arrayList;
        synchronized (sInstances) {
            arrayList = new ArrayList(sInstances);
        }
        return arrayList;
    }

    static void removeFakeCronetEngine(CronetEngine cronetEngine) {
        sInstances.remove(cronetEngine);
    }

    static void addFakeCronetEngine(FakeCronetEngine fakeCronetEngine) {
        sInstances.add(fakeCronetEngine);
    }

    final FakeUrlResponse getResponse(String str, String str2, List<Map.Entry<String, String>> list, byte[] bArr) {
        synchronized (this.mResponseMatchers) {
            Iterator<ResponseMatcher> itIterator2 = this.mResponseMatchers.iterator2();
            while (itIterator2.hasNext()) {
                FakeUrlResponse matchingResponse = itIterator2.mo35924next().getMatchingResponse(str, str2, list, bArr);
                if (matchingResponse != null) {
                    return matchingResponse;
                }
            }
            return getFailedResponse(HttpURLConnection.HTTP_NOT_FOUND);
        }
    }

    private static FakeUrlResponse getFailedResponse(int i) {
        if (i < 400) {
            throw new IllegalArgumentException("Expected HTTP error code (code >= 400), but was: " + i);
        }
        return new FakeUrlResponse.Builder().setHttpStatusCode(i).build();
    }
}
