package android.icu.impl.data;

import java.util.ListResourceBundle;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class HolidayBundle_it extends ListResourceBundle {
    private static final Object[][] fContents = {new Object[]{"All Saints' Day", "Ognissanti"}, new Object[]{"Armistice Day", "armistizio"}, new Object[]{"Ascension", "ascensione"}, new Object[]{"Ash Wednesday", "mercoledì delle ceneri"}, new Object[]{"Boxing Day", "Santo Stefano"}, new Object[]{"Christmas", "natale"}, new Object[]{"Easter Sunday", "pasqua"}, new Object[]{"Epiphany", "Epifania"}, new Object[]{"Good Friday", "venerdì santo"}, new Object[]{"Halloween", "vigilia di Ognissanti"}, new Object[]{"Maundy Thursday", "giovedì santo"}, new Object[]{"New Year's Day", "anno nuovo"}, new Object[]{"Palm Sunday", "domenica delle palme"}, new Object[]{"Pentecost", "di Pentecoste"}, new Object[]{"Shrove Tuesday", "martedi grasso"}, new Object[]{"St. Stephen's Day", "Santo Stefano"}, new Object[]{"Thanksgiving", "Giorno del Ringraziamento"}};

    @Override // java.util.ListResourceBundle
    public synchronized Object[][] getContents() {
        return fContents;
    }
}
