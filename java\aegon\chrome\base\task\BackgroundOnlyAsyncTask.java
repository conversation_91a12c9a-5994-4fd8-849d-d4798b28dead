package aegon.chrome.base.task;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public abstract class BackgroundOnlyAsyncTask<Result> extends AsyncTask<Result> {
    static final /* synthetic */ boolean $assertionsDisabled = false;

    @Override // aegon.chrome.base.task.AsyncTask
    protected final void onPostExecute(Result result) {
    }
}
