package aegon.chrome.net.impl;

import aegon.chrome.base.Log;
import aegon.chrome.base.ObserverList;
import aegon.chrome.base.annotations.JNINamespace;
import aegon.chrome.base.annotations.NativeClassQualifiedName;
import aegon.chrome.net.BidirectionalStream;
import aegon.chrome.net.ExperimentalBidirectionalStream;
import aegon.chrome.net.NetworkQualityRttListener;
import aegon.chrome.net.NetworkQualityThroughputListener;
import aegon.chrome.net.RequestFinishedInfo;
import aegon.chrome.net.UrlRequest;
import aegon.chrome.net.impl.CronetEngineBuilderImpl;
import aegon.chrome.net.impl.SafeNativeFunctionCaller;
import aegon.chrome.net.impl.VersionSafeCallbacks;
import aegon.chrome.net.urlconnection.CronetHttpURLConnection;
import aegon.chrome.net.urlconnection.CronetURLStreamHandlerFactory;
import android.os.ConditionVariable;
import com.yxcorp.utility.uri.UriUtil;
import java.net.Proxy;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLStreamHandlerFactory;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.atomic.AtomicInteger;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace("cronet")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class CronetUrlRequestContext extends CronetEngineBase {
    private static final int LOG_DEBUG = -1;
    private static final int LOG_NONE = 3;
    private static final int LOG_VERBOSE = -2;
    private final String mInUseStoragePath;
    private boolean mIsLogging;
    private final boolean mNetworkQualityEstimatorEnabled;
    private Thread mNetworkThread;
    private volatile ConditionVariable mStopNetLogCompleted;
    private long mUrlRequestContextAdapter;
    static final String LOG_TAG = CronetUrlRequestContext.class.getSimpleName();
    private static final HashSet<String> sInUseStoragePaths = new HashSet<>();
    private final Object mLock = new Object();
    private final ConditionVariable mInitCompleted = new ConditionVariable(false);
    private final AtomicInteger mActiveRequestCount = new AtomicInteger(0);
    private final Object mNetworkQualityLock = new Object();
    private final Object mFinishedListenerLock = new Object();
    private int mEffectiveConnectionType = 0;
    private int mHttpRttMs = -1;
    private int mTransportRttMs = -1;
    private int mDownstreamThroughputKbps = -1;
    private final ObserverList<VersionSafeCallbacks.NetworkQualityRttListenerWrapper> mRttListenerList = new ObserverList<>();
    private final ObserverList<VersionSafeCallbacks.NetworkQualityThroughputListenerWrapper> mThroughputListenerList = new ObserverList<>();
    private final Map<RequestFinishedInfo.Listener, VersionSafeCallbacks.RequestFinishedInfoListener> mFinishedListenerMap = new HashMap();

    interface Natives {
        void addPkp(long j, String str, byte[][] bArr, boolean z, long j2);

        void addQuicHint(long j, String str, int i, int i2);

        @NativeClassQualifiedName("CronetURLRequestContextAdapter")
        void configureNetworkQualityEstimatorForTesting(long j, CronetUrlRequestContext cronetUrlRequestContext, boolean z, boolean z2, boolean z3);

        long createRequestContextAdapter(long j);

        long createRequestContextConfig(String str, String str2, boolean z, String str3, boolean z2, boolean z3, boolean z4, int i, long j, String str4, long j2, boolean z5, boolean z6, int i2);

        @NativeClassQualifiedName("CronetURLRequestContextAdapter")
        void destroy(long j, CronetUrlRequestContext cronetUrlRequestContext);

        byte[] getHistogramDeltas();

        @NativeClassQualifiedName("CronetURLRequestContextAdapter")
        void initRequestContextOnInitThread(long j, CronetUrlRequestContext cronetUrlRequestContext);

        @NativeClassQualifiedName("CronetURLRequestContextAdapter")
        void provideRTTObservations(long j, CronetUrlRequestContext cronetUrlRequestContext, boolean z);

        @NativeClassQualifiedName("CronetURLRequestContextAdapter")
        void provideThroughputObservations(long j, CronetUrlRequestContext cronetUrlRequestContext, boolean z);

        int setMinLogLevel(int i);

        @NativeClassQualifiedName("CronetURLRequestContextAdapter")
        void startNetLogToDisk(long j, CronetUrlRequestContext cronetUrlRequestContext, String str, boolean z, int i);

        @NativeClassQualifiedName("CronetURLRequestContextAdapter")
        boolean startNetLogToFile(long j, CronetUrlRequestContext cronetUrlRequestContext, String str, boolean z);

        @NativeClassQualifiedName("CronetURLRequestContextAdapter")
        void stopNetLog(long j, CronetUrlRequestContext cronetUrlRequestContext);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBase, aegon.chrome.net.ExperimentalCronetEngine, aegon.chrome.net.CronetEngine
    public /* bridge */ /* synthetic */ UrlRequest.Builder newUrlRequestBuilder(String str, UrlRequest.Callback callback, Executor executor) {
        return super.newUrlRequestBuilder(str, callback, executor);
    }

    public CronetUrlRequestContext(final CronetEngineBuilderImpl cronetEngineBuilderImpl) {
        this.mRttListenerList.disableThreadAsserts();
        this.mThroughputListenerList.disableThreadAsserts();
        this.mNetworkQualityEstimatorEnabled = cronetEngineBuilderImpl.networkQualityEstimatorEnabled();
        CronetLibraryLoader.ensureInitialized(cronetEngineBuilderImpl.getContext(), cronetEngineBuilderImpl);
        SafeNativeFunctionCaller.Ensure(new Runnable() { // from class: aegon.chrome.net.impl.-$$Lambda$CronetUrlRequestContext$8gF3cEF_YXV43gt3rqJRI8mTEVI
            @Override // java.lang.Runnable
            public final void run() {
                this.f$0.lambda$new$0$CronetUrlRequestContext();
            }
        });
        if (cronetEngineBuilderImpl.httpCacheMode() == 1) {
            this.mInUseStoragePath = cronetEngineBuilderImpl.storagePath();
            synchronized (sInUseStoragePaths) {
                if (!sInUseStoragePaths.add(this.mInUseStoragePath)) {
                    throw new IllegalStateException("Disk cache storage path already in use");
                }
            }
        } else {
            this.mInUseStoragePath = null;
        }
        synchronized (this.mLock) {
            this.mUrlRequestContextAdapter = ((Long) SafeNativeFunctionCaller.EnsureResult(new SafeNativeFunctionCaller.Supplier() { // from class: aegon.chrome.net.impl.-$$Lambda$CronetUrlRequestContext$N4iNWP6h1FSiKJKCST6yCm6l3zs
                @Override // aegon.chrome.net.impl.SafeNativeFunctionCaller.Supplier
                public final Object get() {
                    return Long.valueOf(CronetUrlRequestContextJni.get().createRequestContextAdapter(CronetUrlRequestContext.createNativeUrlRequestContextConfig(cronetEngineBuilderImpl)));
                }
            })).longValue();
            if (this.mUrlRequestContextAdapter == 0) {
                throw new NullPointerException("Context Adapter creation failed.");
            }
        }
        CronetLibraryLoader.postToInitThread(new RunnableC01011());
    }

    public /* synthetic */ void lambda$new$0$CronetUrlRequestContext() {
        CronetUrlRequestContextJni.get().setMinLogLevel(getLoggingLevel());
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    /* renamed from: aegon.chrome.net.impl.CronetUrlRequestContext$1 */
    class RunnableC01011 implements Runnable {
        RunnableC01011() {
        }

        @Override // java.lang.Runnable
        public void run() {
            CronetLibraryLoader.ensureInitializedOnInitThread();
            SafeNativeFunctionCaller.Ensure(new Runnable() { // from class: aegon.chrome.net.impl.-$$Lambda$CronetUrlRequestContext$1$TFfT8pLZ6Upi1E0tIKO-PSBZhWs
                @Override // java.lang.Runnable
                public final void run() {
                    this.f$0.lambda$run$0$CronetUrlRequestContext$1();
                }
            });
        }

        public /* synthetic */ void lambda$run$0$CronetUrlRequestContext$1() {
            synchronized (CronetUrlRequestContext.this.mLock) {
                CronetUrlRequestContextJni.get().initRequestContextOnInitThread(CronetUrlRequestContext.this.mUrlRequestContextAdapter, CronetUrlRequestContext.this);
            }
        }
    }

    public static long createNativeUrlRequestContextConfig(CronetEngineBuilderImpl cronetEngineBuilderImpl) {
        long jCreateRequestContextConfig = CronetUrlRequestContextJni.get().createRequestContextConfig(cronetEngineBuilderImpl.getUserAgent(), cronetEngineBuilderImpl.storagePath(), cronetEngineBuilderImpl.quicEnabled(), cronetEngineBuilderImpl.getDefaultQuicUserAgentId(), cronetEngineBuilderImpl.http2Enabled(), cronetEngineBuilderImpl.brotliEnabled(), cronetEngineBuilderImpl.cacheDisabled(), cronetEngineBuilderImpl.httpCacheMode(), cronetEngineBuilderImpl.httpCacheMaxSize(), cronetEngineBuilderImpl.experimentalOptions(), cronetEngineBuilderImpl.mockCertVerifier(), cronetEngineBuilderImpl.networkQualityEstimatorEnabled(), cronetEngineBuilderImpl.publicKeyPinningBypassForLocalTrustAnchorsEnabled(), cronetEngineBuilderImpl.threadPriority(10));
        for (CronetEngineBuilderImpl.QuicHint quicHint : cronetEngineBuilderImpl.quicHints()) {
            CronetUrlRequestContextJni.get().addQuicHint(jCreateRequestContextConfig, quicHint.mHost, quicHint.mPort, quicHint.mAlternatePort);
        }
        for (CronetEngineBuilderImpl.Pkp pkp : cronetEngineBuilderImpl.publicKeyPins()) {
            CronetUrlRequestContextJni.get().addPkp(jCreateRequestContextConfig, pkp.mHost, pkp.mHashes, pkp.mIncludeSubdomains, pkp.mExpirationDate.getTime());
        }
        return jCreateRequestContextConfig;
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public ExperimentalBidirectionalStream.Builder newBidirectionalStreamBuilder(String str, BidirectionalStream.Callback callback, Executor executor) {
        return new BidirectionalStreamBuilderImpl(str, callback, executor, this);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBase
    public UrlRequestBase createRequest(String str, UrlRequest.Callback callback, Executor executor, int i, Collection<Object> collection, boolean z, boolean z2, boolean z3, boolean z4, int i2, boolean z5, int i3, RequestFinishedInfo.Listener listener, int i4) throws Throwable {
        synchronized (this.mLock) {
            try {
            } catch (Throwable th) {
                th = th;
            }
            try {
                checkHaveAdapter();
                return new CronetUrlRequest(this, str, i, callback, executor, collection, z, z2, z3, z4, i2, z5, i3, listener, i4);
            } catch (Throwable th2) {
                th = th2;
                throw th;
            }
        }
    }

    @Override // aegon.chrome.net.impl.CronetEngineBase
    protected ExperimentalBidirectionalStream createBidirectionalStream(String str, BidirectionalStream.Callback callback, Executor executor, String str2, List<Map.Entry<String, String>> list, int i, boolean z, Collection<Object> collection, boolean z2, int i2, boolean z3, int i3) throws Throwable {
        synchronized (this.mLock) {
            try {
                try {
                    checkHaveAdapter();
                    return new CronetBidirectionalStream(this, str, i, callback, executor, str2, list, z, collection, z2, i2, z3, i3);
                } catch (Throwable th) {
                    th = th;
                    throw th;
                }
            } catch (Throwable th2) {
                th = th2;
                throw th;
            }
        }
    }

    @Override // aegon.chrome.net.CronetEngine
    public String getVersionString() {
        return "Cronet/" + ImplVersion.getCronetVersionWithLastChange();
    }

    @Override // aegon.chrome.net.CronetEngine
    public void shutdown() {
        if (this.mInUseStoragePath != null) {
            synchronized (sInUseStoragePaths) {
                sInUseStoragePaths.remove(this.mInUseStoragePath);
            }
        }
        synchronized (this.mLock) {
            checkHaveAdapter();
            if (this.mActiveRequestCount.get() != 0) {
                throw new IllegalStateException("Cannot shutdown with active requests.");
            }
            if (Thread.currentThread() == this.mNetworkThread) {
                throw new IllegalThreadStateException("Cannot shutdown from network thread.");
            }
        }
        this.mInitCompleted.block();
        stopNetLog();
        synchronized (this.mLock) {
            if (haveRequestContextAdapter()) {
                CronetUrlRequestContextJni.get().destroy(this.mUrlRequestContextAdapter, this);
                this.mUrlRequestContextAdapter = 0L;
            }
        }
    }

    @Override // aegon.chrome.net.CronetEngine
    public void startNetLogToFile(String str, boolean z) {
        synchronized (this.mLock) {
            checkHaveAdapter();
            if (!CronetUrlRequestContextJni.get().startNetLogToFile(this.mUrlRequestContextAdapter, this, str, z)) {
                throw new RuntimeException("Unable to start NetLog");
            }
            this.mIsLogging = true;
        }
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public void startNetLogToDisk(String str, boolean z, int i) {
        synchronized (this.mLock) {
            checkHaveAdapter();
            CronetUrlRequestContextJni.get().startNetLogToDisk(this.mUrlRequestContextAdapter, this, str, z, i);
            this.mIsLogging = true;
        }
    }

    @Override // aegon.chrome.net.CronetEngine
    public void stopNetLog() {
        synchronized (this.mLock) {
            if (this.mIsLogging) {
                checkHaveAdapter();
                this.mStopNetLogCompleted = new ConditionVariable();
                CronetUrlRequestContextJni.get().stopNetLog(this.mUrlRequestContextAdapter, this);
                this.mIsLogging = false;
                this.mStopNetLogCompleted.block();
            }
        }
    }

    public void stopNetLogCompleted() {
        this.mStopNetLogCompleted.open();
    }

    @Override // aegon.chrome.net.CronetEngine
    public byte[] getGlobalMetricsDeltas() {
        return CronetUrlRequestContextJni.get().getHistogramDeltas();
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public int getEffectiveConnectionType() {
        int iConvertConnectionTypeToApiValue;
        if (!this.mNetworkQualityEstimatorEnabled) {
            throw new IllegalStateException("Network quality estimator must be enabled");
        }
        synchronized (this.mNetworkQualityLock) {
            iConvertConnectionTypeToApiValue = convertConnectionTypeToApiValue(this.mEffectiveConnectionType);
        }
        return iConvertConnectionTypeToApiValue;
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public int getHttpRttMs() {
        int i;
        if (!this.mNetworkQualityEstimatorEnabled) {
            throw new IllegalStateException("Network quality estimator must be enabled");
        }
        synchronized (this.mNetworkQualityLock) {
            i = this.mHttpRttMs != -1 ? this.mHttpRttMs : -1;
        }
        return i;
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public int getTransportRttMs() {
        int i;
        if (!this.mNetworkQualityEstimatorEnabled) {
            throw new IllegalStateException("Network quality estimator must be enabled");
        }
        synchronized (this.mNetworkQualityLock) {
            i = this.mTransportRttMs != -1 ? this.mTransportRttMs : -1;
        }
        return i;
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public int getDownstreamThroughputKbps() {
        int i;
        if (!this.mNetworkQualityEstimatorEnabled) {
            throw new IllegalStateException("Network quality estimator must be enabled");
        }
        synchronized (this.mNetworkQualityLock) {
            i = this.mDownstreamThroughputKbps != -1 ? this.mDownstreamThroughputKbps : -1;
        }
        return i;
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public void configureNetworkQualityEstimatorForTesting(boolean z, boolean z2, boolean z3) {
        if (!this.mNetworkQualityEstimatorEnabled) {
            throw new IllegalStateException("Network quality estimator must be enabled");
        }
        synchronized (this.mLock) {
            checkHaveAdapter();
            CronetUrlRequestContextJni.get().configureNetworkQualityEstimatorForTesting(this.mUrlRequestContextAdapter, this, z, z2, z3);
        }
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public void addRttListener(NetworkQualityRttListener networkQualityRttListener) {
        if (!this.mNetworkQualityEstimatorEnabled) {
            throw new IllegalStateException("Network quality estimator must be enabled");
        }
        synchronized (this.mNetworkQualityLock) {
            if (this.mRttListenerList.isEmpty()) {
                synchronized (this.mLock) {
                    checkHaveAdapter();
                    CronetUrlRequestContextJni.get().provideRTTObservations(this.mUrlRequestContextAdapter, this, true);
                }
            }
            this.mRttListenerList.addObserver(new VersionSafeCallbacks.NetworkQualityRttListenerWrapper(networkQualityRttListener));
        }
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public void removeRttListener(NetworkQualityRttListener networkQualityRttListener) {
        if (!this.mNetworkQualityEstimatorEnabled) {
            throw new IllegalStateException("Network quality estimator must be enabled");
        }
        synchronized (this.mNetworkQualityLock) {
            if (this.mRttListenerList.removeObserver(new VersionSafeCallbacks.NetworkQualityRttListenerWrapper(networkQualityRttListener)) && this.mRttListenerList.isEmpty()) {
                synchronized (this.mLock) {
                    checkHaveAdapter();
                    CronetUrlRequestContextJni.get().provideRTTObservations(this.mUrlRequestContextAdapter, this, false);
                }
            }
        }
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public void addThroughputListener(NetworkQualityThroughputListener networkQualityThroughputListener) {
        if (!this.mNetworkQualityEstimatorEnabled) {
            throw new IllegalStateException("Network quality estimator must be enabled");
        }
        synchronized (this.mNetworkQualityLock) {
            if (this.mThroughputListenerList.isEmpty()) {
                synchronized (this.mLock) {
                    checkHaveAdapter();
                    CronetUrlRequestContextJni.get().provideThroughputObservations(this.mUrlRequestContextAdapter, this, true);
                }
            }
            this.mThroughputListenerList.addObserver(new VersionSafeCallbacks.NetworkQualityThroughputListenerWrapper(networkQualityThroughputListener));
        }
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public void removeThroughputListener(NetworkQualityThroughputListener networkQualityThroughputListener) {
        if (!this.mNetworkQualityEstimatorEnabled) {
            throw new IllegalStateException("Network quality estimator must be enabled");
        }
        synchronized (this.mNetworkQualityLock) {
            if (this.mThroughputListenerList.removeObserver(new VersionSafeCallbacks.NetworkQualityThroughputListenerWrapper(networkQualityThroughputListener)) && this.mThroughputListenerList.isEmpty()) {
                synchronized (this.mLock) {
                    checkHaveAdapter();
                    CronetUrlRequestContextJni.get().provideThroughputObservations(this.mUrlRequestContextAdapter, this, false);
                }
            }
        }
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public void addRequestFinishedListener(RequestFinishedInfo.Listener listener) {
        synchronized (this.mFinishedListenerLock) {
            this.mFinishedListenerMap.put(listener, new VersionSafeCallbacks.RequestFinishedInfoListener(listener));
        }
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public void removeRequestFinishedListener(RequestFinishedInfo.Listener listener) {
        synchronized (this.mFinishedListenerLock) {
            this.mFinishedListenerMap.remove(listener);
        }
    }

    boolean hasRequestFinishedListener() {
        boolean z;
        synchronized (this.mFinishedListenerLock) {
            z = !this.mFinishedListenerMap.isEmpty();
        }
        return z;
    }

    @Override // aegon.chrome.net.CronetEngine
    public URLConnection openConnection(URL url) {
        return openConnection(url, Proxy.NO_PROXY);
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public URLConnection openConnection(URL url, Proxy proxy) {
        if (proxy.type() != Proxy.Type.DIRECT) {
            throw new UnsupportedOperationException();
        }
        String protocol = url.getProtocol();
        if (UriUtil.HTTP_SCHEME.equals(protocol) || UriUtil.HTTPS_SCHEME.equals(protocol)) {
            return new CronetHttpURLConnection(url, this);
        }
        throw new UnsupportedOperationException("Unexpected protocol:" + protocol);
    }

    @Override // aegon.chrome.net.CronetEngine
    public URLStreamHandlerFactory createURLStreamHandlerFactory() {
        return new CronetURLStreamHandlerFactory(this);
    }

    void onRequestStarted() {
        this.mActiveRequestCount.incrementAndGet();
    }

    void onRequestDestroyed() {
        this.mActiveRequestCount.decrementAndGet();
    }

    public long getUrlRequestContextAdapter() {
        long j;
        synchronized (this.mLock) {
            checkHaveAdapter();
            j = this.mUrlRequestContextAdapter;
        }
        return j;
    }

    private void checkHaveAdapter() {
        if (!haveRequestContextAdapter()) {
            throw new IllegalStateException("Engine is shut down.");
        }
    }

    private boolean haveRequestContextAdapter() {
        return this.mUrlRequestContextAdapter != 0;
    }

    private int getLoggingLevel() {
        if (Log.isLoggable(LOG_TAG, 2)) {
            return -2;
        }
        return Log.isLoggable(LOG_TAG, 3) ? -1 : 3;
    }

    private static int convertConnectionTypeToApiValue(int i) {
        if (i == 0) {
            return 0;
        }
        int i2 = 1;
        if (i != 1) {
            i2 = 2;
            if (i != 2) {
                i2 = 3;
                if (i != 3) {
                    i2 = 4;
                    if (i != 4) {
                        if (i == 5) {
                            return 5;
                        }
                        throw new RuntimeException("Internal Error: Illegal EffectiveConnectionType value " + i);
                    }
                }
            }
        }
        return i2;
    }

    private void initNetworkThread() {
        this.mNetworkThread = Thread.currentThread();
        this.mInitCompleted.open();
        Thread.currentThread().setName("ChromiumNet");
    }

    private void onEffectiveConnectionTypeChanged(int i) {
        synchronized (this.mNetworkQualityLock) {
            this.mEffectiveConnectionType = i;
        }
    }

    private void onRTTOrThroughputEstimatesComputed(int i, int i2, int i3) {
        synchronized (this.mNetworkQualityLock) {
            this.mHttpRttMs = i;
            this.mTransportRttMs = i2;
            this.mDownstreamThroughputKbps = i3;
        }
    }

    private void onRttObservation(final int i, final long j, final int i2) {
        synchronized (this.mNetworkQualityLock) {
            Iterator<VersionSafeCallbacks.NetworkQualityRttListenerWrapper> itIterator2 = this.mRttListenerList.iterator2();
            while (itIterator2.hasNext()) {
                final VersionSafeCallbacks.NetworkQualityRttListenerWrapper networkQualityRttListenerWrapperMo35924next = itIterator2.mo35924next();
                postObservationTaskToExecutor(networkQualityRttListenerWrapperMo35924next.getExecutor(), new Runnable() { // from class: aegon.chrome.net.impl.CronetUrlRequestContext.2
                    @Override // java.lang.Runnable
                    public void run() {
                        networkQualityRttListenerWrapperMo35924next.onRttObservation(i, j, i2);
                    }
                });
            }
        }
    }

    private void onThroughputObservation(final int i, final long j, final int i2) {
        synchronized (this.mNetworkQualityLock) {
            Iterator<VersionSafeCallbacks.NetworkQualityThroughputListenerWrapper> itIterator2 = this.mThroughputListenerList.iterator2();
            while (itIterator2.hasNext()) {
                final VersionSafeCallbacks.NetworkQualityThroughputListenerWrapper networkQualityThroughputListenerWrapperMo35924next = itIterator2.mo35924next();
                postObservationTaskToExecutor(networkQualityThroughputListenerWrapperMo35924next.getExecutor(), new Runnable() { // from class: aegon.chrome.net.impl.CronetUrlRequestContext.3
                    @Override // java.lang.Runnable
                    public void run() {
                        networkQualityThroughputListenerWrapperMo35924next.onThroughputObservation(i, j, i2);
                    }
                });
            }
        }
    }

    void reportRequestFinished(final RequestFinishedInfo requestFinishedInfo) {
        synchronized (this.mFinishedListenerLock) {
            if (this.mFinishedListenerMap.isEmpty()) {
                return;
            }
            Iterator itIterator2 = new ArrayList(this.mFinishedListenerMap.values()).iterator2();
            while (itIterator2.hasNext()) {
                final VersionSafeCallbacks.RequestFinishedInfoListener requestFinishedInfoListener = (VersionSafeCallbacks.RequestFinishedInfoListener) itIterator2.mo35924next();
                postObservationTaskToExecutor(requestFinishedInfoListener.getExecutor(), new Runnable() { // from class: aegon.chrome.net.impl.CronetUrlRequestContext.4
                    @Override // java.lang.Runnable
                    public void run() {
                        requestFinishedInfoListener.onRequestFinished(requestFinishedInfo);
                    }
                });
            }
        }
    }

    private static void postObservationTaskToExecutor(Executor executor, Runnable runnable) {
        try {
            executor.execute(runnable);
        } catch (RejectedExecutionException e) {
            Log.m43e(LOG_TAG, "Exception posting task to executor", e);
        }
    }

    public boolean isNetworkThread(Thread thread) {
        return thread == this.mNetworkThread;
    }
}
