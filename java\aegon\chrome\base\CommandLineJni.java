package aegon.chrome.base;

import aegon.chrome.base.CommandLine;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class CommandLineJni implements CommandLine.Natives {
    public static final JniStaticTestMocker<CommandLine.Natives> TEST_HOOKS = new JniStaticTestMocker<CommandLine.Natives>() { // from class: aegon.chrome.base.CommandLineJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(CommandLine.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                CommandLine.Natives unused = CommandLineJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static CommandLine.Natives testInstance;

    CommandLineJni() {
    }

    @Override // aegon.chrome.base.CommandLine.Natives
    public void init(String[] strArr) {
        GEN_JNI.org_chromium_base_CommandLine_init(strArr);
    }

    @Override // aegon.chrome.base.CommandLine.Natives
    public boolean hasSwitch(String str) {
        return GEN_JNI.org_chromium_base_CommandLine_hasSwitch(str);
    }

    @Override // aegon.chrome.base.CommandLine.Natives
    public String getSwitchValue(String str) {
        return GEN_JNI.org_chromium_base_CommandLine_getSwitchValue(str);
    }

    @Override // aegon.chrome.base.CommandLine.Natives
    public String[] getSwitchesFlattened() {
        return GEN_JNI.org_chromium_base_CommandLine_getSwitchesFlattened();
    }

    @Override // aegon.chrome.base.CommandLine.Natives
    public void appendSwitch(String str) {
        GEN_JNI.org_chromium_base_CommandLine_appendSwitch(str);
    }

    @Override // aegon.chrome.base.CommandLine.Natives
    public void appendSwitchWithValue(String str, String str2) {
        GEN_JNI.org_chromium_base_CommandLine_appendSwitchWithValue(str, str2);
    }

    @Override // aegon.chrome.base.CommandLine.Natives
    public void appendSwitchesAndArguments(String[] strArr) {
        GEN_JNI.org_chromium_base_CommandLine_appendSwitchesAndArguments(strArr);
    }

    @Override // aegon.chrome.base.CommandLine.Natives
    public void removeSwitch(String str) {
        GEN_JNI.org_chromium_base_CommandLine_removeSwitch(str);
    }

    public static CommandLine.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            CommandLine.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.CommandLine.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new CommandLineJni();
    }
}
