package aegon.chrome.base;

import aegon.chrome.base.CpuFeatures;
import aegon.chrome.base.natives.GEN_JNI;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class CpuFeaturesJni implements CpuFeatures.Natives {
    public static final JniStaticTestMocker<CpuFeatures.Natives> TEST_HOOKS = new JniStaticTestMocker<CpuFeatures.Natives>() { // from class: aegon.chrome.base.CpuFeaturesJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(CpuFeatures.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                CpuFeatures.Natives unused = CpuFeaturesJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static CpuFeatures.Natives testInstance;

    CpuFeaturesJni() {
    }

    @Override // aegon.chrome.base.CpuFeatures.Natives
    public int getCoreCount() {
        return GEN_JNI.org_chromium_base_CpuFeatures_getCoreCount();
    }

    @Override // aegon.chrome.base.CpuFeatures.Natives
    public long getCpuFeatures() {
        return GEN_JNI.org_chromium_base_CpuFeatures_getCpuFeatures();
    }

    public static CpuFeatures.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            CpuFeatures.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.CpuFeatures.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new CpuFeaturesJni();
    }
}
