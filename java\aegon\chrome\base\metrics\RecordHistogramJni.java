package aegon.chrome.base.metrics;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.metrics.RecordHistogram;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class RecordHistogramJni implements RecordHistogram.Natives {
    public static final JniStaticTestMocker<RecordHistogram.Natives> TEST_HOOKS = new JniStaticTestMocker<RecordHistogram.Natives>() { // from class: aegon.chrome.base.metrics.RecordHistogramJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(RecordHistogram.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                RecordHistogram.Natives unused = RecordHistogramJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static RecordHistogram.Natives testInstance;

    @Override // aegon.chrome.base.metrics.RecordHistogram.Natives
    public int getHistogramValueCountForTesting(String str, int i) {
        return GEN_JNI.m54x9f662db(str, i);
    }

    @Override // aegon.chrome.base.metrics.RecordHistogram.Natives
    public int getHistogramTotalCountForTesting(String str) {
        return GEN_JNI.m53x7c4ad8a8(str);
    }

    @Override // aegon.chrome.base.metrics.RecordHistogram.Natives
    public void forgetHistogramForTesting(String str) {
        GEN_JNI.m52xa86ffe16(str);
    }

    public static RecordHistogram.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            RecordHistogram.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.metrics.RecordHistogram.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new RecordHistogramJni();
    }
}
