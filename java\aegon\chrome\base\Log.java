package aegon.chrome.base;

import java.util.Locale;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class Log {
    public static final int ASSERT = 7;
    public static final int DEBUG = 3;
    public static final int ERROR = 6;
    public static final int INFO = 4;
    public static final int VERBOSE = 2;
    public static final int WARN = 5;
    private static final String sDeprecatedTagPrefix = "cr.";
    private static final String sTagPrefix = "cr_";

    private static boolean isDebug() {
        return true;
    }

    private Log() {
    }

    private static String formatLog(String str, Throwable th, Object... objArr) {
        return objArr != null ? ((th != null || objArr.length <= 0) && objArr.length <= 1) ? str : String.format(Locale.f24101US, str, objArr) : str;
    }

    public static String normalizeTag(String str) {
        if (str.startsWith(sTagPrefix)) {
            return str;
        }
        return sTagPrefix + str.substring(str.startsWith(sDeprecatedTagPrefix) ? 3 : 0, str.length());
    }

    private static String formatLogWithStack(String str, Throwable th, Object... objArr) {
        return "[" + getCallOrigin() + "] " + formatLog(str, th, objArr);
    }

    public static boolean isLoggable(String str, int i) {
        if (isDebug() || i > 4) {
            return android.util.Log.isLoggable(str, i);
        }
        return false;
    }

    /* renamed from: v */
    public static void m45v(String str, String str2, Object... objArr) {
        if (isDebug()) {
            Throwable throwableToLog = getThrowableToLog(objArr);
            String logWithStack = formatLogWithStack(str2, throwableToLog, objArr);
            if (throwableToLog != null) {
                android.util.Log.v(normalizeTag(str), logWithStack, throwableToLog);
            } else {
                android.util.Log.v(normalizeTag(str), logWithStack);
            }
        }
    }

    /* renamed from: d */
    public static void m42d(String str, String str2, Object... objArr) {
        if (isDebug()) {
            Throwable throwableToLog = getThrowableToLog(objArr);
            String logWithStack = formatLogWithStack(str2, throwableToLog, objArr);
            if (throwableToLog != null) {
                android.util.Log.d(normalizeTag(str), logWithStack, throwableToLog);
            } else {
                android.util.Log.d(normalizeTag(str), logWithStack);
            }
        }
    }

    /* renamed from: i */
    public static void m44i(String str, String str2, Object... objArr) {
        Throwable throwableToLog = getThrowableToLog(objArr);
        String log = formatLog(str2, throwableToLog, objArr);
        if (throwableToLog != null) {
            android.util.Log.i(normalizeTag(str), log, throwableToLog);
        } else {
            android.util.Log.i(normalizeTag(str), log);
        }
    }

    /* renamed from: w */
    public static void m46w(String str, String str2, Object... objArr) {
        Throwable throwableToLog = getThrowableToLog(objArr);
        String log = formatLog(str2, throwableToLog, objArr);
        if (throwableToLog != null) {
            android.util.Log.w(normalizeTag(str), log, throwableToLog);
        } else {
            android.util.Log.w(normalizeTag(str), log);
        }
    }

    /* renamed from: e */
    public static void m43e(String str, String str2, Object... objArr) {
        Throwable throwableToLog = getThrowableToLog(objArr);
        String log = formatLog(str2, throwableToLog, objArr);
        if (throwableToLog != null) {
            android.util.Log.e(normalizeTag(str), log, throwableToLog);
        } else {
            android.util.Log.e(normalizeTag(str), log);
        }
    }

    public static void wtf(String str, String str2, Object... objArr) {
        Throwable throwableToLog = getThrowableToLog(objArr);
        String log = formatLog(str2, throwableToLog, objArr);
        if (throwableToLog != null) {
            android.util.Log.wtf(normalizeTag(str), log, throwableToLog);
        } else {
            android.util.Log.wtf(normalizeTag(str), log);
        }
    }

    public static String getStackTraceString(Throwable th) {
        return android.util.Log.getStackTraceString(th);
    }

    private static Throwable getThrowableToLog(Object[] objArr) {
        if (objArr == null || objArr.length == 0) {
            return null;
        }
        Object obj = objArr[objArr.length - 1];
        if (obj instanceof Throwable) {
            return (Throwable) obj;
        }
        return null;
    }

    private static String getCallOrigin() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        String name = Log.class.getName();
        int i = 0;
        while (true) {
            if (i >= stackTrace.length) {
                break;
            }
            if (stackTrace[i].getClassName().equals(name)) {
                i += 3;
                break;
            }
            i++;
        }
        return stackTrace[i].getFileName() + ":" + stackTrace[i].getLineNumber();
    }
}
