package aegon.chrome.base;

import java.p654io.FileInputStream;
import java.p654io.IOException;
import java.security.SecureRandom;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
public class SecureRandomInitializer {
    private static final int NUM_RANDOM_BYTES = 16;

    public static void initialize(SecureRandom secureRandom) throws IOException {
        FileInputStream fileInputStream = new FileInputStream("/dev/urandom");
        try {
            byte[] bArr = new byte[16];
            if (fileInputStream.read(bArr) != 16) {
                throw new IOException("Failed to get enough random data.");
            }
            secureRandom.setSeed(bArr);
            fileInputStream.lambda$new$0();
        } catch (Throwable th) {
            try {
                throw th;
            } catch (Throwable th2) {
                try {
                    fileInputStream.lambda$new$0();
                } catch (Throwable unused) {
                }
                throw th2;
            }
        }
    }
}
