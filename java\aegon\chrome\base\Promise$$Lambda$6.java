package aegon.chrome.base;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class Promise$$Lambda$6 implements Runnable {
    private final Callback arg$1;
    private final Object arg$2;

    private Promise$$Lambda$6(Callback callback, Object obj) {
        this.arg$1 = callback;
        this.arg$2 = obj;
    }

    public static Runnable lambdaFactory$(Callback callback, Object obj) {
        return new Promise$$Lambda$6(callback, obj);
    }

    @Override // java.lang.Runnable
    public final void run() {
        Promise.lambda$postCallbackToLooper$3(this.arg$1, this.arg$2);
    }
}
