package aegon.chrome.net.impl;

import aegon.chrome.net.impl.CronetUrlRequestContext;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class CronetUrlRequestContext$1$$Lambda$1 implements Runnable {
    private final CronetUrlRequestContext.RunnableC01011 arg$1;

    private CronetUrlRequestContext$1$$Lambda$1(CronetUrlRequestContext.RunnableC01011 runnableC01011) {
        this.arg$1 = runnableC01011;
    }

    public static Runnable lambdaFactory$(CronetUrlRequestContext.RunnableC01011 runnableC01011) {
        return new CronetUrlRequestContext$1$$Lambda$1(runnableC01011);
    }

    @Override // java.lang.Runnable
    public final void run() {
        CronetUrlRequestContext.RunnableC01011.lambda$run$0(this.arg$1);
    }
}
