package aegon.chrome.base.metrics;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class NoopUmaRecorder implements UmaRecorder {
    @Override // aegon.chrome.base.metrics.UmaRecorder
    public void recordBooleanHistogram(String str, boolean z) {
    }

    @Override // aegon.chrome.base.metrics.UmaRecorder
    public void recordExponentialHistogram(String str, int i, int i2, int i3, int i4) {
    }

    @Override // aegon.chrome.base.metrics.UmaRecorder
    public void recordLinearHistogram(String str, int i, int i2, int i3, int i4) {
    }

    @Override // aegon.chrome.base.metrics.UmaRecorder
    public void recordSparseHistogram(String str, int i) {
    }

    @Override // aegon.chrome.base.metrics.UmaRecorder
    public void recordUserAction(String str, long j) {
    }

    NoopUmaRecorder() {
    }
}
