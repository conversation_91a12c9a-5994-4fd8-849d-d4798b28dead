package android.icu.impl.duration;

import libcore.icu.RelativeDateTimeFormatter;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class TimeUnit {
    public static final TimeUnit MILLISECOND;
    static final long[] approxDurations;
    static final TimeUnit[] units;
    final String name;
    final byte ordinal;
    public static final TimeUnit YEAR = new TimeUnit("year", 0);
    public static final TimeUnit MONTH = new TimeUnit("month", 1);
    public static final TimeUnit WEEK = new TimeUnit("week", 2);
    public static final TimeUnit DAY = new TimeUnit("day", 3);
    public static final TimeUnit HOUR = new TimeUnit("hour", 4);
    public static final TimeUnit MINUTE = new TimeUnit("minute", 5);
    public static final TimeUnit SECOND = new TimeUnit("second", 6);

    private TimeUnit(String name, int ordinal) {
        this.name = name;
        this.ordinal = (byte) ordinal;
    }

    public String toString() {
        return this.name;
    }

    static {
        TimeUnit timeUnit = new TimeUnit("millisecond", 7);
        MILLISECOND = timeUnit;
        units = new TimeUnit[]{YEAR, MONTH, WEEK, DAY, HOUR, MINUTE, SECOND, timeUnit};
        approxDurations = new long[]{31557600000L, 2630880000L, RelativeDateTimeFormatter.WEEK_IN_MILLIS, 86400000, RelativeDateTimeFormatter.HOUR_IN_MILLIS, 60000, 1000, 1};
    }

    public TimeUnit larger() {
        byte b2 = this.ordinal;
        if (b2 == 0) {
            return null;
        }
        return units[b2 - 1];
    }

    public TimeUnit smaller() {
        byte b2 = this.ordinal;
        TimeUnit[] timeUnitArr = units;
        if (b2 == timeUnitArr.length - 1) {
            return null;
        }
        return timeUnitArr[b2 + 1];
    }

    public int ordinal() {
        return this.ordinal;
    }
}
