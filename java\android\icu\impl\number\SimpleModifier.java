package android.icu.impl.number;

import android.icu.impl.FormattedStringBuilder;
import android.icu.impl.SimpleFormatterImpl;
import android.icu.impl.number.Modifier;
import android.icu.impl.number.range.PrefixInfixSuffixLengthHelper;
import android.icu.util.ICUException;
import java.text.Format;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class SimpleModifier implements Modifier {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final int ARG_NUM_LIMIT = 256;
    private final String compiledPattern;
    private final Format.Field field;
    private final Modifier.Parameters parameters;
    private final int prefixLength;
    private final boolean strong;
    private final int suffixLength;
    private final int suffixOffset;

    public SimpleModifier(String compiledPattern, Format.Field field, boolean strong) {
        this(compiledPattern, field, strong, null);
    }

    public SimpleModifier(String compiledPattern, Format.Field field, boolean strong, Modifier.Parameters parameters) {
        this.compiledPattern = compiledPattern;
        this.field = field;
        this.strong = strong;
        this.parameters = parameters;
        int argLimit = SimpleFormatterImpl.getArgumentLimit(compiledPattern);
        if (argLimit == 0) {
            this.prefixLength = compiledPattern.charAt(1) - 256;
            this.suffixOffset = -1;
            this.suffixLength = 0;
            return;
        }
        if (compiledPattern.charAt(1) != 0) {
            int iCharAt = compiledPattern.charAt(1) - 256;
            this.prefixLength = iCharAt;
            this.suffixOffset = iCharAt + 3;
        } else {
            this.prefixLength = 0;
            this.suffixOffset = 2;
        }
        if (this.prefixLength + 3 < compiledPattern.length()) {
            this.suffixLength = compiledPattern.charAt(this.suffixOffset) - 256;
        } else {
            this.suffixLength = 0;
        }
    }

    @Override // android.icu.impl.number.Modifier
    public int apply(FormattedStringBuilder output, int leftIndex, int rightIndex) {
        return formatAsPrefixSuffix(output, leftIndex, rightIndex);
    }

    @Override // android.icu.impl.number.Modifier
    public int getPrefixLength() {
        return this.prefixLength;
    }

    @Override // android.icu.impl.number.Modifier
    public int getCodePointCount() {
        int i = this.prefixLength;
        int count = i > 0 ? 0 + Character.codePointCount(this.compiledPattern, 2, i + 2) : 0;
        int i2 = this.suffixLength;
        if (i2 > 0) {
            String str = this.compiledPattern;
            int i3 = this.suffixOffset;
            return count + Character.codePointCount(str, i3 + 1, i3 + 1 + i2);
        }
        return count;
    }

    @Override // android.icu.impl.number.Modifier
    public boolean isStrong() {
        return this.strong;
    }

    @Override // android.icu.impl.number.Modifier
    public boolean containsField(Format.Field field) {
        return false;
    }

    @Override // android.icu.impl.number.Modifier
    public Modifier.Parameters getParameters() {
        return this.parameters;
    }

    @Override // android.icu.impl.number.Modifier
    public boolean semanticallyEquivalent(Modifier other) {
        if (!(other instanceof SimpleModifier)) {
            return false;
        }
        SimpleModifier _other = (SimpleModifier) other;
        Modifier.Parameters parameters = this.parameters;
        if (parameters == null || _other.parameters == null || parameters.obj != _other.parameters.obj) {
            return this.compiledPattern.equals(_other.compiledPattern) && this.field == _other.field && this.strong == _other.strong;
        }
        return true;
    }

    public int formatAsPrefixSuffix(FormattedStringBuilder result, int startIndex, int endIndex) {
        if (this.suffixOffset == -1) {
            return result.splice(startIndex, endIndex, this.compiledPattern, 2, this.prefixLength + 2, this.field);
        }
        int i = this.prefixLength;
        if (i > 0) {
            result.insert(startIndex, this.compiledPattern, 2, i + 2, this.field);
        }
        int i2 = this.suffixLength;
        if (i2 > 0) {
            int i3 = endIndex + this.prefixLength;
            String str = this.compiledPattern;
            int i4 = this.suffixOffset;
            result.insert(i3, str, i4 + 1, i4 + 1 + i2, this.field);
        }
        return this.prefixLength + this.suffixLength;
    }

    public static void formatTwoArgPattern(String compiledPattern, FormattedStringBuilder result, int index, PrefixInfixSuffixLengthHelper h, Format.Field field) {
        int prefixLength;
        int infixLength;
        int suffixLength;
        int argLimit = SimpleFormatterImpl.getArgumentLimit(compiledPattern);
        if (argLimit != 2) {
            throw new ICUException();
        }
        int length = 0;
        int prefixLength2 = compiledPattern.charAt(1);
        int offset = 1 + 1;
        if (prefixLength2 < 256) {
            prefixLength = 0;
        } else {
            prefixLength = prefixLength2 - 256;
            result.insert(index + 0, compiledPattern, offset, offset + prefixLength, field);
            length = 0 + prefixLength;
            offset = offset + prefixLength + 1;
        }
        int infixLength2 = compiledPattern.charAt(offset);
        int offset2 = offset + 1;
        if (infixLength2 < 256) {
            infixLength = 0;
        } else {
            infixLength = infixLength2 - 256;
            result.insert(index + length, compiledPattern, offset2, offset2 + infixLength, field);
            length += infixLength;
            offset2 = offset2 + infixLength + 1;
        }
        if (offset2 == compiledPattern.length()) {
            suffixLength = 0;
        } else {
            int suffixLength2 = compiledPattern.charAt(offset2) - 256;
            int offset3 = offset2 + 1;
            result.insert(index + length, compiledPattern, offset3, offset3 + suffixLength2, field);
            int i = length + suffixLength2;
            suffixLength = suffixLength2;
        }
        h.lengthPrefix = prefixLength;
        h.lengthInfix = infixLength;
        h.lengthSuffix = suffixLength;
    }
}
