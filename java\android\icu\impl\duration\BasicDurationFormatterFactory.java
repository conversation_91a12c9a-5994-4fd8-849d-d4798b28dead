package android.icu.impl.duration;

import java.util.Locale;
import java.util.TimeZone;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
class BasicDurationFormatterFactory implements DurationFormatterFactory {
    private PeriodBuilder builder;

    /* renamed from: f */
    private BasicDurationFormatter f61f;
    private DateFormatter fallback;
    private long fallbackLimit;
    private PeriodFormatter formatter;

    /* renamed from: ps */
    private BasicPeriodFormatterService f62ps;
    private String localeName = Locale.getDefault().toString();
    private TimeZone timeZone = TimeZone.getDefault();

    BasicDurationFormatterFactory(BasicPeriodFormatterService ps) {
        this.f62ps = ps;
    }

    @Override // android.icu.impl.duration.DurationFormatterFactory
    public DurationFormatterFactory setPeriodFormatter(PeriodFormatter formatter) {
        if (formatter != this.formatter) {
            this.formatter = formatter;
            reset();
        }
        return this;
    }

    @Override // android.icu.impl.duration.DurationFormatterFactory
    public DurationFormatterFactory setPeriodBuilder(PeriodBuilder builder) {
        if (builder != this.builder) {
            this.builder = builder;
            reset();
        }
        return this;
    }

    @Override // android.icu.impl.duration.DurationFormatterFactory
    public DurationFormatterFactory setFallback(DateFormatter fallback) {
        boolean doReset = true;
        if (fallback == null) {
            if (this.fallback == null) {
                doReset = false;
            }
        } else if (fallback.equals(this.fallback)) {
            doReset = false;
        }
        if (doReset) {
            this.fallback = fallback;
            reset();
        }
        return this;
    }

    @Override // android.icu.impl.duration.DurationFormatterFactory
    public DurationFormatterFactory setFallbackLimit(long fallbackLimit) {
        if (fallbackLimit < 0) {
            fallbackLimit = 0;
        }
        if (fallbackLimit != this.fallbackLimit) {
            this.fallbackLimit = fallbackLimit;
            reset();
        }
        return this;
    }

    @Override // android.icu.impl.duration.DurationFormatterFactory
    public DurationFormatterFactory setLocale(String localeName) {
        if (!localeName.equals(this.localeName)) {
            this.localeName = localeName;
            PeriodBuilder periodBuilder = this.builder;
            if (periodBuilder != null) {
                this.builder = periodBuilder.withLocale(localeName);
            }
            PeriodFormatter periodFormatter = this.formatter;
            if (periodFormatter != null) {
                this.formatter = periodFormatter.withLocale(localeName);
            }
            reset();
        }
        return this;
    }

    @Override // android.icu.impl.duration.DurationFormatterFactory
    public DurationFormatterFactory setTimeZone(TimeZone timeZone) {
        if (!timeZone.equals(this.timeZone)) {
            this.timeZone = timeZone;
            PeriodBuilder periodBuilder = this.builder;
            if (periodBuilder != null) {
                this.builder = periodBuilder.withTimeZone(timeZone);
            }
            reset();
        }
        return this;
    }

    @Override // android.icu.impl.duration.DurationFormatterFactory
    public DurationFormatter getFormatter() {
        if (this.f61f == null) {
            DateFormatter dateFormatter = this.fallback;
            if (dateFormatter != null) {
                this.fallback = dateFormatter.withLocale(this.localeName).withTimeZone(this.timeZone);
            }
            this.formatter = getPeriodFormatter();
            this.builder = getPeriodBuilder();
            this.f61f = createFormatter();
        }
        return this.f61f;
    }

    public PeriodFormatter getPeriodFormatter() {
        if (this.formatter == null) {
            this.formatter = this.f62ps.newPeriodFormatterFactory().setLocale(this.localeName).getFormatter();
        }
        return this.formatter;
    }

    public PeriodBuilder getPeriodBuilder() {
        if (this.builder == null) {
            this.builder = this.f62ps.newPeriodBuilderFactory().setLocale(this.localeName).setTimeZone(this.timeZone).getSingleUnitBuilder();
        }
        return this.builder;
    }

    public DateFormatter getFallback() {
        return this.fallback;
    }

    public long getFallbackLimit() {
        if (this.fallback == null) {
            return 0L;
        }
        return this.fallbackLimit;
    }

    public String getLocaleName() {
        return this.localeName;
    }

    public TimeZone getTimeZone() {
        return this.timeZone;
    }

    protected BasicDurationFormatter createFormatter() {
        return new BasicDurationFormatter(this.formatter, this.builder, this.fallback, this.fallbackLimit, this.localeName, this.timeZone);
    }

    protected void reset() {
        this.f61f = null;
    }
}
