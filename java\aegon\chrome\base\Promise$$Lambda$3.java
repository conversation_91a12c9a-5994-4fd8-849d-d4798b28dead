package aegon.chrome.base;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class Promise$$Lambda$3 implements Callback {
    private final Promise arg$1;

    private Promise$$Lambda$3(Promise promise) {
        this.arg$1 = promise;
    }

    public static Callback lambdaFactory$(Promise promise) {
        return new Promise$$Lambda$3(promise);
    }

    @Override // aegon.chrome.base.Callback
    public final void onResult(Object obj) {
        this.arg$1.reject((Exception) obj);
    }
}
