package android.icu.impl.number.parse;

import android.icu.impl.StringSegment;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public interface NumberParseMatcher {

    public interface Flexible {
    }

    boolean match(StringSegment stringSegment, ParsedNumber parsedNumber);

    void postProcess(ParsedNumber parsedNumber);

    boolean smokeTest(StringSegment stringSegment);
}
