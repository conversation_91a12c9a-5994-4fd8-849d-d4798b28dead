package aegon.chrome.base;

import aegon.chrome.base.compat.ApiHelperForO;
import android.app.Application;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.pm.ApplicationInfo;
import android.os.Build;
import androidx.collection.SimpleArrayMap;
import dalvik.system.BaseDexClassLoader;
import java.lang.reflect.Field;
import java.util.Arrays;
import org.apache.xpath.compiler.PsuedoNames;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class BundleUtils {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static Boolean sIsBundle;
    private static final Object sSplitLock = new Object();
    private static final SimpleArrayMap<String, ClassLoader> sCachedClassLoaders = new SimpleArrayMap<>();

    public static boolean isBundleForNative() {
        return isBundle();
    }

    public static boolean isBundle() {
        if (aegon.chrome.build.BuildConfig.BUNDLES_SUPPORTED) {
            return sIsBundle.booleanValue();
        }
        return false;
    }

    public static void setIsBundle(boolean z) {
        sIsBundle = Boolean.valueOf(z);
    }

    public static boolean isolatedSplitsEnabled() {
        return aegon.chrome.build.BuildConfig.ISOLATED_SPLITS_ENABLED;
    }

    public static boolean isIsolatedSplitInstalled(Context context, String str) {
        String[] splitNames;
        return Build.VERSION.SDK_INT >= 26 && (splitNames = ApiHelperForO.getSplitNames(context.getApplicationInfo())) != null && Arrays.asList(splitNames).contains(str);
    }

    public static Object getSplitContextLock() {
        return sSplitLock;
    }

    /* JADX WARN: Removed duplicated region for block: B:29:0x0083 A[Catch: all -> 0x00ae, TryCatch #2 {, blocks: (B:24:0x004f, B:26:0x0057, B:27:0x0079, B:29:0x0083, B:31:0x008d, B:33:0x009b, B:32:0x0092), top: B:48:0x004f, outer: #1 }] */
    /* JADX WARN: Removed duplicated region for block: B:32:0x0092 A[Catch: all -> 0x00ae, TryCatch #2 {, blocks: (B:24:0x004f, B:26:0x0057, B:27:0x0079, B:29:0x0083, B:31:0x008d, B:33:0x009b, B:32:0x0092), top: B:48:0x004f, outer: #1 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static android.content.Context createIsolatedSplitContext(android.content.Context r8, java.lang.String r9) {
        /*
            int r0 = android.os.Build.VERSION.SDK_INT
            r1 = 26
            if (r0 >= r1) goto L7
            return r8
        L7:
            boolean r0 = isApplicationContext(r8)     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            if (r0 == 0) goto L12
            android.content.Context r8 = aegon.chrome.base.compat.ApiHelperForO.createContextForSplit(r8, r9)     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            goto L1c
        L12:
            java.lang.Object r0 = getSplitContextLock()     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            monitor-enter(r0)     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            android.content.Context r8 = aegon.chrome.base.compat.ApiHelperForO.createContextForSplit(r8, r9)     // Catch: java.lang.Throwable -> Lb1
            monitor-exit(r0)     // Catch: java.lang.Throwable -> Lb1
        L1c:
            java.lang.ClassLoader r0 = r8.getClassLoader()     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            java.lang.ClassLoader r0 = r0.getParent()     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            android.content.Context r1 = aegon.chrome.base.ContextUtils.getApplicationContext()     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            boolean r2 = isolatedSplitsEnabled()     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            r3 = 1
            if (r2 == 0) goto L49
            java.lang.Class<aegon.chrome.base.BundleUtils> r2 = aegon.chrome.base.BundleUtils.class
            java.lang.ClassLoader r2 = r2.getClassLoader()     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            boolean r2 = r0.equals(r2)     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            if (r2 != 0) goto L49
            if (r1 == 0) goto L49
            java.lang.ClassLoader r2 = r1.getClassLoader()     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            boolean r0 = r0.equals(r2)     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            if (r0 != 0) goto L49
            r0 = 1
            goto L4a
        L49:
            r0 = 0
        L4a:
            androidx.collection.SimpleArrayMap<java.lang.String, java.lang.ClassLoader> r2 = aegon.chrome.base.BundleUtils.sCachedClassLoaders     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            monitor-enter(r2)     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            if (r0 == 0) goto L79
            androidx.collection.SimpleArrayMap<java.lang.String, java.lang.ClassLoader> r4 = aegon.chrome.base.BundleUtils.sCachedClassLoaders     // Catch: java.lang.Throwable -> Lae
            boolean r4 = r4.containsKey(r9)     // Catch: java.lang.Throwable -> Lae
            if (r4 != 0) goto L79
            android.content.pm.ApplicationInfo r4 = r8.getApplicationInfo()     // Catch: java.lang.Throwable -> Lae
            java.lang.String[] r4 = aegon.chrome.base.compat.ApiHelperForO.getSplitNames(r4)     // Catch: java.lang.Throwable -> Lae
            int r4 = java.util.Arrays.binarySearch(r4, r9)     // Catch: java.lang.Throwable -> Lae
            androidx.collection.SimpleArrayMap<java.lang.String, java.lang.ClassLoader> r5 = aegon.chrome.base.BundleUtils.sCachedClassLoaders     // Catch: java.lang.Throwable -> Lae
            dalvik.system.PathClassLoader r6 = new dalvik.system.PathClassLoader     // Catch: java.lang.Throwable -> Lae
            android.content.pm.ApplicationInfo r7 = r8.getApplicationInfo()     // Catch: java.lang.Throwable -> Lae
            java.lang.String[] r7 = r7.splitSourceDirs     // Catch: java.lang.Throwable -> Lae
            r4 = r7[r4]     // Catch: java.lang.Throwable -> Lae
            java.lang.ClassLoader r1 = r1.getClassLoader()     // Catch: java.lang.Throwable -> Lae
            r6.<init>(r4, r1)     // Catch: java.lang.Throwable -> Lae
            r5.put(r9, r6)     // Catch: java.lang.Throwable -> Lae
        L79:
            androidx.collection.SimpleArrayMap<java.lang.String, java.lang.ClassLoader> r1 = aegon.chrome.base.BundleUtils.sCachedClassLoaders     // Catch: java.lang.Throwable -> Lae
            java.lang.Object r1 = r1.get(r9)     // Catch: java.lang.Throwable -> Lae
            java.lang.ClassLoader r1 = (java.lang.ClassLoader) r1     // Catch: java.lang.Throwable -> Lae
            if (r1 == 0) goto L92
            java.lang.ClassLoader r4 = r8.getClassLoader()     // Catch: java.lang.Throwable -> Lae
            boolean r4 = r1.equals(r4)     // Catch: java.lang.Throwable -> Lae
            if (r4 != 0) goto L9b
            replaceClassLoader(r8, r1)     // Catch: java.lang.Throwable -> Lae
            r0 = 1
            goto L9b
        L92:
            androidx.collection.SimpleArrayMap<java.lang.String, java.lang.ClassLoader> r1 = aegon.chrome.base.BundleUtils.sCachedClassLoaders     // Catch: java.lang.Throwable -> Lae
            java.lang.ClassLoader r3 = r8.getClassLoader()     // Catch: java.lang.Throwable -> Lae
            r1.put(r9, r3)     // Catch: java.lang.Throwable -> Lae
        L9b:
            monitor-exit(r2)     // Catch: java.lang.Throwable -> Lae
            java.lang.StringBuilder r1 = new java.lang.StringBuilder     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            java.lang.String r2 = "Android.IsolatedSplits.ClassLoaderReplaced."
            r1.<init>(r2)     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            r1.append(r9)     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            java.lang.String r9 = r1.toString()     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            aegon.chrome.base.metrics.RecordHistogram.recordBooleanHistogram(r9, r0)     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
            return r8
        Lae:
            r8 = move-exception
            monitor-exit(r2)     // Catch: java.lang.Throwable -> Lae
            throw r8     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
        Lb1:
            r8 = move-exception
            monitor-exit(r0)     // Catch: java.lang.Throwable -> Lb1
            throw r8     // Catch: android.content.pm.PackageManager.NameNotFoundException -> Lb4
        Lb4:
            r8 = move-exception
            java.lang.RuntimeException r9 = new java.lang.RuntimeException
            r9.<init>(r8)
            throw r9
        */
        throw new UnsupportedOperationException("Method not decompiled: aegon.chrome.base.BundleUtils.createIsolatedSplitContext(android.content.Context, java.lang.String):android.content.Context");
    }

    public static void replaceClassLoader(Context context, ClassLoader classLoader) throws IllegalArgumentException {
        while (context instanceof ContextWrapper) {
            context = ((ContextWrapper) context).getBaseContext();
        }
        try {
            Field declaredField = context.getClass().getDeclaredField("mClassLoader");
            declaredField.setAccessible(true);
            declaredField.set(context, classLoader);
        } catch (ReflectiveOperationException e) {
            throw new RuntimeException("Error setting ClassLoader.", e);
        }
    }

    public static String getNativeLibraryPath(String str, String str2) {
        StrictModeContext strictModeContextAllowDiskReads = StrictModeContext.allowDiskReads();
        try {
            String strFindLibrary = ((BaseDexClassLoader) BundleUtils.class.getClassLoader()).findLibrary(str);
            if (strFindLibrary != null) {
                if (strictModeContextAllowDiskReads != null) {
                    strictModeContextAllowDiskReads.lambda$new$0();
                }
                return strFindLibrary;
            }
            ClassLoader classLoader = ContextUtils.getApplicationContext().getClassLoader();
            if (classLoader instanceof BaseDexClassLoader) {
                strFindLibrary = ((BaseDexClassLoader) classLoader).findLibrary(str);
            } else if (classLoader instanceof WrappedClassLoader) {
                strFindLibrary = ((WrappedClassLoader) classLoader).findLibrary(str);
            }
            if (strFindLibrary != null) {
                if (strictModeContextAllowDiskReads != null) {
                    strictModeContextAllowDiskReads.lambda$new$0();
                }
                return strFindLibrary;
            }
            String splitApkLibraryPath = getSplitApkLibraryPath(str, str2);
            if (strictModeContextAllowDiskReads != null) {
                strictModeContextAllowDiskReads.lambda$new$0();
            }
            return splitApkLibraryPath;
        } catch (Throwable th) {
            if (strictModeContextAllowDiskReads != null) {
                try {
                    strictModeContextAllowDiskReads.lambda$new$0();
                } catch (Throwable unused) {
                }
            }
            throw th;
        }
    }

    public static String getNativeLibraryPath(String str) {
        return getNativeLibraryPath(str, "");
    }

    private static String getSplitApkLibraryPath(String str, String str2) {
        ApplicationInfo applicationInfo;
        String[] splitNames;
        int iBinarySearch;
        if (Build.VERSION.SDK_INT < 26 || (splitNames = ApiHelperForO.getSplitNames((applicationInfo = ContextUtils.getApplicationContext().getApplicationInfo()))) == null || (iBinarySearch = Arrays.binarySearch(splitNames, str2)) < 0) {
            return null;
        }
        try {
            return applicationInfo.splitSourceDirs[iBinarySearch] + "!/lib/" + ((String) applicationInfo.getClass().getField("primaryCpuAbi").get(applicationInfo)) + PsuedoNames.PSEUDONAME_ROOT + System.mapLibraryName(str);
        } catch (ReflectiveOperationException e) {
            throw new RuntimeException(e);
        }
    }

    private static boolean isApplicationContext(Context context) {
        while (context instanceof ContextWrapper) {
            if (context instanceof Application) {
                return true;
            }
            context = ((ContextWrapper) context).getBaseContext();
        }
        return false;
    }
}
