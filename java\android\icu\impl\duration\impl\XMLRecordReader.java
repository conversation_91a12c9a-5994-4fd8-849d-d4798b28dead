package android.icu.impl.duration.impl;

import android.icu.lang.UCharacter;
import com.xiaomi.stat.C7892d;
import java.p654io.IOException;
import java.p654io.Reader;
import java.util.ArrayList;
import java.util.List;
import org.apache.xpath.compiler.PsuedoNames;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class XMLRecordReader implements RecordReader {
    private boolean atTag;
    private List<String> nameStack = new ArrayList();

    /* renamed from: r */
    private Reader f76r;
    private String tag;

    public XMLRecordReader(Reader r) {
        this.f76r = r;
        if (getTag().startsWith("?xml")) {
            advance();
        }
        if (getTag().startsWith("!--")) {
            advance();
        }
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public boolean open(String title) {
        if (getTag().equals(title)) {
            this.nameStack.add(title);
            advance();
            return true;
        }
        return false;
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public boolean close() {
        int ix = this.nameStack.size() - 1;
        String name = this.nameStack.get(ix);
        if (getTag().equals(PsuedoNames.PSEUDONAME_ROOT + name)) {
            this.nameStack.remove(ix);
            advance();
            return true;
        }
        return false;
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public boolean bool(String name) {
        String s = string(name);
        if (s != null) {
            return "true".equals(s);
        }
        return false;
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public boolean[] boolArray(String name) {
        String[] sa = stringArray(name);
        if (sa != null) {
            boolean[] result = new boolean[sa.length];
            for (int i = 0; i < sa.length; i++) {
                result[i] = "true".equals(sa[i]);
            }
            return result;
        }
        return null;
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public char character(String name) {
        String s = string(name);
        if (s != null) {
            return s.charAt(0);
        }
        return (char) 65535;
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public char[] characterArray(String name) {
        String[] sa = stringArray(name);
        if (sa != null) {
            char[] result = new char[sa.length];
            for (int i = 0; i < sa.length; i++) {
                result[i] = sa[i].charAt(0);
            }
            return result;
        }
        return null;
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public byte namedIndex(String name, String[] names) {
        String sa = string(name);
        if (sa != null) {
            for (int i = 0; i < names.length; i++) {
                if (sa.equals(names[i])) {
                    return (byte) i;
                }
            }
            return (byte) -1;
        }
        return (byte) -1;
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public byte[] namedIndexArray(String name, String[] names) {
        String[] sa = stringArray(name);
        if (sa != null) {
            byte[] result = new byte[sa.length];
            for (int i = 0; i < sa.length; i++) {
                String s = sa[i];
                int j = 0;
                while (true) {
                    if (j < names.length) {
                        if (!names[j].equals(s)) {
                            j++;
                        } else {
                            result[i] = (byte) j;
                            break;
                        }
                    } else {
                        result[i] = -1;
                        break;
                    }
                }
            }
            return result;
        }
        return null;
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public String string(String name) {
        if (match(name)) {
            String result = readData();
            if (match(PsuedoNames.PSEUDONAME_ROOT + name)) {
                return result;
            }
            return null;
        }
        return null;
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public String[] stringArray(String name) {
        if (match(name + "List")) {
            List<String> list = new ArrayList<>();
            while (true) {
                String strString = string(name);
                String s = strString;
                if (strString == null) {
                    break;
                }
                if ("Null".equals(s)) {
                    s = null;
                }
                list.add(s);
            }
            if (match(PsuedoNames.PSEUDONAME_ROOT + name + "List")) {
                return (String[]) list.toArray(new String[list.size()]);
            }
            return null;
        }
        return null;
    }

    @Override // android.icu.impl.duration.impl.RecordReader
    public String[][] stringTable(String name) {
        if (match(name + "Table")) {
            List<String[]> list = new ArrayList<>();
            while (true) {
                String[] sa = stringArray(name);
                if (sa == null) {
                    break;
                }
                list.add(sa);
            }
            if (match(PsuedoNames.PSEUDONAME_ROOT + name + "Table")) {
                return (String[][]) list.toArray(new String[list.size()][]);
            }
            return null;
        }
        return null;
    }

    private boolean match(String target) {
        if (getTag().equals(target)) {
            advance();
            return true;
        }
        return false;
    }

    private String getTag() {
        if (this.tag == null) {
            this.tag = readNextTag();
        }
        return this.tag;
    }

    private void advance() {
        this.tag = null;
    }

    private String readData() {
        int c2;
        StringBuilder sb = new StringBuilder();
        boolean inWhitespace = false;
        while (true) {
            c2 = readChar();
            if (c2 == -1 || c2 == 60) {
                break;
            }
            if (c2 == 38) {
                int c3 = readChar();
                if (c3 == 35) {
                    StringBuilder numBuf = new StringBuilder();
                    int radix = 10;
                    int c4 = readChar();
                    if (c4 == 120) {
                        radix = 16;
                        c4 = readChar();
                    }
                    while (c4 != 59 && c4 != -1) {
                        numBuf.append((char) c4);
                        c4 = readChar();
                    }
                    try {
                        int num = Integer.parseInt(numBuf.toString(), radix);
                        c2 = (char) num;
                    } catch (NumberFormatException ex) {
                        System.err.println("numbuf: " + numBuf.toString() + " radix: " + radix);
                        throw ex;
                    }
                } else {
                    StringBuilder charBuf = new StringBuilder();
                    while (c3 != 59 && c3 != -1) {
                        charBuf.append((char) c3);
                        c3 = readChar();
                    }
                    String charName = charBuf.toString();
                    if (charName.equals(C7892d.f22699T)) {
                        c2 = 60;
                    } else if (charName.equals("gt")) {
                        c2 = 62;
                    } else if (charName.equals("quot")) {
                        c2 = 34;
                    } else if (charName.equals("apos")) {
                        c2 = 39;
                    } else if (charName.equals("amp")) {
                        c2 = 38;
                    } else {
                        System.err.println("unrecognized character entity: '" + charName + "'");
                    }
                }
            }
            if (UCharacter.isWhitespace(c2)) {
                if (!inWhitespace) {
                    c2 = 32;
                    inWhitespace = true;
                }
            } else {
                inWhitespace = false;
            }
            sb.append((char) c2);
        }
        this.atTag = c2 == 60;
        return sb.toString();
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x0032, code lost:
    
        if (r0 != 60) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0034, code lost:
    
        r5.atTag = true;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private java.lang.String readNextTag() {
        /*
            r5 = this;
            r0 = 0
        L1:
            boolean r1 = r5.atTag
            r2 = -1
            if (r1 != 0) goto L37
            int r0 = r5.readChar()
            r1 = 60
            if (r0 == r1) goto L32
            if (r0 != r2) goto L11
            goto L32
        L11:
            boolean r1 = android.icu.lang.UCharacter.isWhitespace(r0)
            if (r1 != 0) goto L1
            java.io.PrintStream r1 = java.lang.System.err
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            r3.<init>()
            java.lang.String r4 = "Unexpected non-whitespace character "
            r3.append(r4)
            java.lang.String r4 = java.lang.Integer.toHexString(r0)
            r3.append(r4)
            java.lang.String r3 = r3.toString()
            r1.println(r3)
            goto L37
        L32:
            if (r0 != r1) goto L37
            r1 = 1
            r5.atTag = r1
        L37:
            boolean r1 = r5.atTag
            if (r1 == 0) goto L58
            r1 = 0
            r5.atTag = r1
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
        L43:
            int r0 = r5.readChar()
            r3 = 62
            if (r0 == r3) goto L53
            if (r0 != r2) goto L4e
            goto L53
        L4e:
            char r3 = (char) r0
            r1.append(r3)
            goto L43
        L53:
            java.lang.String r2 = r1.toString()
            return r2
        L58:
            r1 = 0
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.duration.impl.XMLRecordReader.readNextTag():java.lang.String");
    }

    int readChar() {
        try {
            return this.f76r.read();
        } catch (IOException e) {
            return -1;
        }
    }
}
