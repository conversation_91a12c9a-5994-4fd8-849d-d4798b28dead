package aegon.chrome.net.test;

import aegon.chrome.net.BidirectionalStream;
import aegon.chrome.net.CronetEngine;
import aegon.chrome.net.ExperimentalBidirectionalStream;
import aegon.chrome.net.ICronetEngineBuilder;
import aegon.chrome.net.NetworkQualityRttListener;
import aegon.chrome.net.NetworkQualityThroughputListener;
import aegon.chrome.net.RequestFinishedInfo;
import aegon.chrome.net.UrlRequest;
import aegon.chrome.net.impl.CronetEngineBase;
import aegon.chrome.net.impl.CronetEngineBuilderImpl;
import aegon.chrome.net.impl.ImplVersion;
import aegon.chrome.net.impl.UrlRequestBase;
import android.content.Context;
import java.net.Proxy;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLStreamHandlerFactory;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
final class FakeCronetEngine extends CronetEngineBase {
    private int mActiveRequestCount;
    private final FakeCronetController mController;
    private final ExecutorService mExecutorService;
    private boolean mIsShutdown;
    private final Object mLock;

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final void addRequestFinishedListener(RequestFinishedInfo.Listener listener) {
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final void addRttListener(NetworkQualityRttListener networkQualityRttListener) {
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final void addThroughputListener(NetworkQualityThroughputListener networkQualityThroughputListener) {
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final void configureNetworkQualityEstimatorForTesting(boolean z, boolean z2, boolean z3) {
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final int getDownstreamThroughputKbps() {
        return -1;
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final int getEffectiveConnectionType() {
        return 0;
    }

    @Override // aegon.chrome.net.CronetEngine
    public final byte[] getGlobalMetricsDeltas() {
        return new byte[0];
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final int getHttpRttMs() {
        return -1;
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final int getTransportRttMs() {
        return -1;
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final void removeRequestFinishedListener(RequestFinishedInfo.Listener listener) {
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final void removeRttListener(NetworkQualityRttListener networkQualityRttListener) {
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final void removeThroughputListener(NetworkQualityThroughputListener networkQualityThroughputListener) {
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final void startNetLogToDisk(String str, boolean z, int i) {
    }

    @Override // aegon.chrome.net.CronetEngine
    public final void startNetLogToFile(String str, boolean z) {
    }

    @Override // aegon.chrome.net.CronetEngine
    public final void stopNetLog() {
    }

    @Override // aegon.chrome.net.impl.CronetEngineBase, aegon.chrome.net.ExperimentalCronetEngine, aegon.chrome.net.CronetEngine
    public final /* bridge */ /* synthetic */ UrlRequest.Builder newUrlRequestBuilder(String str, UrlRequest.Callback callback, Executor executor) {
        return super.newUrlRequestBuilder(str, callback, executor);
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static class Builder extends CronetEngineBuilderImpl {
        private FakeCronetController mController;

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder addPublicKeyPins(String str, Set set, boolean z, Date date) {
            return super.addPublicKeyPins(str, (Set<byte[]>) set, z, date);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder addQuicHint(String str, int i, int i2) {
            return super.addQuicHint(str, i, i2);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder enableBrotli(boolean z) {
            return super.enableBrotli(z);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder enableHttp2(boolean z) {
            return super.enableHttp2(z);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder enableHttpCache(int i, long j) {
            return super.enableHttpCache(i, j);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder enableNetworkQualityEstimator(boolean z) {
            return super.enableNetworkQualityEstimator(z);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder enablePublicKeyPinningBypassForLocalTrustAnchors(boolean z) {
            return super.enablePublicKeyPinningBypassForLocalTrustAnchors(z);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder enableQuic(boolean z) {
            return super.enableQuic(z);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder enableSdch(boolean z) {
            return super.enableSdch(z);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder setExperimentalOptions(String str) {
            return super.setExperimentalOptions(str);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder setLibraryLoader(CronetEngine.Builder.LibraryLoader libraryLoader) {
            return super.setLibraryLoader(libraryLoader);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder setStoragePath(String str) {
            return super.setStoragePath(str);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder setThreadPriority(int i) {
            return super.setThreadPriority(i);
        }

        @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
        public /* bridge */ /* synthetic */ ICronetEngineBuilder setUserAgent(String str) {
            return super.setUserAgent(str);
        }

        Builder(Context context) {
            super(context);
        }

        @Override // aegon.chrome.net.ICronetEngineBuilder
        public FakeCronetEngine build() {
            return new FakeCronetEngine(this);
        }

        void setController(FakeCronetController fakeCronetController) {
            this.mController = fakeCronetController;
        }
    }

    private FakeCronetEngine(Builder builder) {
        this.mLock = new Object();
        if (builder.mController != null) {
            this.mController = builder.mController;
        } else {
            this.mController = new FakeCronetController();
        }
        this.mExecutorService = new ThreadPoolExecutor(1, 5, 50L, TimeUnit.SECONDS, new LinkedBlockingQueue(), new ThreadFactory() { // from class: aegon.chrome.net.test.FakeCronetEngine.1
            @Override // java.util.concurrent.ThreadFactory
            public Thread newThread(final Runnable runnable) {
                return Executors.defaultThreadFactory().newThread(new Runnable() { // from class: aegon.chrome.net.test.FakeCronetEngine.1.1
                    @Override // java.lang.Runnable
                    public void run() {
                        Thread.currentThread().setName("FakeCronetEngine");
                        runnable.run();
                    }
                });
            }
        });
        FakeCronetController.addFakeCronetEngine(this);
    }

    final FakeCronetController getController() {
        return this.mController;
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final ExperimentalBidirectionalStream.Builder newBidirectionalStreamBuilder(String str, BidirectionalStream.Callback callback, Executor executor) {
        synchronized (this.mLock) {
            if (this.mIsShutdown) {
                throw new IllegalStateException("This instance of CronetEngine has been shutdown and can no longer be used.");
            }
            throw new UnsupportedOperationException("The bidirectional stream API is not supported by the Fake implementation of CronetEngine.");
        }
    }

    @Override // aegon.chrome.net.CronetEngine
    public final String getVersionString() {
        return "FakeCronet/" + ImplVersion.getCronetVersionWithLastChange();
    }

    @Override // aegon.chrome.net.CronetEngine
    public final void shutdown() {
        synchronized (this.mLock) {
            if (this.mActiveRequestCount != 0) {
                throw new IllegalStateException("Cannot shutdown with active requests.");
            }
            this.mIsShutdown = true;
        }
        this.mExecutorService.shutdown();
        FakeCronetController.removeFakeCronetEngine(this);
    }

    @Override // aegon.chrome.net.CronetEngine
    public final URLConnection openConnection(URL url) {
        throw new UnsupportedOperationException("The openConnection API is not supported by the Fake implementation of CronetEngine.");
    }

    @Override // aegon.chrome.net.ExperimentalCronetEngine
    public final URLConnection openConnection(URL url, Proxy proxy) {
        throw new UnsupportedOperationException("The openConnection API is not supported by the Fake implementation of CronetEngine.");
    }

    @Override // aegon.chrome.net.CronetEngine
    public final URLStreamHandlerFactory createURLStreamHandlerFactory() {
        throw new UnsupportedOperationException("The URLStreamHandlerFactory API is not supported by the Fake implementation of CronetEngine.");
    }

    @Override // aegon.chrome.net.impl.CronetEngineBase
    public final UrlRequestBase createRequest(String str, UrlRequest.Callback callback, Executor executor, int i, Collection<Object> collection, boolean z, boolean z2, boolean z3, boolean z4, int i2, boolean z5, int i3, RequestFinishedInfo.Listener listener, int i4) {
        FakeUrlRequest fakeUrlRequest;
        synchronized (this.mLock) {
            if (this.mIsShutdown) {
                throw new IllegalStateException("This instance of CronetEngine has been shutdown and can no longer be used.");
            }
            fakeUrlRequest = new FakeUrlRequest(callback, executor, this.mExecutorService, str, z3, z4, i2, z5, i3, this.mController, this);
        }
        return fakeUrlRequest;
    }

    @Override // aegon.chrome.net.impl.CronetEngineBase
    public final ExperimentalBidirectionalStream createBidirectionalStream(String str, BidirectionalStream.Callback callback, Executor executor, String str2, List<Map.Entry<String, String>> list, int i, boolean z, Collection<Object> collection, boolean z2, int i2, boolean z3, int i3) {
        synchronized (this.mLock) {
            if (this.mIsShutdown) {
                throw new IllegalStateException("This instance of CronetEngine has been shutdown and can no longer be used.");
            }
            throw new UnsupportedOperationException("The BidirectionalStream API is not supported by the Fake implementation of CronetEngine.");
        }
    }

    final boolean startRequest() {
        synchronized (this.mLock) {
            if (this.mIsShutdown) {
                return false;
            }
            this.mActiveRequestCount++;
            return true;
        }
    }

    final void onRequestDestroyed() {
        synchronized (this.mLock) {
            if (this.mIsShutdown) {
                throw new IllegalStateException("This instance of CronetEngine was shutdown. All requests must have been complete.");
            }
            this.mActiveRequestCount--;
        }
    }
}
