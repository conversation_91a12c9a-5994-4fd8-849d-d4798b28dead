package android.icu.impl.number.parse;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class RequireNumberValidator extends ValidationMatcher {
    @Override // android.icu.impl.number.parse.NumberParseMatcher
    public void postProcess(ParsedNumber result) {
        if (!result.seenNumber()) {
            result.flags |= 256;
        }
    }

    public String toString() {
        return "<RequireNumber>";
    }
}
