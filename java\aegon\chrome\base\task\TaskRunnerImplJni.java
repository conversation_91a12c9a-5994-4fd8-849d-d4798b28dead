package aegon.chrome.base.task;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.base.task.TaskRunnerImpl;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class TaskRunnerImplJni implements TaskRunnerImpl.Natives {
    public static final JniStaticTestMocker<TaskRunnerImpl.Natives> TEST_HOOKS = new JniStaticTestMocker<TaskRunnerImpl.Natives>() { // from class: aegon.chrome.base.task.TaskRunnerImplJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(TaskRunnerImpl.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                TaskRunnerImpl.Natives unused = TaskRunnerImplJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static TaskRunnerImpl.Natives testInstance;

    TaskRunnerImplJni() {
    }

    @Override // aegon.chrome.base.task.TaskRunnerImpl.Natives
    public long init(int i, int i2, boolean z, boolean z2, byte b2, byte[] bArr) {
        return GEN_JNI.org_chromium_base_task_TaskRunnerImpl_init(i, i2, z, z2, b2, bArr);
    }

    @Override // aegon.chrome.base.task.TaskRunnerImpl.Natives
    public void destroy(long j) {
        GEN_JNI.org_chromium_base_task_TaskRunnerImpl_destroy(j);
    }

    @Override // aegon.chrome.base.task.TaskRunnerImpl.Natives
    public void postDelayedTask(long j, Runnable runnable, long j2, String str) {
        GEN_JNI.org_chromium_base_task_TaskRunnerImpl_postDelayedTask(j, runnable, j2, str);
    }

    @Override // aegon.chrome.base.task.TaskRunnerImpl.Natives
    public boolean belongsToCurrentThread(long j) {
        return GEN_JNI.org_chromium_base_task_TaskRunnerImpl_belongsToCurrentThread(j);
    }

    public static TaskRunnerImpl.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            TaskRunnerImpl.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.task.TaskRunnerImpl.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new TaskRunnerImplJni();
    }
}
