package android.icu.impl.coll;

import android.icu.impl.Normalizer2Impl;
import com.kuaishou.socket.nano.SocketMessages;
import dalvik.bytecode.Opcodes;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationFastLatin {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    static final int BAIL_OUT = 1;
    public static final int BAIL_OUT_RESULT = -2;
    static final int CASE_AND_TERTIARY_MASK = 31;
    static final int CASE_MASK = 24;
    static final int COMMON_SEC = 160;
    static final int COMMON_SEC_PLUS_OFFSET = 192;
    static final int COMMON_TER = 0;
    static final int COMMON_TER_PLUS_OFFSET = 32;
    static final int CONTRACTION = 1024;
    static final int CONTR_CHAR_MASK = 511;
    static final int CONTR_LENGTH_SHIFT = 9;
    static final int EOS = 2;
    static final int EXPANSION = 2048;
    static final int INDEX_MASK = 1023;
    public static final int LATIN_LIMIT = 384;
    public static final int LATIN_MAX = 383;
    static final int LATIN_MAX_UTF8_LEAD = 197;
    static final int LONG_INC = 8;
    static final int LONG_PRIMARY_MASK = 65528;
    static final int LOWER_CASE = 8;
    static final int MAX_LONG = 4088;
    static final int MAX_SEC_AFTER = 352;
    static final int MAX_SEC_BEFORE = 128;
    static final int MAX_SEC_HIGH = 992;
    static final int MAX_SHORT = 64512;
    static final int MAX_TER_AFTER = 7;
    static final int MERGE_WEIGHT = 3;
    static final int MIN_LONG = 3072;
    static final int MIN_SEC_AFTER = 192;
    static final int MIN_SEC_BEFORE = 0;
    static final int MIN_SEC_HIGH = 384;
    static final int MIN_SHORT = 4096;
    static final int NUM_FAST_CHARS = 448;
    static final int PUNCT_LIMIT = 8256;
    static final int PUNCT_START = 8192;
    static final int SECONDARY_MASK = 992;
    static final int SEC_INC = 32;
    static final int SEC_OFFSET = 32;
    static final int SHORT_INC = 1024;
    static final int SHORT_PRIMARY_MASK = 64512;
    static final int TERTIARY_MASK = 7;
    static final int TER_OFFSET = 32;
    static final int TWO_CASES_MASK = 1572888;
    static final int TWO_COMMON_SEC_PLUS_OFFSET = 12583104;
    static final int TWO_COMMON_TER_PLUS_OFFSET = 2097184;
    static final int TWO_LONG_PRIMARIES_MASK = -458760;
    static final int TWO_LOWER_CASES = 524296;
    static final int TWO_SECONDARIES_MASK = 65012704;
    static final int TWO_SEC_OFFSETS = 2097184;
    static final int TWO_SHORT_PRIMARIES_MASK = -67044352;
    static final int TWO_TERTIARIES_MASK = 458759;
    static final int TWO_TER_OFFSETS = 2097184;
    public static final int VERSION = 2;

    static int getCharIndex(char c2) {
        if (c2 <= 383) {
            return c2;
        }
        if (8192 <= c2 && c2 < PUNCT_LIMIT) {
            return c2 - 7808;
        }
        return -1;
    }

    public static int getOptions(CollationData data, CollationSettings settings, char[] primaries) {
        int headerLength;
        int miniVarTop;
        int miniVarTop2;
        int p;
        char[] header = data.fastLatinTableHeader;
        if (header == null || primaries.length != 384) {
            return -1;
        }
        if ((settings.options & 12) == 0) {
            headerLength = Opcodes.OP_IGET_CHAR_JUMBO;
        } else {
            int headerLength2 = header[0] & 255;
            int i = settings.getMaxVariable() + 1;
            if (i >= headerLength2) {
                return -1;
            }
            headerLength = header[i];
        }
        int i2 = 0;
        if (!settings.hasReordering()) {
            miniVarTop = headerLength;
        } else {
            long prevStart = 0;
            long beforeDigitStart = 0;
            long digitStart = 0;
            long afterDigitStart = 0;
            int group = 4096;
            while (group < 4104) {
                int miniVarTop3 = headerLength;
                long start = settings.reorder(data.getFirstPrimaryForGroup(group));
                if (group == 4100) {
                    beforeDigitStart = prevStart;
                    digitStart = start;
                } else if (start == 0) {
                    continue;
                } else {
                    if (start < prevStart) {
                        return -1;
                    }
                    if (digitStart != 0 && afterDigitStart == 0 && prevStart == beforeDigitStart) {
                        afterDigitStart = start;
                    }
                    prevStart = start;
                }
                group++;
                headerLength = miniVarTop3;
            }
            miniVarTop = headerLength;
            long latinStart = settings.reorder(data.getFirstPrimaryForGroup(25));
            if (latinStart < prevStart) {
                return -1;
            }
            if (afterDigitStart == 0) {
                afterDigitStart = latinStart;
            }
            if (beforeDigitStart >= digitStart || digitStart >= afterDigitStart) {
                i2 = 1;
            }
        }
        char[] table = data.fastLatinTable;
        int c2 = 0;
        while (c2 < 384) {
            char c3 = table[c2];
            if (c3 >= 4096) {
                p = c3 & 64512;
                miniVarTop2 = miniVarTop;
            } else {
                miniVarTop2 = miniVarTop;
                if (c3 > miniVarTop2) {
                    p = c3 & LONG_PRIMARY_MASK;
                } else {
                    p = 0;
                }
            }
            primaries[c2] = (char) p;
            c2++;
            miniVarTop = miniVarTop2;
        }
        int miniVarTop4 = miniVarTop;
        if (i2 != 0 || (settings.options & 2) != 0) {
            for (int c4 = 48; c4 <= 57; c4++) {
                primaries[c4] = 0;
            }
        }
        int c5 = miniVarTop4 << 16;
        return c5 | settings.options;
    }

    /* JADX WARN: Code restructure failed: missing block: B:302:0x03ae, code lost:
    
        r8 = r8 >>> 16;
        r9 = r9 >>> 16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x00e7, code lost:
    
        if (r8 != r9) goto L313;
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x00e9, code lost:
    
        if (r8 != 2) goto L70;
     */
    /* JADX WARN: Code restructure failed: missing block: B:70:0x00ec, code lost:
    
        r9 = 0;
        r8 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x00f3, code lost:
    
        r11 = r8 & 65535;
        r14 = r9 & 65535;
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x00fa, code lost:
    
        if (r11 == r14) goto L77;
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x00fc, code lost:
    
        if (r11 >= r14) goto L75;
     */
    /* JADX WARN: Code restructure failed: missing block: B:74:0x00fe, code lost:
    
        return -1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x0101, code lost:
    
        return 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:77:0x0104, code lost:
    
        if (r8 != 2) goto L302;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r13v12, types: [int] */
    /* JADX WARN: Type inference failed for: r13v23, types: [int] */
    /* JADX WARN: Type inference failed for: r13v6, types: [int] */
    /* JADX WARN: Type inference failed for: r14v10, types: [int] */
    /* JADX WARN: Type inference failed for: r14v16, types: [int] */
    /* JADX WARN: Type inference failed for: r14v6, types: [int] */
    /* JADX WARN: Type inference failed for: r8v0 */
    /* JADX WARN: Type inference failed for: r8v1 */
    /* JADX WARN: Type inference failed for: r8v2 */
    /* JADX WARN: Type inference failed for: r8v3, types: [int] */
    /* JADX WARN: Type inference failed for: r8v4 */
    /* JADX WARN: Type inference failed for: r8v52, types: [int] */
    /* JADX WARN: Type inference failed for: r8v53 */
    /* JADX WARN: Type inference failed for: r8v57 */
    /* JADX WARN: Type inference failed for: r8v59, types: [int] */
    /* JADX WARN: Type inference failed for: r8v60 */
    /* JADX WARN: Type inference failed for: r8v61, types: [char] */
    /* JADX WARN: Type inference failed for: r9v0 */
    /* JADX WARN: Type inference failed for: r9v1 */
    /* JADX WARN: Type inference failed for: r9v2 */
    /* JADX WARN: Type inference failed for: r9v3 */
    /* JADX WARN: Type inference failed for: r9v4, types: [int] */
    /* JADX WARN: Type inference failed for: r9v5 */
    /* JADX WARN: Type inference failed for: r9v57, types: [int] */
    /* JADX WARN: Type inference failed for: r9v58 */
    /* JADX WARN: Type inference failed for: r9v62 */
    /* JADX WARN: Type inference failed for: r9v63 */
    /* JADX WARN: Type inference failed for: r9v65, types: [int] */
    /* JADX WARN: Type inference failed for: r9v66, types: [char] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static int compareUTF16(char[] r24, char[] r25, int r26, java.lang.CharSequence r27, java.lang.CharSequence r28, int r29) {
        /*
            Method dump skipped, instructions count: 951
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationFastLatin.compareUTF16(char[], char[], int, java.lang.CharSequence, java.lang.CharSequence, int):int");
    }

    private static int lookup(char[] table, int c2) {
        if (8192 <= c2 && c2 < PUNCT_LIMIT) {
            return table[(c2 - 8192) + 384];
        }
        if (c2 == 65534) {
            return 3;
        }
        if (c2 == 65535) {
            return 64680;
        }
        return 1;
    }

    private static long nextPair(char[] table, int c2, int ce, CharSequence s16, int sIndex) {
        long result;
        int x;
        if (ce >= MIN_LONG || ce < 1024) {
            return ce;
        }
        if (ce >= 2048) {
            int index = (ce & 1023) + 448;
            return (table[index + 1] << 16) | table[index];
        }
        int index2 = (ce & 1023) + 448;
        boolean inc = false;
        if (sIndex != s16.length()) {
            int i = sIndex + 1;
            int c22 = s16.charAt(sIndex);
            if (c22 > 383) {
                if (8192 <= c22 && c22 < PUNCT_LIMIT) {
                    c22 = (c22 - 8192) + 384;
                } else {
                    if (c22 != 65534 && c22 != 65535) {
                        return 1L;
                    }
                    c22 = -1;
                }
            }
            int i2 = index2;
            char c3 = table[i2];
            do {
                i2 += c3 >> '\t';
                c3 = table[i2];
                x = c3 & 511;
            } while (x < c22);
            if (x == c22) {
                index2 = i2;
                inc = true;
            }
        }
        int length = table[index2] >> '\t';
        if (length == 1) {
            return 1L;
        }
        char c4 = table[index2 + 1];
        if (length == 2) {
            result = c4;
        } else {
            result = (table[index2 + 2] << 16) | c4;
        }
        return inc ? ~result : result;
    }

    private static int getPrimaries(int variableTop, int pair) {
        int ce = 65535 & pair;
        if (ce >= 4096) {
            return TWO_SHORT_PRIMARIES_MASK & pair;
        }
        if (ce > variableTop) {
            return TWO_LONG_PRIMARIES_MASK & pair;
        }
        if (ce >= MIN_LONG) {
            return 0;
        }
        return pair;
    }

    private static int getSecondariesFromOneShortCE(int ce) {
        int ce2 = ce & SocketMessages.PayloadType.SC_LIVE_BLIND_DATE_CHAT_SERVICE_NOTICE;
        if (ce2 < 384) {
            return ce2 + 32;
        }
        return ((ce2 + 32) << 16) | 192;
    }

    private static int getSecondaries(int variableTop, int pair) {
        if (pair <= 65535) {
            if (pair >= 4096) {
                return getSecondariesFromOneShortCE(pair);
            }
            if (pair > variableTop) {
                return 192;
            }
            if (pair >= MIN_LONG) {
                return 0;
            }
            return pair;
        }
        int ce = 65535 & pair;
        if (ce >= 4096) {
            return (TWO_SECONDARIES_MASK & pair) + 2097184;
        }
        if (ce > variableTop) {
            return TWO_COMMON_SEC_PLUS_OFFSET;
        }
        return 0;
    }

    private static int getCases(int variableTop, boolean strengthIsPrimary, int pair) {
        if (pair <= 65535) {
            if (pair >= 4096) {
                int pair2 = pair & 24;
                if (!strengthIsPrimary && (pair & SocketMessages.PayloadType.SC_LIVE_BLIND_DATE_CHAT_SERVICE_NOTICE) >= 384) {
                    return pair2 | 524288;
                }
                return pair2;
            }
            if (pair > variableTop) {
                return 8;
            }
            if (pair >= MIN_LONG) {
                return 0;
            }
            return pair;
        }
        int ce = 65535 & pair;
        if (ce >= 4096) {
            if (strengthIsPrimary && ((-67108864) & pair) == 0) {
                return pair & 24;
            }
            return pair & TWO_CASES_MASK;
        }
        if (ce > variableTop) {
            return TWO_LOWER_CASES;
        }
        return 0;
    }

    private static int getTertiaries(int variableTop, boolean withCaseBits, int pair) {
        int pair2;
        if (pair <= 65535) {
            if (pair < 4096) {
                if (pair > variableTop) {
                    int pair3 = (pair & 7) + 32;
                    if (withCaseBits) {
                        return pair3 | 8;
                    }
                    return pair3;
                }
                if (pair >= MIN_LONG) {
                    return 0;
                }
                return pair;
            }
            if (withCaseBits) {
                int pair4 = (pair & 31) + 32;
                if ((pair & SocketMessages.PayloadType.SC_LIVE_BLIND_DATE_CHAT_SERVICE_NOTICE) < 384) {
                    return pair4;
                }
                return 2621440 | pair4;
            }
            int pair5 = (pair & 7) + 32;
            if ((pair & SocketMessages.PayloadType.SC_LIVE_BLIND_DATE_CHAT_SERVICE_NOTICE) < 384) {
                return pair5;
            }
            return 2097152 | pair5;
        }
        int ce = 65535 & pair;
        if (ce >= 4096) {
            if (withCaseBits) {
                pair2 = pair & 2031647;
            } else {
                pair2 = pair & TWO_TERTIARIES_MASK;
            }
            return pair2 + 2097184;
        }
        if (ce > variableTop) {
            int pair6 = (pair & TWO_TERTIARIES_MASK) + 2097184;
            if (withCaseBits) {
                return pair6 | TWO_LOWER_CASES;
            }
            return pair6;
        }
        return 0;
    }

    private static int getQuaternaries(int variableTop, int pair) {
        if (pair <= 65535) {
            if (pair >= 4096) {
                if ((pair & SocketMessages.PayloadType.SC_LIVE_BLIND_DATE_CHAT_SERVICE_NOTICE) >= 384) {
                    return TWO_SHORT_PRIMARIES_MASK;
                }
                return Normalizer2Impl.MIN_NORMAL_MAYBE_YES;
            }
            if (pair > variableTop) {
                return Normalizer2Impl.MIN_NORMAL_MAYBE_YES;
            }
            if (pair >= MIN_LONG) {
                return pair & LONG_PRIMARY_MASK;
            }
            return pair;
        }
        int ce = 65535 & pair;
        if (ce > variableTop) {
            return TWO_SHORT_PRIMARIES_MASK;
        }
        return pair & TWO_LONG_PRIMARIES_MASK;
    }

    private CollationFastLatin() {
    }
}
