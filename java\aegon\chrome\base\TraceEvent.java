package aegon.chrome.base;

import aegon.chrome.base.annotations.JNINamespace;
import aegon.chrome.base.annotations.MainDex;
import android.os.Looper;
import android.os.MessageQueue;
import android.os.SystemClock;
import android.util.Printer;
import com.getui.gtc.extension.distribution.gbd.p150d.C1928e;
import java.lang.reflect.Method;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.xpath.compiler.PsuedoNames;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@MainDex
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class TraceEvent implements AutoCloseable {
    public static final long ATRACE_TAG_APP = 4096;
    public static final long ATRACE_TAG_WEBVIEW = 16;
    private static ATrace sATrace;
    private static volatile boolean sEnabled;
    private static AtomicBoolean sNativeTracingReady = new AtomicBoolean();
    private static AtomicBoolean sUiThreadReady = new AtomicBoolean();
    private final String mName;

    interface Natives {
        void begin(String str, String str2);

        void beginToplevel(String str);

        void end(String str, String str2);

        void endToplevel(String str);

        void finishAsync(String str, long j);

        void instant(String str, String str2);

        void registerEnabledObserver();

        void setupATraceStartupTrace(String str);

        void startATrace(String str);

        void startAsync(String str, long j);

        void stopATrace();
    }

    static class ATrace implements MessageQueue.IdleHandler {
        static final /* synthetic */ boolean $assertionsDisabled = false;
        private static final String TAG = "ATrace";
        private Method mAsyncTraceBeginMethod;
        private Method mAsyncTraceEndMethod;
        private Method mGetSystemPropertyMethod;
        private boolean mIdleHandlerRegistered;
        private Method mIsTraceTagEnabledMethod;
        private boolean mShouldWriteToSystemTrace;
        private Class<?> mSystemPropertiesClass;
        private Method mTraceBeginMethod;
        private Class<?> mTraceClass;
        private Method mTraceEndMethod;
        private final long mTraceTag;
        private final AtomicBoolean mNativeTracingReady = new AtomicBoolean();
        private final AtomicBoolean mUiThreadReady = new AtomicBoolean();
        private final AtomicBoolean mTraceTagActive = new AtomicBoolean();

        static class CategoryConfig {
            public String filter;
            public boolean shouldWriteToATrace;

            private CategoryConfig() {
                this.filter = "";
                this.shouldWriteToATrace = true;
            }
        }

        public ATrace(long j) {
            try {
                this.mTraceClass = Class.forName("android.os.Trace");
                this.mIsTraceTagEnabledMethod = this.mTraceClass.getMethod("isTagEnabled", Long.TYPE);
                this.mTraceBeginMethod = this.mTraceClass.getMethod("traceBegin", Long.TYPE, String.class);
                this.mTraceEndMethod = this.mTraceClass.getMethod("traceEnd", Long.TYPE);
                this.mAsyncTraceBeginMethod = this.mTraceClass.getMethod("asyncTraceBegin", Long.TYPE, String.class, Integer.TYPE);
                this.mAsyncTraceEndMethod = this.mTraceClass.getMethod("asyncTraceEnd", Long.TYPE, String.class, Integer.TYPE);
                this.mSystemPropertiesClass = Class.forName("android.os.SystemProperties");
                this.mGetSystemPropertyMethod = this.mSystemPropertiesClass.getMethod("get", String.class);
            } catch (Exception e) {
                Log.m46w(TAG, "Reflection error", e);
                this.mIsTraceTagEnabledMethod = null;
            }
            this.mTraceTag = j;
            lambda$onNativeTracingReady$0$TraceEvent$ATrace();
        }

        private String getSystemProperty(String str) {
            try {
                return (String) this.mGetSystemPropertyMethod.invoke(this.mSystemPropertiesClass, str);
            } catch (Exception unused) {
                return null;
            }
        }

        private Integer getIntegerSystemProperty(String str) {
            String systemProperty = getSystemProperty(str);
            if (systemProperty == null) {
                return null;
            }
            try {
                return Integer.decode(systemProperty);
            } catch (NumberFormatException unused) {
                return null;
            }
        }

        private boolean isTraceTagEnabled(long j) {
            try {
                return ((Boolean) this.mIsTraceTagEnabledMethod.invoke(this.mTraceClass, Long.valueOf(j))).booleanValue();
            } catch (Exception unused) {
                return false;
            }
        }

        public boolean hasActiveSession() {
            return this.mTraceTagActive.get();
        }

        /* renamed from: pollConfig, reason: merged with bridge method [inline-methods] */
        private boolean lambda$onNativeTracingReady$0$TraceEvent$ATrace() {
            boolean z = this.mTraceTagActive.get();
            boolean zIsTraceTagEnabled = isTraceTagEnabled(this.mTraceTag);
            if (z == zIsTraceTagEnabled) {
                return false;
            }
            this.mTraceTagActive.set(zIsTraceTagEnabled);
            if (!zIsTraceTagEnabled) {
                EarlyTraceEvent.disable();
                disableNativeATrace();
                this.mShouldWriteToSystemTrace = false;
                ThreadUtils.getUiThreadLooper().setMessageLogging(null);
                return true;
            }
            CategoryConfig categoryConfigFromATrace = getCategoryConfigFromATrace();
            this.mShouldWriteToSystemTrace = false;
            if (this.mNativeTracingReady.get()) {
                if (categoryConfigFromATrace.shouldWriteToATrace) {
                    enableNativeATrace(categoryConfigFromATrace.filter);
                } else {
                    setupATraceStartupTrace(categoryConfigFromATrace.filter);
                }
            } else if (categoryConfigFromATrace.shouldWriteToATrace) {
                this.mShouldWriteToSystemTrace = true;
            } else {
                EarlyTraceEvent.enable();
            }
            if (!categoryConfigFromATrace.shouldWriteToATrace) {
                ThreadUtils.getUiThreadLooper().setMessageLogging(LooperMonitorHolder.sInstance);
            }
            return true;
        }

        private CategoryConfig getCategoryConfigFromATrace() {
            CategoryConfig categoryConfig = new CategoryConfig();
            Integer integerSystemProperty = getIntegerSystemProperty("debug.atrace.app_number");
            if (integerSystemProperty != null && integerSystemProperty.intValue() > 0 && ContextUtils.getApplicationContext() != null) {
                String packageName = ContextUtils.getApplicationContext().getPackageName();
                for (int i = 0; i < integerSystemProperty.intValue(); i++) {
                    String systemProperty = getSystemProperty("debug.atrace.app_" + i);
                    if (systemProperty != null && systemProperty.startsWith(packageName)) {
                        String strSubstring = systemProperty.substring(packageName.length());
                        if (strSubstring.startsWith(PsuedoNames.PSEUDONAME_ROOT)) {
                            for (String str : strSubstring.substring(1).split(":")) {
                                if (str.equals("-atrace")) {
                                    categoryConfig.shouldWriteToATrace = false;
                                } else {
                                    if (categoryConfig.filter.length() > 0) {
                                        categoryConfig.filter += C1928e.f5807a;
                                    }
                                    categoryConfig.filter += str;
                                }
                            }
                        }
                    }
                }
            }
            return categoryConfig;
        }

        public void onNativeTracingReady() {
            this.mNativeTracingReady.set(true);
            this.mTraceTagActive.set(false);
            if (this.mUiThreadReady.get()) {
                ThreadUtils.postOnUiThread(new Runnable() { // from class: aegon.chrome.base.-$$Lambda$TraceEvent$ATrace$8P4tEMXPhHwC-ErKhbjPpmSGO8Y
                    @Override // java.lang.Runnable
                    public final void run() {
                        this.f$0.lambda$onNativeTracingReady$0$TraceEvent$ATrace();
                    }
                });
            }
        }

        public void onUiThreadReady() {
            this.mUiThreadReady.set(true);
            if (!ThreadUtils.runningOnUiThread()) {
                ThreadUtils.postOnUiThread(new Runnable() { // from class: aegon.chrome.base.-$$Lambda$TraceEvent$ATrace$n11cNJ6EIMO_SsfWV9UQah_KlZw
                    @Override // java.lang.Runnable
                    public final void run() {
                        this.f$0.lambda$onUiThreadReady$1$TraceEvent$ATrace();
                    }
                });
            } else {
                lambda$onUiThreadReady$1$TraceEvent$ATrace();
            }
        }

        /* renamed from: startPolling, reason: merged with bridge method [inline-methods] */
        private void lambda$onUiThreadReady$1$TraceEvent$ATrace() {
            ThreadUtils.assertOnUiThread();
            if (!this.mIdleHandlerRegistered) {
                Looper.myQueue().addIdleHandler(this);
                this.mIdleHandlerRegistered = true;
            }
            lambda$onNativeTracingReady$0$TraceEvent$ATrace();
        }

        @Override // android.os.MessageQueue.IdleHandler
        public final boolean queueIdle() {
            lambda$onNativeTracingReady$0$TraceEvent$ATrace();
            return true;
        }

        private void enableNativeATrace(String str) {
            TraceEventJni.get().startATrace(str);
        }

        private void disableNativeATrace() {
            TraceEventJni.get().stopATrace();
        }

        private void setupATraceStartupTrace(String str) {
            TraceEventJni.get().setupATraceStartupTrace(str);
        }

        public void traceBegin(String str) {
            if (this.mShouldWriteToSystemTrace) {
                try {
                    this.mTraceBeginMethod.invoke(this.mTraceClass, Long.valueOf(this.mTraceTag), str);
                } catch (Exception unused) {
                }
            }
        }

        public void traceEnd() {
            if (this.mShouldWriteToSystemTrace) {
                try {
                    this.mTraceEndMethod.invoke(this.mTraceClass, Long.valueOf(this.mTraceTag));
                } catch (Exception unused) {
                }
            }
        }

        public void asyncTraceBegin(String str, int i) {
            if (this.mShouldWriteToSystemTrace) {
                try {
                    this.mAsyncTraceBeginMethod.invoke(this.mTraceClass, Long.valueOf(this.mTraceTag), str, Integer.valueOf(i));
                } catch (Exception unused) {
                }
            }
        }

        public void asyncTraceEnd(String str, int i) {
            if (this.mShouldWriteToSystemTrace) {
                try {
                    this.mAsyncTraceEndMethod.invoke(this.mTraceClass, Long.valueOf(this.mTraceTag), str, Integer.valueOf(i));
                } catch (Exception unused) {
                }
            }
        }
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static class BasicLooperMonitor implements Printer {
        static final /* synthetic */ boolean $assertionsDisabled = false;
        private static final String LOOPER_TASK_PREFIX = "Looper.dispatch: ";
        private static final int SHORTEST_LOG_PREFIX_LENGTH = 18;
        private String mCurrentTarget;

        private BasicLooperMonitor() {
        }

        @Override // android.util.Printer
        public void println(String str) {
            if (str.startsWith(">")) {
                beginHandling(str);
            } else {
                endHandling(str);
            }
        }

        void beginHandling(String str) {
            boolean zEnabled = EarlyTraceEvent.enabled();
            if (TraceEvent.sEnabled || zEnabled) {
                this.mCurrentTarget = getTraceEventName(str);
                if (TraceEvent.sEnabled) {
                    TraceEventJni.get().beginToplevel(this.mCurrentTarget);
                } else {
                    EarlyTraceEvent.begin(this.mCurrentTarget, true);
                }
            }
        }

        void endHandling(String str) {
            boolean zEnabled = EarlyTraceEvent.enabled();
            if ((TraceEvent.sEnabled || zEnabled) && this.mCurrentTarget != null) {
                if (TraceEvent.sEnabled) {
                    TraceEventJni.get().endToplevel(this.mCurrentTarget);
                } else {
                    EarlyTraceEvent.end(this.mCurrentTarget, true);
                }
            }
            this.mCurrentTarget = null;
        }

        private static String getTraceEventName(String str) {
            return LOOPER_TASK_PREFIX + getTarget(str) + "(" + getTargetName(str) + ")";
        }

        private static String getTarget(String str) {
            int iIndexOf = str.indexOf(40, SHORTEST_LOG_PREFIX_LENGTH);
            int iIndexOf2 = iIndexOf == -1 ? -1 : str.indexOf(41, iIndexOf);
            return iIndexOf2 != -1 ? str.substring(iIndexOf + 1, iIndexOf2) : "";
        }

        private static String getTargetName(String str) {
            int iIndexOf = str.indexOf(125, SHORTEST_LOG_PREFIX_LENGTH);
            int iIndexOf2 = iIndexOf == -1 ? -1 : str.indexOf(58, iIndexOf);
            if (iIndexOf2 == -1) {
                iIndexOf2 = str.length();
            }
            return iIndexOf != -1 ? str.substring(iIndexOf + 2, iIndexOf2) : "";
        }
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static final class IdleTracingLooperMonitor extends BasicLooperMonitor implements MessageQueue.IdleHandler {
        private static final long FRAME_DURATION_MILLIS = 16;
        private static final String IDLE_EVENT_NAME = "Looper.queueIdle";
        private static final long MIN_INTERESTING_BURST_DURATION_MILLIS = 48;
        private static final long MIN_INTERESTING_DURATION_MILLIS = 16;
        private static final String TAG = "TraceEvent_LooperMonitor";
        private boolean mIdleMonitorAttached;
        private long mLastIdleStartedAt;
        private long mLastWorkStartedAt;
        private int mNumIdlesSeen;
        private int mNumTasksSeen;
        private int mNumTasksSinceLastIdle;

        private IdleTracingLooperMonitor() {
            super();
        }

        private final void syncIdleMonitoring() {
            if (TraceEvent.sEnabled && !this.mIdleMonitorAttached) {
                this.mLastIdleStartedAt = SystemClock.elapsedRealtime();
                Looper.myQueue().addIdleHandler(this);
                this.mIdleMonitorAttached = true;
                android.util.Log.v(TAG, "attached idle handler");
                return;
            }
            if (!this.mIdleMonitorAttached || TraceEvent.sEnabled) {
                return;
            }
            Looper.myQueue().removeIdleHandler(this);
            this.mIdleMonitorAttached = false;
            android.util.Log.v(TAG, "detached idle handler");
        }

        @Override // aegon.chrome.base.TraceEvent.BasicLooperMonitor
        final void beginHandling(String str) {
            if (this.mNumTasksSinceLastIdle == 0) {
                TraceEvent.end(IDLE_EVENT_NAME);
            }
            this.mLastWorkStartedAt = SystemClock.elapsedRealtime();
            syncIdleMonitoring();
            super.beginHandling(str);
        }

        @Override // aegon.chrome.base.TraceEvent.BasicLooperMonitor
        final void endHandling(String str) {
            long jElapsedRealtime = SystemClock.elapsedRealtime() - this.mLastWorkStartedAt;
            if (jElapsedRealtime > 16) {
                traceAndLog(5, "observed a task that took " + jElapsedRealtime + "ms: " + str);
            }
            super.endHandling(str);
            syncIdleMonitoring();
            this.mNumTasksSeen++;
            this.mNumTasksSinceLastIdle++;
        }

        private static void traceAndLog(int i, String str) {
            TraceEvent.instant("TraceEvent.LooperMonitor:IdleStats", str);
            android.util.Log.println(i, TAG, str);
        }

        @Override // android.os.MessageQueue.IdleHandler
        public final boolean queueIdle() {
            long jElapsedRealtime = SystemClock.elapsedRealtime();
            if (this.mLastIdleStartedAt == 0) {
                this.mLastIdleStartedAt = jElapsedRealtime;
            }
            long j = jElapsedRealtime - this.mLastIdleStartedAt;
            this.mNumIdlesSeen++;
            TraceEvent.begin(IDLE_EVENT_NAME, this.mNumTasksSinceLastIdle + " tasks since last idle.");
            if (j > MIN_INTERESTING_BURST_DURATION_MILLIS) {
                traceAndLog(3, this.mNumTasksSeen + " tasks and " + this.mNumIdlesSeen + " idles processed so far, " + this.mNumTasksSinceLastIdle + " tasks bursted and " + j + "ms elapsed since last idle");
            }
            this.mLastIdleStartedAt = jElapsedRealtime;
            this.mNumTasksSinceLastIdle = 0;
            return true;
        }
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static final class LooperMonitorHolder {
        private static final BasicLooperMonitor sInstance;

        private LooperMonitorHolder() {
        }

        static {
            sInstance = CommandLine.getInstance().hasSwitch(BaseSwitches.ENABLE_IDLE_TRACING) ? new IdleTracingLooperMonitor() : new BasicLooperMonitor();
        }
    }

    private TraceEvent(String str, String str2) {
        this.mName = str;
        begin(str, str2);
    }

    @Override // java.lang.AutoCloseable
    /* renamed from: close */
    public void lambda$new$0() {
        end(this.mName);
    }

    public static TraceEvent scoped(String str, String str2) {
        if (EarlyTraceEvent.enabled() || enabled()) {
            return new TraceEvent(str, str2);
        }
        return null;
    }

    public static TraceEvent scoped(String str) {
        return scoped(str, null);
    }

    public static void setEnabled(boolean z) {
        if (z) {
            EarlyTraceEvent.disable();
        }
        if (sEnabled != z) {
            sEnabled = z;
            ATrace aTrace = sATrace;
            if (aTrace == null || !aTrace.hasActiveSession()) {
                ThreadUtils.getUiThreadLooper().setMessageLogging(z ? LooperMonitorHolder.sInstance : null);
            }
        }
    }

    public static void maybeEnableEarlyTracing(long j, boolean z) {
        if (z) {
            EarlyTraceEvent.maybeEnableInBrowserProcess();
        }
        if (j != 0) {
            sATrace = new ATrace(j);
            if (sNativeTracingReady.get()) {
                sATrace.onNativeTracingReady();
            }
            if (sUiThreadReady.get()) {
                sATrace.onUiThreadReady();
            }
        }
        if (EarlyTraceEvent.enabled()) {
            ATrace aTrace = sATrace;
            if (aTrace == null || !aTrace.hasActiveSession()) {
                ThreadUtils.getUiThreadLooper().setMessageLogging(LooperMonitorHolder.sInstance);
            }
        }
    }

    public static void onNativeTracingReady() {
        sNativeTracingReady.set(true);
        TraceEventJni.get().registerEnabledObserver();
        ATrace aTrace = sATrace;
        if (aTrace != null) {
            aTrace.onNativeTracingReady();
        }
    }

    static void onUiThreadReady() {
        sUiThreadReady.set(true);
        ATrace aTrace = sATrace;
        if (aTrace != null) {
            aTrace.onUiThreadReady();
        }
    }

    public static boolean enabled() {
        return sEnabled;
    }

    public static void instant(String str) {
        if (sEnabled) {
            TraceEventJni.get().instant(str, null);
        }
    }

    public static void instant(String str, String str2) {
        if (sEnabled) {
            TraceEventJni.get().instant(str, str2);
        }
    }

    public static void startAsync(String str, long j) {
        EarlyTraceEvent.startAsync(str, j);
        if (sEnabled) {
            TraceEventJni.get().startAsync(str, j);
            return;
        }
        ATrace aTrace = sATrace;
        if (aTrace != null) {
            aTrace.asyncTraceBegin(str, (int) j);
        }
    }

    public static void finishAsync(String str, long j) {
        EarlyTraceEvent.finishAsync(str, j);
        if (sEnabled) {
            TraceEventJni.get().finishAsync(str, j);
            return;
        }
        ATrace aTrace = sATrace;
        if (aTrace != null) {
            aTrace.asyncTraceEnd(str, (int) j);
        }
    }

    public static void begin(String str) {
        begin(str, null);
    }

    public static void begin(String str, String str2) {
        EarlyTraceEvent.begin(str, false);
        if (sEnabled) {
            TraceEventJni.get().begin(str, str2);
            return;
        }
        ATrace aTrace = sATrace;
        if (aTrace != null) {
            aTrace.traceBegin(str);
        }
    }

    public static void end(String str) {
        end(str, null);
    }

    public static void end(String str, String str2) {
        EarlyTraceEvent.end(str, false);
        if (sEnabled) {
            TraceEventJni.get().end(str, str2);
            return;
        }
        ATrace aTrace = sATrace;
        if (aTrace != null) {
            aTrace.traceEnd();
        }
    }
}
