package aegon.chrome.base;

import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.memory.MemoryPressureCallback;
import android.app.Activity;
import java.util.Iterator;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class MemoryPressureListener {
    private static final String ACTION_LOW_MEMORY = "aegon.chrome.base.ACTION_LOW_MEMORY";
    private static final String ACTION_TRIM_MEMORY = "aegon.chrome.base.ACTION_TRIM_MEMORY";
    private static final String ACTION_TRIM_MEMORY_MODERATE = "aegon.chrome.base.ACTION_TRIM_MEMORY_MODERATE";
    private static final String ACTION_TRIM_MEMORY_RUNNING_CRITICAL = "aegon.chrome.base.ACTION_TRIM_MEMORY_RUNNING_CRITICAL";
    private static ObserverList<MemoryPressureCallback> sCallbacks;

    interface Natives {
        void onMemoryPressure(int i);
    }

    private static void addNativeCallback() {
        ThreadUtils.assertOnUiThread();
        addCallback(new MemoryPressureCallback() { // from class: aegon.chrome.base.-$$Lambda$MemoryPressureListener$t_XWHqkj5h4coRH2d5Enh-npQec
            @Override // aegon.chrome.base.memory.MemoryPressureCallback
            public final void onPressure(int i) {
                MemoryPressureListenerJni.get().onMemoryPressure(i);
            }
        });
    }

    public static void addCallback(MemoryPressureCallback memoryPressureCallback) {
        ThreadUtils.assertOnUiThread();
        if (sCallbacks == null) {
            sCallbacks = new ObserverList<>();
        }
        sCallbacks.addObserver(memoryPressureCallback);
    }

    public static void removeCallback(MemoryPressureCallback memoryPressureCallback) {
        ThreadUtils.assertOnUiThread();
        ObserverList<MemoryPressureCallback> observerList = sCallbacks;
        if (observerList == null) {
            return;
        }
        observerList.removeObserver(memoryPressureCallback);
    }

    public static void notifyMemoryPressure(int i) {
        ThreadUtils.assertOnUiThread();
        ObserverList<MemoryPressureCallback> observerList = sCallbacks;
        if (observerList == null) {
            return;
        }
        Iterator<MemoryPressureCallback> itIterator2 = observerList.iterator2();
        while (itIterator2.hasNext()) {
            itIterator2.mo35924next().onPressure(i);
        }
    }

    public static boolean handleDebugIntent(Activity activity, String str) {
        ThreadUtils.assertOnUiThread();
        if (ACTION_LOW_MEMORY.equals(str)) {
            simulateLowMemoryPressureSignal(activity);
            return true;
        }
        if (ACTION_TRIM_MEMORY.equals(str)) {
            simulateTrimMemoryPressureSignal(activity, 80);
            return true;
        }
        if (ACTION_TRIM_MEMORY_RUNNING_CRITICAL.equals(str)) {
            simulateTrimMemoryPressureSignal(activity, 15);
            return true;
        }
        if (!ACTION_TRIM_MEMORY_MODERATE.equals(str)) {
            return false;
        }
        simulateTrimMemoryPressureSignal(activity, 60);
        return true;
    }

    private static void simulateLowMemoryPressureSignal(Activity activity) {
        activity.getApplication().onLowMemory();
        activity.onLowMemory();
    }

    private static void simulateTrimMemoryPressureSignal(Activity activity, int i) {
        activity.getApplication().onTrimMemory(i);
        activity.onTrimMemory(i);
    }
}
