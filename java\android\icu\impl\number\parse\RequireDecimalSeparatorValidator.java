package android.icu.impl.number.parse;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class RequireDecimalSeparatorValidator extends ValidationMatcher {

    /* renamed from: A */
    private static final RequireDecimalSeparatorValidator f83A = new RequireDecimalSeparatorValidator(true);

    /* renamed from: B */
    private static final RequireDecimalSeparatorValidator f84B = new RequireDecimalSeparatorValidator(false);
    private final boolean patternHasDecimalSeparator;

    public static RequireDecimalSeparatorValidator getInstance(boolean patternHasDecimalSeparator) {
        return patternHasDecimalSeparator ? f83A : f84B;
    }

    private RequireDecimalSeparatorValidator(boolean patternHasDecimalSeparator) {
        this.patternHasDecimalSeparator = patternHasDecimalSeparator;
    }

    @Override // android.icu.impl.number.parse.NumberParseMatcher
    public void postProcess(ParsedNumber result) {
        boolean parseHasDecimalSeparator = (result.flags & 32) != 0;
        if (parseHasDecimalSeparator != this.patternHasDecimalSeparator) {
            result.flags |= 256;
        }
    }

    public String toString() {
        return "<RequireDecimalSeparator>";
    }
}
