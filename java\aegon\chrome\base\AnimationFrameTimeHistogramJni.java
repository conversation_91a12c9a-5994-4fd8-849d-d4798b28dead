package aegon.chrome.base;

import aegon.chrome.base.AnimationFrameTimeHistogram;
import p001K.C0000S;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final class AnimationFrameTimeHistogramJni implements AnimationFrameTimeHistogram.Natives {
    public static final JniStaticTestMocker<AnimationFrameTimeHistogram.Natives> TEST_HOOKS = new JniStaticTestMocker<AnimationFrameTimeHistogram.Natives>() { // from class: aegon.chrome.base.AnimationFrameTimeHistogramJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(AnimationFrameTimeHistogram.Natives natives) {
            AnimationFrameTimeHistogram.Natives unused = AnimationFrameTimeHistogramJni.testInstance = natives;
        }
    };
    private static AnimationFrameTimeHistogram.Natives testInstance;

    AnimationFrameTimeHistogramJni() {
    }

    @Override // aegon.chrome.base.AnimationFrameTimeHistogram.Natives
    public final void saveHistogram(String str, long[] jArr, int i) {
        C0000S.M7xB0tc0(str, jArr, i);
    }

    public static AnimationFrameTimeHistogram.Natives get() {
        if (C0000S.a) {
            AnimationFrameTimeHistogram.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (C0000S.b) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.AnimationFrameTimeHistogram.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        return new AnimationFrameTimeHistogramJni();
    }
}
