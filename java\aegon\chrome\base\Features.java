package aegon.chrome.base;

import aegon.chrome.base.annotations.JNINamespace;
import aegon.chrome.base.annotations.MainDex;

@MainDex
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public abstract class Features {
    private final String mName;

    interface Natives {
        boolean getFieldTrialParamByFeatureAsBoolean(long j, String str, boolean z);

        boolean isEnabled(long j);
    }

    protected abstract long getFeaturePointer();

    protected Features(String str) {
        this.mName = str;
    }

    public String getName() {
        return this.mName;
    }

    public boolean isEnabled() {
        return FeaturesJni.get().isEnabled(getFeaturePointer());
    }

    public boolean getFieldTrialParamByFeatureAsBoolean(String str, boolean z) {
        return FeaturesJni.get().getFieldTrialParamByFeatureAsBoolean(getFeaturePointer(), str, z);
    }
}
