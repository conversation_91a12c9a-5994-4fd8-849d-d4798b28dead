package aegon.chrome.net.impl;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.net.impl.CronetLibraryLoader;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class CronetLibraryLoaderJni implements CronetLibraryLoader.Natives {
    public static final JniStaticTestMocker<CronetLibraryLoader.Natives> TEST_HOOKS = new JniStaticTestMocker<CronetLibraryLoader.Natives>() { // from class: aegon.chrome.net.impl.CronetLibraryLoaderJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(CronetLibraryLoader.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                CronetLibraryLoader.Natives unused = CronetLibraryLoaderJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static CronetLibraryLoader.Natives testInstance;

    CronetLibraryLoaderJni() {
    }

    @Override // aegon.chrome.net.impl.CronetLibraryLoader.Natives
    public void cronetInitOnInitThread() {
        GEN_JNI.org_chromium_net_impl_CronetLibraryLoader_cronetInitOnInitThread();
    }

    @Override // aegon.chrome.net.impl.CronetLibraryLoader.Natives
    public String getCronetVersion() {
        return GEN_JNI.org_chromium_net_impl_CronetLibraryLoader_getCronetVersion();
    }

    public static CronetLibraryLoader.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            CronetLibraryLoader.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.net.impl.CronetLibraryLoader.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new CronetLibraryLoaderJni();
    }
}
