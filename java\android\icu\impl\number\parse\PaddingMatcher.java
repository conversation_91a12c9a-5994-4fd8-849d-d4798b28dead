package android.icu.impl.number.parse;

import android.icu.impl.StringSegment;
import android.icu.impl.number.parse.NumberParseMatcher;
import android.icu.text.UnicodeSet;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class PaddingMatcher extends SymbolMatcher implements NumberParseMatcher.Flexible {
    public static PaddingMatcher getInstance(String padString) {
        return new PaddingMatcher(padString);
    }

    private PaddingMatcher(String symbolString) {
        super(symbolString, UnicodeSet.EMPTY);
    }

    @Override // android.icu.impl.number.parse.SymbolMatcher
    protected boolean isDisabled(ParsedNumber result) {
        return false;
    }

    @Override // android.icu.impl.number.parse.SymbolMatcher
    protected void accept(StringSegment segment, ParsedNumber result) {
    }

    public String toString() {
        return "<PaddingMatcher>";
    }
}
