package android.icu.impl.number;

import java.p654io.IOException;
import java.p654io.ObjectInputStream;
import java.p654io.ObjectOutputStream;
import java.p654io.Serializable;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class Properties implements Serializable {
    private static final long serialVersionUID = 4095518955889349243L;
    private transient DecimalFormatProperties instance;

    public DecimalFormatProperties getInstance() {
        return this.instance;
    }

    private void readObject(ObjectInputStream ois) throws IOException, ClassNotFoundException {
        if (this.instance == null) {
            this.instance = new DecimalFormatProperties();
        }
        this.instance.readObjectImpl(ois);
    }

    private void writeObject(ObjectOutputStream oos) throws IOException {
        if (this.instance == null) {
            this.instance = new DecimalFormatProperties();
        }
        this.instance.writeObjectImpl(oos);
    }
}
