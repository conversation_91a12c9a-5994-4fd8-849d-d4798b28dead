package android.icu.impl.duration;

import android.icu.impl.duration.BasicPeriodBuilderFactory;

/* compiled from: BasicPeriodBuilderFactory.java */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
class MultiUnitBuilder extends PeriodBuilderImpl {
    private int nPeriods;

    MultiUnitBuilder(int nPeriods, BasicPeriodBuilderFactory.Settings settings) {
        super(settings);
        this.nPeriods = nPeriods;
    }

    public static MultiUnitBuilder get(int nPeriods, BasicPeriodBuilderFactory.Settings settings) {
        if (nPeriods > 0 && settings != null) {
            return new MultiUnitBuilder(nPeriods, settings);
        }
        return null;
    }

    @Override // android.icu.impl.duration.PeriodBuilderImpl
    protected PeriodBuilder withSettings(BasicPeriodBuilderFactory.Settings settingsToUse) {
        return get(this.nPeriods, settingsToUse);
    }

    @Override // android.icu.impl.duration.PeriodBuilderImpl
    protected Period handleCreate(long duration, long referenceDate, boolean inPast) {
        short uset = this.settings.effectiveSet();
        int n = 0;
        Period period = null;
        long duration2 = duration;
        for (int i = 0; i < TimeUnit.units.length; i++) {
            if (((1 << i) & uset) != 0) {
                TimeUnit unit = TimeUnit.units[i];
                if (n == this.nPeriods) {
                    break;
                }
                long unitDuration = approximateDurationOf(unit);
                if (duration2 >= unitDuration || n > 0) {
                    n++;
                    double count = duration2 / unitDuration;
                    if (n < this.nPeriods) {
                        count = Math.floor(count);
                        duration2 -= (long) (unitDuration * count);
                    }
                    period = period == null ? Period.m89at((float) count, unit).inPast(inPast) : period.and((float) count, unit);
                }
            }
        }
        return period;
    }
}
