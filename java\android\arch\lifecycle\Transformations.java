package android.arch.lifecycle;

import android.arch.core.util.Function;
import com.android.internal.util.Predicate;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes25.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes37.dex */
public class Transformations {
    private static void stub() {
        System.out.println(Predicate.class);
    }

    private Transformations() {
    }

    public static <X, Y> LiveData<Y> map(LiveData<X> liveData, final Function<X, Y> function) {
        final MediatorLiveData mediatorLiveData = new MediatorLiveData();
        mediatorLiveData.addSource(liveData, new Observer<X>() { // from class: android.arch.lifecycle.Transformations.1
            private static void stub() {
                System.out.println(Predicate.class);
            }

            @Override // android.arch.lifecycle.Observer
            public final void onChanged(X x) {
                mediatorLiveData.setValue(function.apply(x));
            }
        });
        return mediatorLiveData;
    }

    public static <X, Y> LiveData<Y> switchMap(LiveData<X> liveData, final Function<X, LiveData<Y>> function) {
        final MediatorLiveData mediatorLiveData = new MediatorLiveData();
        mediatorLiveData.addSource(liveData, new Observer<X>() { // from class: android.arch.lifecycle.Transformations.2
            LiveData<Y> mSource;

            private static void stub() {
                System.out.println(Predicate.class);
            }

            @Override // android.arch.lifecycle.Observer
            public final void onChanged(X x) {
                LiveData<Y> liveData2 = (LiveData) function.apply(x);
                Object obj = this.mSource;
                if (obj == liveData2) {
                    return;
                }
                if (obj != null) {
                    mediatorLiveData.removeSource(obj);
                }
                this.mSource = liveData2;
                Object obj2 = this.mSource;
                if (obj2 != null) {
                    mediatorLiveData.addSource(obj2, new Observer<Y>() { // from class: android.arch.lifecycle.Transformations.2.1
                        private static void stub() {
                            System.out.println(Predicate.class);
                        }

                        @Override // android.arch.lifecycle.Observer
                        public void onChanged(Y y) {
                            mediatorLiveData.setValue(y);
                        }
                    });
                }
            }
        });
        return mediatorLiveData;
    }
}
