package aegon.chrome.base;

import aegon.chrome.base.ThreadUtils;
import aegon.chrome.base.lifetime.DestroyChecker;
import android.os.Handler;
import android.os.Looper;
import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class UnownedUserDataHost {
    private final DestroyChecker mDestroyChecker;
    private Handler mHandler;
    private final ThreadUtils.ThreadChecker mThreadChecker;
    private HashMap<UnownedUserDataKey<?>, WeakReference<? extends UnownedUserData>> mUnownedUserDataMap;

    private static Looper retrieveNonNullLooperOrThrow() {
        Looper looperMyLooper = Looper.myLooper();
        if (looperMyLooper != null) {
            return looperMyLooper;
        }
        throw new IllegalStateException();
    }

    public UnownedUserDataHost() {
        this(new Handler(retrieveNonNullLooperOrThrow()));
    }

    UnownedUserDataHost(Handler handler) {
        this.mThreadChecker = new ThreadUtils.ThreadChecker();
        this.mDestroyChecker = new DestroyChecker();
        this.mUnownedUserDataMap = new HashMap<>();
        this.mHandler = handler;
    }

    final <T extends UnownedUserData> void set(UnownedUserDataKey<T> unownedUserDataKey, T t) {
        checkState();
        if (this.mUnownedUserDataMap.containsKey(unownedUserDataKey) && !t.equals(get(unownedUserDataKey))) {
            unownedUserDataKey.detachFromHost(this);
        }
        this.mUnownedUserDataMap.put(unownedUserDataKey, new WeakReference<>(t));
    }

    final <T extends UnownedUserData> T get(UnownedUserDataKey<T> unownedUserDataKey) {
        checkState();
        WeakReference<? extends UnownedUserData> weakReference = this.mUnownedUserDataMap.get(unownedUserDataKey);
        if (weakReference == null) {
            return null;
        }
        UnownedUserData unownedUserData = weakReference.get();
        if (unownedUserData == null) {
            unownedUserDataKey.detachFromHost(this);
            return null;
        }
        return unownedUserDataKey.getValueClass().cast(unownedUserData);
    }

    final <T extends UnownedUserData> void remove(UnownedUserDataKey<T> unownedUserDataKey) {
        final UnownedUserData unownedUserData;
        checkState();
        WeakReference<? extends UnownedUserData> weakReferenceRemove = this.mUnownedUserDataMap.remove(unownedUserDataKey);
        if (weakReferenceRemove == null || (unownedUserData = weakReferenceRemove.get()) == null || !unownedUserData.informOnDetachmentFromHost()) {
            return;
        }
        this.mHandler.post(new Runnable() { // from class: aegon.chrome.base.-$$Lambda$UnownedUserDataHost$fk1HL8GPeUFcgk92Nawh5_7vpEc
            @Override // java.lang.Runnable
            public final void run() {
                this.f$0.lambda$remove$0$UnownedUserDataHost(unownedUserData);
            }
        });
    }

    public /* synthetic */ void lambda$remove$0$UnownedUserDataHost(UnownedUserData unownedUserData) {
        unownedUserData.onDetachedFromHost(this);
    }

    public final void destroy() {
        this.mThreadChecker.assertOnValidThread();
        if (this.mDestroyChecker.isDestroyed()) {
            return;
        }
        Iterator<E> it = new HashSet(this.mUnownedUserDataMap.keySet()).iterator();
        while (it.hasNext()) {
            ((UnownedUserDataKey) it.mo35924next()).detachFromHost(this);
        }
        this.mUnownedUserDataMap = null;
        this.mHandler = null;
        this.mDestroyChecker.destroy();
    }

    final int getMapSize() {
        checkState();
        return this.mUnownedUserDataMap.size();
    }

    final boolean isDestroyed() {
        return this.mDestroyChecker.isDestroyed();
    }

    private void checkState() {
        this.mThreadChecker.assertOnValidThread();
        this.mDestroyChecker.checkNotDestroyed();
    }
}
