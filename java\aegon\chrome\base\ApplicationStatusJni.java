package aegon.chrome.base;

import aegon.chrome.base.ApplicationStatus;
import aegon.chrome.base.natives.GEN_JNI;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class ApplicationStatusJni implements ApplicationStatus.Natives {
    public static final JniStaticTestMocker<ApplicationStatus.Natives> TEST_HOOKS = new JniStaticTestMocker<ApplicationStatus.Natives>() { // from class: aegon.chrome.base.ApplicationStatusJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(ApplicationStatus.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                ApplicationStatus.Natives unused = ApplicationStatusJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static ApplicationStatus.Natives testInstance;

    ApplicationStatusJni() {
    }

    @Override // aegon.chrome.base.ApplicationStatus.Natives
    public void onApplicationStateChange(int i) {
        GEN_JNI.org_chromium_base_ApplicationStatus_onApplicationStateChange(i);
    }

    public static ApplicationStatus.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            ApplicationStatus.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.ApplicationStatus.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new ApplicationStatusJni();
    }
}
