D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\java\com\example\yqbz_test\AdvancedAdBlocker.java:231: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            if (className.toLowerCase().contains(keyword.toLowerCase())) {
                          ~~~~~~~~~~~
D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\java\com\example\yqbz_test\AdvancedAdBlocker.java:231: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            if (className.toLowerCase().contains(keyword.toLowerCase())) {
                                                         ~~~~~~~~~~~
D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\java\com\example\yqbz_test\AdvancedAdBlocker.java:246: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            if (className.toLowerCase().contains(keyword.toLowerCase())) {
                          ~~~~~~~~~~~
D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\java\com\example\yqbz_test\AdvancedAdBlocker.java:246: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            if (className.toLowerCase().contains(keyword.toLowerCase())) {
                                                         ~~~~~~~~~~~
D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\java\com\example\yqbz_test\AdvancedAdBlocker.java:276: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerUrl = url.toLowerCase();
                              ~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

D:\桌面\元气桌面壁纸\yqbz_test\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.11.1 is available: 8.13.0 [AndroidGradlePluginVersion]
agp = "8.11.1"
      ~~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

D:\桌面\元气桌面壁纸\yqbz_test\gradle\libs.versions.toml:7: Warning: A newer version of com.google.android.material:material than 1.12.0 is available: 1.13.0 [GradleDependency]
material = "1.12.0"
           ~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\java\com\example\yqbz_test\MainActivity.java:19: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        textView.setText("元气桌面壁纸广告屏蔽模块\n\n" +
                         ^

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

0 errors, 8 warnings
