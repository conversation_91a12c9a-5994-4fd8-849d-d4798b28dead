package aegon.chrome.base;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@Retention(RetentionPolicy.SOURCE)
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public @interface ApplicationState {
    public static final int HAS_DESTROYED_ACTIVITIES = 4;
    public static final int HAS_PAUSED_ACTIVITIES = 2;
    public static final int HAS_RUNNING_ACTIVITIES = 1;
    public static final int HAS_STOPPED_ACTIVITIES = 3;
    public static final int UNKNOWN = 0;
}
