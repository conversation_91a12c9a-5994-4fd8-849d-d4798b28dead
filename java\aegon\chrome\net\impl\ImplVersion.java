package aegon.chrome.net.impl;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class ImplVersion {
    private static final int API_LEVEL = 14;
    private static final String CRONET_VERSION = "95.0.4638.74";
    private static final String LAST_CHANGE = "aa0b5bfba697c178b700768b00eb29c36a1a7b7e-refs/heads/main@{#934970}";

    public static int getApiLevel() {
        return 14;
    }

    public static String getCronetVersion() {
        return CRONET_VERSION;
    }

    public static String getLastChange() {
        return LAST_CHANGE;
    }

    private ImplVersion() {
    }

    public static String getCronetVersionWithLastChange() {
        return "95.0.4638.74@aa0b5bfb";
    }
}
