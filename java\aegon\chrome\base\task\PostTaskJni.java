package aegon.chrome.base.task;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.base.task.PostTask;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class PostTaskJni implements PostTask.Natives {
    public static final JniStaticTestMocker<PostTask.Natives> TEST_HOOKS = new JniStaticTestMocker<PostTask.Natives>() { // from class: aegon.chrome.base.task.PostTaskJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(PostTask.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                PostTask.Natives unused = PostTaskJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static PostTask.Natives testInstance;

    PostTaskJni() {
    }

    @Override // aegon.chrome.base.task.PostTask.Natives
    public void postDelayedTask(int i, boolean z, boolean z2, byte b2, byte[] bArr, Runnable runnable, long j, String str) {
        GEN_JNI.org_chromium_base_task_PostTask_postDelayedTask(i, z, z2, b2, bArr, runnable, j, str);
    }

    public static PostTask.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            PostTask.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.task.PostTask.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new PostTaskJni();
    }
}
