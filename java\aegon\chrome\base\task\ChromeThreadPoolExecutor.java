package aegon.chrome.base.task;

import aegon.chrome.base.task.AsyncTask;
import aegon.chrome.base.task.ChromeThreadPoolExecutor;
import aegon.chrome.build.BuildConfig;
import android.os.Process;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class ChromeThreadPoolExecutor extends ThreadPoolExecutor {
    private static final int KEEP_ALIVE_SECONDS = 30;
    private static final int RUNNABLE_WARNING_COUNT = 32;
    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();
    private static final int CORE_POOL_SIZE = Math.max(2, Math.min(CPU_COUNT - 1, 4));
    private static final int MAXIMUM_POOL_SIZE = (CPU_COUNT * 2) + 1;
    private static final ThreadFactory sThreadFactory = new ThreadFactoryC00511();
    private static final BlockingQueue<Runnable> sPoolWorkQueue = new ArrayBlockingQueue(128);

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    /* renamed from: aegon.chrome.base.task.ChromeThreadPoolExecutor$1 */
    class ThreadFactoryC00511 implements ThreadFactory {
        private final AtomicInteger mCount = new AtomicInteger(1);

        ThreadFactoryC00511() {
        }

        @Override // java.util.concurrent.ThreadFactory
        public Thread newThread(final Runnable runnable) {
            return new Thread(new Runnable() { // from class: aegon.chrome.base.task.-$$Lambda$ChromeThreadPoolExecutor$1$v5_TSBR6TVxXuTFRFwyMsUxd_f8
                @Override // java.lang.Runnable
                public final void run() throws SecurityException, IllegalArgumentException {
                    ChromeThreadPoolExecutor.ThreadFactoryC00511.lambda$newThread$0(runnable);
                }
            }, "CrAsyncTask #" + this.mCount.getAndIncrement());
        }

        static /* synthetic */ void lambda$newThread$0(Runnable runnable) throws SecurityException, IllegalArgumentException {
            Process.setThreadPriority(10);
            runnable.run();
        }
    }

    ChromeThreadPoolExecutor() {
        this(CORE_POOL_SIZE, MAXIMUM_POOL_SIZE, 30L, TimeUnit.SECONDS, sPoolWorkQueue, sThreadFactory);
    }

    ChromeThreadPoolExecutor(int i, int i2, long j, TimeUnit timeUnit, BlockingQueue<Runnable> blockingQueue, ThreadFactory threadFactory) {
        super(i, i2, j, timeUnit, blockingQueue, threadFactory);
        allowCoreThreadTimeOut(true);
    }

    private static String getClassName(Runnable runnable) {
        Class blamedClass = runnable.getClass();
        try {
            if (blamedClass == AsyncTask.NamedFutureTask.class) {
                blamedClass = ((AsyncTask.NamedFutureTask) runnable).getBlamedClass();
            } else if (blamedClass.getEnclosingClass() == android.os.AsyncTask.class) {
                Field declaredField = blamedClass.getDeclaredField("this$0");
                declaredField.setAccessible(true);
                blamedClass = declaredField.get(runnable).getClass();
            }
        } catch (IllegalAccessException e) {
            if (BuildConfig.ENABLE_ASSERTS) {
                throw new RuntimeException(e);
            }
        } catch (NoSuchFieldException e2) {
            if (BuildConfig.ENABLE_ASSERTS) {
                throw new RuntimeException(e2);
            }
        }
        return blamedClass.getName();
    }

    /* JADX WARN: Multi-variable type inference failed */
    private Map<String, Integer> getNumberOfClassNameOccurrencesInQueue() {
        HashMap map = new HashMap();
        for (Runnable runnable : (Runnable[]) getQueue().toArray(new Runnable[0])) {
            String className = getClassName(runnable);
            map.put(className, Integer.valueOf((map.containsKey(className) ? ((Integer) map.get(className)).intValue() : 0) + 1));
        }
        return map;
    }

    private String findClassNamesWithTooManyRunnables(Map<String, Integer> map) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            if (entry.getValue().intValue() > 32) {
                sb.append(entry.getKey());
                sb.append(' ');
            }
        }
        return sb.length() == 0 ? "NO CLASSES FOUND" : sb.toString();
    }

    @Override // java.util.concurrent.ThreadPoolExecutor, java.util.concurrent.Executor
    public void execute(Runnable runnable) {
        try {
            super.execute(runnable);
        } catch (RejectedExecutionException e) {
            throw new RejectedExecutionException("Prominent classes in AsyncTask: " + findClassNamesWithTooManyRunnables(getNumberOfClassNameOccurrencesInQueue()), e);
        }
    }
}
