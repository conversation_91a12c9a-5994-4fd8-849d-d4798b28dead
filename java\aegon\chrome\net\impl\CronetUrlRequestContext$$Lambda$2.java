package aegon.chrome.net.impl;

import aegon.chrome.net.impl.SafeNativeFunctionCaller;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class CronetUrlRequestContext$$Lambda$2 implements SafeNativeFunctionCaller.Supplier {
    private final CronetEngineBuilderImpl arg$1;

    private CronetUrlRequestContext$$Lambda$2(CronetEngineBuilderImpl cronetEngineBuilderImpl) {
        this.arg$1 = cronetEngineBuilderImpl;
    }

    public static SafeNativeFunctionCaller.Supplier lambdaFactory$(CronetEngineBuilderImpl cronetEngineBuilderImpl) {
        return new CronetUrlRequestContext$$Lambda$2(cronetEngineBuilderImpl);
    }

    @Override // aegon.chrome.net.impl.SafeNativeFunctionCaller.Supplier
    public final Object get() {
        return Long.valueOf(CronetUrlRequestContextJni.get().createRequestContextAdapter(CronetUrlRequestContext.createNativeUrlRequestContextConfig(this.arg$1)));
    }
}
