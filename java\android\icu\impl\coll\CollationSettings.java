package android.icu.impl.coll;

import aegon.chrome.net.NetError;
import android.net.wifi.hotspot2.pps.UpdateParameter;
import java.util.Arrays;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationSettings extends SharedObject {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    static final int ALTERNATE_MASK = 12;
    public static final int BACKWARD_SECONDARY = 2048;
    public static final int CASE_FIRST = 512;
    public static final int CASE_FIRST_AND_UPPER_MASK = 768;
    public static final int CASE_LEVEL = 1024;
    public static final int CHECK_FCD = 1;
    private static final int[] EMPTY_INT_ARRAY = new int[0];
    static final int MAX_VARIABLE_MASK = 112;
    static final int MAX_VARIABLE_SHIFT = 4;
    static final int MAX_VAR_CURRENCY = 3;
    static final int MAX_VAR_PUNCT = 1;
    static final int MAX_VAR_SPACE = 0;
    static final int MAX_VAR_SYMBOL = 2;
    public static final int NUMERIC = 2;
    static final int SHIFTED = 4;
    static final int STRENGTH_MASK = 61440;
    static final int STRENGTH_SHIFT = 12;
    static final int UPPER_FIRST = 256;
    long minHighNoReorder;
    long[] reorderRanges;
    public byte[] reorderTable;
    public long variableTop;
    public int options = 8208;
    public int[] reorderCodes = EMPTY_INT_ARRAY;
    public int fastLatinOptions = -1;
    public char[] fastLatinPrimaries = new char[384];

    CollationSettings() {
    }

    @Override // android.icu.impl.coll.SharedObject
    /* renamed from: clone */
    public CollationSettings mo35917clone() {
        CollationSettings newSettings = (CollationSettings) super.mo35917clone();
        newSettings.fastLatinPrimaries = (char[]) this.fastLatinPrimaries.clone();
        return newSettings;
    }

    public boolean equals(Object other) {
        if (other == null || !getClass().equals(other.getClass())) {
            return false;
        }
        CollationSettings o = (CollationSettings) other;
        int i = this.options;
        if (i != o.options) {
            return false;
        }
        if (((i & 12) != 0 && this.variableTop != o.variableTop) || !Arrays.equals(this.reorderCodes, o.reorderCodes)) {
            return false;
        }
        return true;
    }

    public int hashCode() {
        int i = this.options;
        int h = i << 8;
        if ((i & 12) != 0) {
            h = (int) (h ^ this.variableTop);
        }
        int h2 = this.reorderCodes.length ^ h;
        int i2 = 0;
        while (true) {
            int[] iArr = this.reorderCodes;
            if (i2 < iArr.length) {
                h2 ^= iArr[i2] << i2;
                i2++;
            } else {
                return h2;
            }
        }
    }

    public void resetReordering() {
        this.reorderTable = null;
        this.minHighNoReorder = 0L;
        this.reorderRanges = null;
        this.reorderCodes = EMPTY_INT_ARRAY;
    }

    void aliasReordering(CollationData data, int[] codesAndRanges, int codesLength, byte[] table) {
        int[] codes;
        if (codesLength == codesAndRanges.length) {
            codes = codesAndRanges;
        } else {
            codes = Arrays.copyOf(codesAndRanges, codesLength);
        }
        int rangesLimit = codesAndRanges.length;
        int rangesLength = rangesLimit - codesLength;
        if (table != null && (rangesLength != 0 ? !(rangesLength < 2 || (codesAndRanges[codesLength] & 65535) != 0 || (codesAndRanges[rangesLimit - 1] & 65535) == 0) : !reorderTableHasSplitBytes(table))) {
            this.reorderTable = table;
            this.reorderCodes = codes;
            int firstSplitByteRangeIndex = codesLength;
            while (firstSplitByteRangeIndex < rangesLimit && (codesAndRanges[firstSplitByteRangeIndex] & 16711680) == 0) {
                firstSplitByteRangeIndex++;
            }
            if (firstSplitByteRangeIndex == rangesLimit) {
                this.minHighNoReorder = 0L;
                this.reorderRanges = null;
                return;
            } else {
                this.minHighNoReorder = codesAndRanges[rangesLimit - 1] & Collation.MAX_PRIMARY;
                setReorderRanges(codesAndRanges, firstSplitByteRangeIndex, rangesLimit - firstSplitByteRangeIndex);
                return;
            }
        }
        setReordering(data, codes);
    }

    public void setReordering(CollationData data, int[] codes) {
        int rangesLength;
        if (codes.length != 0) {
            int rangesStart = 0;
            if (codes.length != 1 || codes[0] != 103) {
                UVector32 rangesList = new UVector32();
                data.makeReorderRanges(codes, rangesList);
                int rangesLength2 = rangesList.size();
                if (rangesLength2 == 0) {
                    resetReordering();
                    return;
                }
                int[] ranges = rangesList.getBuffer();
                this.minHighNoReorder = ranges[rangesLength2 - 1] & Collation.MAX_PRIMARY;
                byte[] table = new byte[256];
                int b2 = 0;
                int firstSplitByteRangeIndex = -1;
                for (int i = 0; i < rangesLength2; i++) {
                    int pair = ranges[i];
                    int limit1 = pair >>> 24;
                    while (b2 < limit1) {
                        table[b2] = (byte) (b2 + pair);
                        b2++;
                    }
                    if ((16711680 & pair) != 0) {
                        table[limit1] = 0;
                        b2 = limit1 + 1;
                        if (firstSplitByteRangeIndex < 0) {
                            firstSplitByteRangeIndex = i;
                        }
                    }
                }
                for (int b3 = b2; b3 <= 255; b3++) {
                    table[b3] = (byte) b3;
                }
                if (firstSplitByteRangeIndex < 0) {
                    rangesLength = 0;
                } else {
                    rangesStart = firstSplitByteRangeIndex;
                    rangesLength = rangesLength2 - firstSplitByteRangeIndex;
                }
                setReorderArrays(codes, ranges, rangesStart, rangesLength, table);
                return;
            }
        }
        resetReordering();
    }

    private void setReorderArrays(int[] codes, int[] ranges, int rangesStart, int rangesLength, byte[] table) {
        if (codes == null) {
            codes = EMPTY_INT_ARRAY;
        }
        this.reorderTable = table;
        this.reorderCodes = codes;
        setReorderRanges(ranges, rangesStart, rangesLength);
    }

    private void setReorderRanges(int[] ranges, int rangesStart, int rangesLength) {
        if (rangesLength == 0) {
            this.reorderRanges = null;
            return;
        }
        this.reorderRanges = new long[rangesLength];
        int i = 0;
        while (true) {
            int i2 = i + 1;
            int rangesStart2 = rangesStart + 1;
            this.reorderRanges[i] = ranges[rangesStart] & UpdateParameter.UPDATE_CHECK_INTERVAL_NEVER;
            if (i2 < rangesLength) {
                i = i2;
                rangesStart = rangesStart2;
            } else {
                return;
            }
        }
    }

    public void copyReorderingFrom(CollationSettings other) {
        if (!other.hasReordering()) {
            resetReordering();
            return;
        }
        this.minHighNoReorder = other.minHighNoReorder;
        this.reorderTable = other.reorderTable;
        this.reorderRanges = other.reorderRanges;
        this.reorderCodes = other.reorderCodes;
    }

    public boolean hasReordering() {
        return this.reorderTable != null;
    }

    private static boolean reorderTableHasSplitBytes(byte[] table) {
        for (int i = 1; i < 256; i++) {
            if (table[i] == 0) {
                return true;
            }
        }
        return false;
    }

    public long reorder(long p) {
        byte b2 = this.reorderTable[((int) p) >>> 24];
        if (b2 != 0 || p <= 1) {
            return ((b2 & 255) << 24) | (16777215 & p);
        }
        return reorderEx(p);
    }

    private long reorderEx(long p) {
        if (p >= this.minHighNoReorder) {
            return p;
        }
        long q = 65535 | p;
        int i = 0;
        while (true) {
            long r = this.reorderRanges[i];
            if (q < r) {
                return (((short) r) << 24) + p;
            }
            i++;
        }
    }

    public void setStrength(int value) {
        int noStrength = this.options & (-61441);
        if (value == 0 || value == 1 || value == 2 || value == 3 || value == 15) {
            this.options = (value << 12) | noStrength;
            return;
        }
        throw new IllegalArgumentException("illegal strength value " + value);
    }

    public void setStrengthDefault(int defaultOptions) {
        int noStrength = this.options & (-61441);
        this.options = (STRENGTH_MASK & defaultOptions) | noStrength;
    }

    static int getStrength(int options) {
        return options >> 12;
    }

    public int getStrength() {
        return getStrength(this.options);
    }

    public void setFlag(int bit, boolean value) {
        if (value) {
            this.options |= bit;
        } else {
            this.options &= ~bit;
        }
    }

    public void setFlagDefault(int bit, int defaultOptions) {
        this.options = (this.options & (~bit)) | (defaultOptions & bit);
    }

    public boolean getFlag(int bit) {
        return (this.options & bit) != 0;
    }

    public void setCaseFirst(int value) {
        int noCaseFirst = this.options & (-769);
        this.options = noCaseFirst | value;
    }

    public void setCaseFirstDefault(int defaultOptions) {
        int noCaseFirst = this.options & (-769);
        this.options = (defaultOptions & 768) | noCaseFirst;
    }

    public int getCaseFirst() {
        return this.options & 768;
    }

    public void setAlternateHandlingShifted(boolean value) {
        int noAlternate = this.options & (-13);
        if (value) {
            this.options = noAlternate | 4;
        } else {
            this.options = noAlternate;
        }
    }

    public void setAlternateHandlingDefault(int defaultOptions) {
        int noAlternate = this.options & (-13);
        this.options = (defaultOptions & 12) | noAlternate;
    }

    public boolean getAlternateHandling() {
        return (this.options & 12) != 0;
    }

    public void setMaxVariable(int value, int defaultOptions) {
        int noMax = this.options & NetError.ERR_SSL_VERSION_OR_CIPHER_MISMATCH;
        if (value == -1) {
            this.options = (defaultOptions & 112) | noMax;
            return;
        }
        if (value == 0 || value == 1 || value == 2 || value == 3) {
            this.options = (value << 4) | noMax;
            return;
        }
        throw new IllegalArgumentException("illegal maxVariable value " + value);
    }

    public int getMaxVariable() {
        return (this.options & 112) >> 4;
    }

    static boolean isTertiaryWithCaseBits(int options) {
        return (options & 1536) == 512;
    }

    static int getTertiaryMask(int options) {
        if (isTertiaryWithCaseBits(options)) {
            return 65343;
        }
        return Collation.ONLY_TERTIARY_MASK;
    }

    static boolean sortsTertiaryUpperCaseFirst(int options) {
        return (options & 1792) == 768;
    }

    public boolean dontCheckFCD() {
        return (this.options & 1) == 0;
    }

    boolean hasBackwardSecondary() {
        return (this.options & 2048) != 0;
    }

    public boolean isNumeric() {
        return (this.options & 2) != 0;
    }
}
