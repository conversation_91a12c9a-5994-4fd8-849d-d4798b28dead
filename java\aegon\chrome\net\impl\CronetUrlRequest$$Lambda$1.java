package aegon.chrome.net.impl;

import aegon.chrome.net.impl.SafeNativeFunctionCaller;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class CronetUrlRequest$$Lambda$1 implements SafeNativeFunctionCaller.Supplier {
    private final CronetUrlRequest arg$1;

    private CronetUrlRequest$$Lambda$1(CronetUrlRequest cronetUrlRequest) {
        this.arg$1 = cronetUrlRequest;
    }

    public static SafeNativeFunctionCaller.Supplier lambdaFactory$(CronetUrlRequest cronetUrlRequest) {
        return new CronetUrlRequest$$Lambda$1(cronetUrlRequest);
    }

    @Override // aegon.chrome.net.impl.SafeNativeFunctionCaller.Supplier
    public final Object get() {
        return CronetUrlRequest.lambda$start$0(this.arg$1);
    }
}
