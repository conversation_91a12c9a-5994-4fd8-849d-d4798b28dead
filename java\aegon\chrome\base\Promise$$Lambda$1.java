package aegon.chrome.base;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class Promise$$Lambda$1 implements Callback {
    private static final Promise$$Lambda$1 instance = new Promise$$Lambda$1();

    private Promise$$Lambda$1() {
    }

    @Override // aegon.chrome.base.Callback
    public final void onResult(Object obj) {
        Promise.lambda$then$0((Exception) obj);
    }
}
