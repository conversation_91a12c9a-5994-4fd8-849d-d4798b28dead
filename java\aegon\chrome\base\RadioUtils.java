package aegon.chrome.base;

import aegon.chrome.base.annotations.JNINamespace;
import aegon.chrome.base.compat.ApiHelperForM;
import aegon.chrome.base.compat.ApiHelperForP;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.os.Build;
import android.os.Process;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;

@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class RadioUtils {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static Boolean sHaveAccessNetworkState;
    private static Boolean sHaveAccessWifiState;

    private RadioUtils() {
    }

    private static boolean isSupported() {
        return Build.VERSION.SDK_INT >= 28 && haveAccessNetworkState() && haveAccessWifiState();
    }

    private static boolean haveAccessNetworkState() {
        if (sHaveAccessNetworkState == null) {
            sHaveAccessNetworkState = Boolean.valueOf(ApiCompatibilityUtils.checkPermission(ContextUtils.getApplicationContext(), "android.permission.ACCESS_NETWORK_STATE", Process.myPid(), Process.myUid()) == 0);
        }
        return sHaveAccessNetworkState.booleanValue();
    }

    private static boolean haveAccessWifiState() {
        if (sHaveAccessWifiState == null) {
            sHaveAccessWifiState = Boolean.valueOf(ApiCompatibilityUtils.checkPermission(ContextUtils.getApplicationContext(), "android.permission.ACCESS_WIFI_STATE", Process.myPid(), Process.myUid()) == 0);
        }
        return sHaveAccessWifiState.booleanValue();
    }

    private static boolean isWifiConnected() {
        NetworkCapabilities networkCapabilities;
        ConnectivityManager connectivityManager = (ConnectivityManager) ContextUtils.getApplicationContext().getSystemService("connectivity");
        Network activeNetwork = ApiHelperForM.getActiveNetwork(connectivityManager);
        if (activeNetwork == null || (networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)) == null) {
            return false;
        }
        return networkCapabilities.hasTransport(1);
    }

    private static int getCellSignalLevel() {
        try {
            SignalStrength signalStrength = ApiHelperForP.getSignalStrength((TelephonyManager) ContextUtils.getApplicationContext().getSystemService("phone"));
            if (signalStrength != null) {
                return signalStrength.getLevel();
            }
            return -1;
        } catch (SecurityException unused) {
            return -1;
        }
    }

    private static int getCellDataActivity() {
        try {
            return ((TelephonyManager) ContextUtils.getApplicationContext().getSystemService("phone")).getDataActivity();
        } catch (SecurityException unused) {
            return -1;
        }
    }
}
