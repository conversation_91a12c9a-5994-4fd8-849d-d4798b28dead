package aegon.chrome.net;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.net.NetworkActivationRequest;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class NetworkActivationRequestJni implements NetworkActivationRequest.Natives {
    public static final JniStaticTestMocker<NetworkActivationRequest.Natives> TEST_HOOKS = new JniStaticTestMocker<NetworkActivationRequest.Natives>() { // from class: aegon.chrome.net.NetworkActivationRequestJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(NetworkActivationRequest.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                NetworkActivationRequest.Natives unused = NetworkActivationRequestJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static NetworkActivationRequest.Natives testInstance;

    NetworkActivationRequestJni() {
    }

    @Override // aegon.chrome.net.NetworkActivationRequest.Natives
    public void notifyAvailable(long j, long j2) {
        GEN_JNI.org_chromium_net_NetworkActivationRequest_notifyAvailable(j, j2);
    }

    public static NetworkActivationRequest.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            NetworkActivationRequest.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.net.NetworkActivationRequest.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new NetworkActivationRequestJni();
    }
}
