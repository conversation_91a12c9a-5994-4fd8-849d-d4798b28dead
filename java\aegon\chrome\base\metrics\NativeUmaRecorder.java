package aegon.chrome.base.metrics;

import aegon.chrome.base.annotations.JNINamespace;
import aegon.chrome.base.annotations.MainDex;
import android.os.SystemClock;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@MainDex
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
final class NativeUmaRecorder implements UmaRecorder {
    private final Map<String, Long> mNativeHints = Collections.synchronizedMap(new HashMap());

    interface Natives {
        long recordBooleanHistogram(String str, long j, boolean z);

        long recordExponentialHistogram(String str, long j, int i, int i2, int i3, int i4);

        long recordLinearHistogram(String str, long j, int i, int i2, int i3, int i4);

        long recordSparseHistogram(String str, long j, int i);

        void recordUserAction(String str, long j);
    }

    NativeUmaRecorder() {
    }

    @Override // aegon.chrome.base.metrics.UmaRecorder
    public final void recordBooleanHistogram(String str, boolean z) {
        long nativeHint = getNativeHint(str);
        maybeUpdateNativeHint(str, nativeHint, NativeUmaRecorderJni.get().recordBooleanHistogram(str, nativeHint, z));
    }

    @Override // aegon.chrome.base.metrics.UmaRecorder
    public final void recordExponentialHistogram(String str, int i, int i2, int i3, int i4) {
        long nativeHint = getNativeHint(str);
        maybeUpdateNativeHint(str, nativeHint, NativeUmaRecorderJni.get().recordExponentialHistogram(str, nativeHint, i, i2, i3, i4));
    }

    @Override // aegon.chrome.base.metrics.UmaRecorder
    public final void recordLinearHistogram(String str, int i, int i2, int i3, int i4) {
        long nativeHint = getNativeHint(str);
        maybeUpdateNativeHint(str, nativeHint, NativeUmaRecorderJni.get().recordLinearHistogram(str, nativeHint, i, i2, i3, i4));
    }

    @Override // aegon.chrome.base.metrics.UmaRecorder
    public final void recordSparseHistogram(String str, int i) {
        long nativeHint = getNativeHint(str);
        maybeUpdateNativeHint(str, nativeHint, NativeUmaRecorderJni.get().recordSparseHistogram(str, nativeHint, i));
    }

    @Override // aegon.chrome.base.metrics.UmaRecorder
    public final void recordUserAction(String str, long j) {
        NativeUmaRecorderJni.get().recordUserAction(str, SystemClock.elapsedRealtime() - j);
    }

    private long getNativeHint(String str) {
        Long l = this.mNativeHints.get(str);
        if (l == null) {
            return 0L;
        }
        return l.longValue();
    }

    private void maybeUpdateNativeHint(String str, long j, long j2) {
        if (j != j2) {
            this.mNativeHints.put(str, Long.valueOf(j2));
        }
    }
}
