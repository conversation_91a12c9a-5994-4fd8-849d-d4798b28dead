package android.arch.lifecycle;

import android.app.Application;
import com.android.internal.util.Predicate;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes25.dex
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes31.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes37.dex */
public class AndroidViewModel extends ViewModel {
    private Application mApplication;

    private static void stub() {
        System.out.println(Predicate.class);
    }

    public AndroidViewModel(Application application) {
        this.mApplication = application;
    }

    public <T extends Application> T getApplication() {
        return (T) this.mApplication;
    }
}
