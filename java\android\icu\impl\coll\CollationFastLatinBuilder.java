package android.icu.impl.coll;

import android.icu.util.CharsTrie;
import com.kuaishou.socket.nano.SocketMessages;
import com.kwai.video.player.kwai_player.FileUtils;
import java.lang.reflect.Array;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
final class CollationFastLatinBuilder {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final long CONTRACTION_FLAG = 2147483648L;
    private static final int NUM_SPECIAL_GROUPS = 4;
    private long[][] charCEs = (long[][]) Array.newInstance((Class<?>) long.class, FileUtils.S_IRWXU, 2);
    long[] lastSpecialPrimaries = new long[4];
    private StringBuilder result = new StringBuilder();
    private long ce0 = 0;
    private long ce1 = 0;
    private UVector64 contractionCEs = new UVector64();
    private UVector64 uniqueCEs = new UVector64();
    private char[] miniCEs = null;
    private long firstDigitPrimary = 0;
    private long firstLatinPrimary = 0;
    private long lastLatinPrimary = 0;
    private long firstShortPrimary = 0;
    private boolean shortPrimaryOverflow = false;
    private int headerLength = 0;

    private static final int compareInt64AsUnsigned(long a2, long b2) {
        long a3 = a2 - Long.MIN_VALUE;
        long b3 = b2 - Long.MIN_VALUE;
        if (a3 < b3) {
            return -1;
        }
        if (a3 > b3) {
            return 1;
        }
        return 0;
    }

    private static final int binarySearch(long[] list, int limit, long ce) {
        if (limit == 0) {
            return -1;
        }
        int start = 0;
        while (true) {
            int i = (int) ((start + limit) / 2);
            int cmp = compareInt64AsUnsigned(ce, list[i]);
            if (cmp == 0) {
                return i;
            }
            if (cmp < 0) {
                if (i == start) {
                    return ~start;
                }
                limit = i;
            } else {
                if (i == start) {
                    return ~(start + 1);
                }
                start = i;
            }
        }
    }

    CollationFastLatinBuilder() {
    }

    boolean forData(CollationData data) {
        if (this.result.length() != 0) {
            throw new IllegalStateException("attempt to reuse a CollationFastLatinBuilder");
        }
        if (!loadGroups(data)) {
            return false;
        }
        this.firstShortPrimary = this.firstDigitPrimary;
        getCEs(data);
        encodeUniqueCEs();
        if (this.shortPrimaryOverflow) {
            this.firstShortPrimary = this.firstLatinPrimary;
            resetCEs();
            getCEs(data);
            encodeUniqueCEs();
        }
        boolean ok = !this.shortPrimaryOverflow;
        if (ok) {
            encodeCharCEs();
            encodeContractions();
        }
        this.contractionCEs.removeAllElements();
        this.uniqueCEs.removeAllElements();
        return ok;
    }

    char[] getHeader() {
        int i = this.headerLength;
        char[] resultArray = new char[i];
        this.result.getChars(0, i, resultArray, 0);
        return resultArray;
    }

    char[] getTable() {
        int length = this.result.length();
        int i = this.headerLength;
        char[] resultArray = new char[length - i];
        StringBuilder sb = this.result;
        sb.getChars(i, sb.length(), resultArray, 0);
        return resultArray;
    }

    private boolean loadGroups(CollationData data) {
        this.headerLength = 5;
        int r0 = 5 | 512;
        this.result.append((char) r0);
        for (int i = 0; i < 4; i++) {
            this.lastSpecialPrimaries[i] = data.getLastPrimaryForGroup(i + 4096);
            if (this.lastSpecialPrimaries[i] == 0) {
                return false;
            }
            this.result.append(0);
        }
        this.firstDigitPrimary = data.getFirstPrimaryForGroup(4100);
        this.firstLatinPrimary = data.getFirstPrimaryForGroup(25);
        this.lastLatinPrimary = data.getLastPrimaryForGroup(25);
        return (this.firstDigitPrimary == 0 || this.firstLatinPrimary == 0) ? false : true;
    }

    private boolean inSameGroup(long p, long q) {
        long j = this.firstShortPrimary;
        if (p >= j) {
            return q >= j;
        }
        if (q >= j) {
            return false;
        }
        long lastVariablePrimary = this.lastSpecialPrimaries[3];
        if (p > lastVariablePrimary) {
            return q > lastVariablePrimary;
        }
        if (q > lastVariablePrimary) {
            return false;
        }
        int i = 0;
        while (true) {
            long lastPrimary = this.lastSpecialPrimaries[i];
            if (p <= lastPrimary) {
                return q <= lastPrimary;
            }
            if (q <= lastPrimary) {
                return false;
            }
            i++;
        }
    }

    private void resetCEs() {
        this.contractionCEs.removeAllElements();
        this.uniqueCEs.removeAllElements();
        this.shortPrimaryOverflow = false;
        this.result.setLength(this.headerLength);
    }

    private void getCEs(CollationData data) {
        char c2;
        int ce32;
        CollationData d2;
        char c3 = 0;
        int i = 0;
        while (true) {
            if (c3 == 384) {
                c2 = 8192;
            } else if (c3 != 8256) {
                c2 = c3;
            } else {
                this.contractionCEs.addElement(511L);
                return;
            }
            int ce322 = data.getCE32(c2);
            if (ce322 == 192) {
                CollationData d3 = data.base;
                ce32 = d3.getCE32(c2);
                d2 = d3;
            } else {
                ce32 = ce322;
                d2 = data;
            }
            if (getCEsFromCE32(d2, c2, ce32)) {
                long[][] jArr = this.charCEs;
                long[] jArr2 = jArr[i];
                long j = this.ce0;
                jArr2[0] = j;
                jArr[i][1] = this.ce1;
                addUniqueCE(j);
                addUniqueCE(this.ce1);
            } else {
                long[][] jArr3 = this.charCEs;
                long[] jArr4 = jArr3[i];
                this.ce0 = Collation.NO_CE;
                jArr4[0] = 4311744768L;
                long[] jArr5 = jArr3[i];
                this.ce1 = 0L;
                jArr5[1] = 0;
            }
            if (c2 == 0 && !isContractionCharCE(this.ce0)) {
                addContractionEntry(511, this.ce0, this.ce1);
                long[][] jArr6 = this.charCEs;
                jArr6[0][0] = 6442450944L;
                jArr6[0][1] = 0;
            }
            i++;
            c3 = (char) (c2 + 1);
        }
    }

    private boolean getCEsFromCE32(CollationData data, int c2, int ce32) {
        int ce322 = data.getFinalCE32(ce32);
        this.ce1 = 0L;
        if (Collation.isSimpleOrLongCE32(ce322)) {
            this.ce0 = Collation.ceFromCE32(ce322);
        } else {
            int iTagFromCE32 = Collation.tagFromCE32(ce322);
            if (iTagFromCE32 == 4) {
                this.ce0 = Collation.latinCE0FromCE32(ce322);
                this.ce1 = Collation.latinCE1FromCE32(ce322);
            } else if (iTagFromCE32 == 5) {
                int index = Collation.indexFromCE32(ce322);
                int length = Collation.lengthFromCE32(ce322);
                if (length > 2) {
                    return false;
                }
                this.ce0 = Collation.ceFromCE32(data.ce32s[index]);
                if (length == 2) {
                    this.ce1 = Collation.ceFromCE32(data.ce32s[index + 1]);
                }
            } else if (iTagFromCE32 == 6) {
                int index2 = Collation.indexFromCE32(ce322);
                int length2 = Collation.lengthFromCE32(ce322);
                if (length2 > 2) {
                    return false;
                }
                this.ce0 = data.ces[index2];
                if (length2 == 2) {
                    this.ce1 = data.ces[index2 + 1];
                }
            } else {
                if (iTagFromCE32 == 9) {
                    return getCEsFromContractionCE32(data, ce322);
                }
                if (iTagFromCE32 != 14) {
                    return false;
                }
                this.ce0 = data.getCEFromOffsetCE32(c2, ce322);
            }
        }
        long j = this.ce0;
        if (j == 0) {
            return this.ce1 == 0;
        }
        long p0 = j >>> 32;
        if (p0 == 0 || p0 > this.lastLatinPrimary) {
            return false;
        }
        int lower32_0 = (int) j;
        if (p0 < this.firstShortPrimary) {
            int sc0 = lower32_0 & (-16384);
            if (sc0 != 83886080) {
                return false;
            }
        }
        int sc02 = lower32_0 & Collation.ONLY_TERTIARY_MASK;
        if (sc02 < 1280) {
            return false;
        }
        long j2 = this.ce1;
        if (j2 != 0) {
            long p1 = j2 >>> 32;
            if (p1 != 0 ? !inSameGroup(p0, p1) : p0 < this.firstShortPrimary) {
                return false;
            }
            int lower32_1 = (int) this.ce1;
            if ((lower32_1 >>> 16) == 0) {
                return false;
            }
            if (p1 != 0 && p1 < this.firstShortPrimary) {
                int sc1 = lower32_1 & (-16384);
                if (sc1 != 83886080) {
                    return false;
                }
            }
            if ((lower32_0 & Collation.ONLY_TERTIARY_MASK) < 1280) {
                return false;
            }
        }
        return ((this.ce0 | this.ce1) & 192) == 0;
    }

    private boolean getCEsFromContractionCE32(CollationData data, int ce32) {
        int i;
        boolean z;
        int trieIndex = Collation.indexFromCE32(ce32);
        int ce322 = data.getCE32FromContexts(trieIndex);
        int contractionIndex = this.contractionCEs.size();
        if (getCEsFromCE32(data, -1, ce322)) {
            addContractionEntry(511, this.ce0, this.ce1);
        } else {
            addContractionEntry(511, Collation.NO_CE, 0L);
        }
        int i2 = 0;
        CharsTrie.Iterator suffixes = CharsTrie.iterator(data.contexts, trieIndex + 2, 0);
        boolean addContraction = false;
        int prevX = -1;
        while (suffixes.hasNext()) {
            CharsTrie.Entry entry = suffixes.mo35924next();
            CharSequence suffix = entry.chars;
            int x = CollationFastLatin.getCharIndex(suffix.charAt(i2));
            if (x >= 0) {
                if (x == prevX) {
                    if (addContraction) {
                        addContractionEntry(x, Collation.NO_CE, 0L);
                        addContraction = false;
                        i2 = 0;
                    }
                } else {
                    if (addContraction) {
                        i = 1;
                        addContractionEntry(prevX, this.ce0, this.ce1);
                    } else {
                        i = 1;
                    }
                    int ce323 = entry.value;
                    if (suffix.length() == i && getCEsFromCE32(data, -1, ce323)) {
                        z = true;
                    } else {
                        addContractionEntry(x, Collation.NO_CE, 0L);
                        z = false;
                    }
                    addContraction = z;
                    prevX = x;
                    i2 = 0;
                }
            }
            i2 = 0;
        }
        if (addContraction) {
            addContractionEntry(prevX, this.ce0, this.ce1);
        }
        this.ce0 = 6442450944L | contractionIndex;
        this.ce1 = 0L;
        return true;
    }

    private void addContractionEntry(int x, long cce0, long cce1) {
        this.contractionCEs.addElement(x);
        this.contractionCEs.addElement(cce0);
        this.contractionCEs.addElement(cce1);
        addUniqueCE(cce0);
        addUniqueCE(cce1);
    }

    private void addUniqueCE(long ce) {
        long ce2;
        int i;
        if (ce != 0 && (ce >>> 32) != 1 && (i = binarySearch(this.uniqueCEs.getBuffer(), this.uniqueCEs.size(), (ce2 = ce & (-49153)))) < 0) {
            this.uniqueCEs.insertElementAt(ce2, ~i);
        }
    }

    private int getMiniCE(long ce) {
        int index = binarySearch(this.uniqueCEs.getBuffer(), this.uniqueCEs.size(), ce & (-49153));
        return this.miniCEs[index];
    }

    /* JADX WARN: Removed duplicated region for block: B:62:0x00f5  */
    /* JADX WARN: Removed duplicated region for block: B:68:0x010a  */
    /* JADX WARN: Removed duplicated region for block: B:71:0x0119  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private void encodeUniqueCEs() {
        /*
            Method dump skipped, instructions count: 300
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationFastLatinBuilder.encodeUniqueCEs():void");
    }

    private void encodeCharCEs() {
        int miniCEsStart = this.result.length();
        for (int i = 0; i < 448; i++) {
            this.result.append(0);
        }
        int indexBase = this.result.length();
        for (int i2 = 0; i2 < 448; i2++) {
            long ce = this.charCEs[i2][0];
            if (!isContractionCharCE(ce)) {
                int miniCE = encodeTwoCEs(ce, this.charCEs[i2][1]);
                if ((miniCE >>> 16) > 0) {
                    int expansionIndex = this.result.length() - indexBase;
                    if (expansionIndex > 1023) {
                        miniCE = 1;
                    } else {
                        StringBuilder sb = this.result;
                        sb.append((char) (miniCE >> 16));
                        sb.append((char) miniCE);
                        miniCE = expansionIndex | 2048;
                    }
                }
                this.result.setCharAt(miniCEsStart + i2, (char) miniCE);
            }
        }
    }

    private void encodeContractions() {
        int i;
        long ce;
        int i2 = this.headerLength;
        int i3 = FileUtils.S_IRWXU;
        int indexBase = i2 + FileUtils.S_IRWXU;
        int firstContractionIndex = this.result.length();
        int i4 = 0;
        while (i4 < i3) {
            long ce2 = this.charCEs[i4][0];
            if (isContractionCharCE(ce2)) {
                int contractionIndex = this.result.length() - indexBase;
                if (contractionIndex > 1023) {
                    this.result.setCharAt(this.headerLength + i4, (char) 1);
                } else {
                    boolean firstTriple = true;
                    int index = ((int) ce2) & Integer.MAX_VALUE;
                    while (true) {
                        long x = this.contractionCEs.elementAti(index);
                        if (x == 511 && !firstTriple) {
                            break;
                        }
                        long cce0 = this.contractionCEs.elementAti(index + 1);
                        int index2 = index;
                        long cce1 = this.contractionCEs.elementAti(index + 2);
                        int miniCE = encodeTwoCEs(cce0, cce1);
                        if (miniCE == 1) {
                            i = i4;
                            ce = ce2;
                            this.result.append((char) (x | 512));
                        } else {
                            i = i4;
                            ce = ce2;
                            int i5 = miniCE >>> 16;
                            if (i5 == 0) {
                                this.result.append((char) (1024 | x));
                                this.result.append((char) miniCE);
                            } else {
                                this.result.append((char) (1536 | x));
                                StringBuilder sb = this.result;
                                sb.append((char) (miniCE >> 16));
                                sb.append((char) miniCE);
                            }
                        }
                        firstTriple = false;
                        index = index2 + 3;
                        i4 = i;
                        ce2 = ce;
                        i3 = FileUtils.S_IRWXU;
                    }
                    this.result.setCharAt(this.headerLength + i4, (char) (contractionIndex | 1024));
                }
            }
            i4++;
        }
        if (this.result.length() > firstContractionIndex) {
            this.result.append((char) 511);
        }
    }

    private int encodeTwoCEs(long first, long second) {
        if (first == 0) {
            return 0;
        }
        if (first == Collation.NO_CE) {
            return 1;
        }
        int miniCE = getMiniCE(first);
        if (miniCE == 1) {
            return miniCE;
        }
        if (miniCE >= 4096) {
            int c2 = (((int) first) & Collation.CASE_MASK) >> 11;
            miniCE |= c2 + 8;
        }
        if (second == 0) {
            return miniCE;
        }
        int miniCE1 = getMiniCE(second);
        if (miniCE1 == 1) {
            return miniCE1;
        }
        int case1 = ((int) second) & Collation.CASE_MASK;
        if (miniCE >= 4096 && (miniCE & SocketMessages.PayloadType.SC_LIVE_BLIND_DATE_CHAT_SERVICE_NOTICE) == 160) {
            int sec1 = miniCE1 & SocketMessages.PayloadType.SC_LIVE_BLIND_DATE_CHAT_SERVICE_NOTICE;
            int ter1 = miniCE1 & 7;
            if (sec1 >= 384 && case1 == 0 && ter1 == 0) {
                return (miniCE & (-993)) | sec1;
            }
        }
        if (miniCE1 <= 992 || 4096 <= miniCE1) {
            miniCE1 |= (case1 >> 11) + 8;
        }
        return (miniCE << 16) | miniCE1;
    }

    private static boolean isContractionCharCE(long ce) {
        return (ce >>> 32) == 1 && ce != Collation.NO_CE;
    }
}
