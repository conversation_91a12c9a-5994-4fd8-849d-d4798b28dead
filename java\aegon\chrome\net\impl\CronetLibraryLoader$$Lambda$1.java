package aegon.chrome.net.impl;

import aegon.chrome.net.impl.SafeNativeFunctionCaller;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class CronetLibraryLoader$$Lambda$1 implements SafeNativeFunctionCaller.Supplier {
    private static final CronetLibraryLoader$$Lambda$1 instance = new CronetLibraryLoader$$Lambda$1();

    private CronetLibraryLoader$$Lambda$1() {
    }

    @Override // aegon.chrome.net.impl.SafeNativeFunctionCaller.Supplier
    public final Object get() {
        return CronetLibraryLoaderJni.get().getCronetVersion();
    }
}
