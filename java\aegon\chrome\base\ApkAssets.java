package aegon.chrome.base;

import aegon.chrome.base.annotations.JNINamespace;
import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.icu.text.PluralRules;
import android.text.TextUtils;
import java.p654io.IOException;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class ApkAssets {
    private static final String LOGTAG = "ApkAssets";

    public static long[] open(String str, String str2) throws IOException {
        AssetFileDescriptor assetFileDescriptorOpenNonAssetFd = null;
        try {
            try {
                Context applicationContext = ContextUtils.getApplicationContext();
                if (!TextUtils.isEmpty(str2) && BundleUtils.isIsolatedSplitInstalled(applicationContext, str2)) {
                    applicationContext = BundleUtils.createIsolatedSplitContext(applicationContext, str2);
                }
                assetFileDescriptorOpenNonAssetFd = applicationContext.getAssets().openNonAssetFd(str);
                long[] jArr = {assetFileDescriptorOpenNonAssetFd.getParcelFileDescriptor().detachFd(), assetFileDescriptorOpenNonAssetFd.getStartOffset(), assetFileDescriptorOpenNonAssetFd.getLength()};
                if (assetFileDescriptorOpenNonAssetFd != null) {
                    try {
                        assetFileDescriptorOpenNonAssetFd.close();
                    } catch (IOException e) {
                        android.util.Log.e(LOGTAG, "Unable to close AssetFileDescriptor", e);
                    }
                }
                return jArr;
            } catch (IOException e2) {
                if (!e2.getMessage().equals("") && !e2.getMessage().equals(str)) {
                    android.util.Log.e(LOGTAG, "Error while loading asset " + str + PluralRules.KEYWORD_RULE_SEPARATOR + ((Object) e2));
                }
                long[] jArr2 = {-1, -1, -1};
                if (assetFileDescriptorOpenNonAssetFd != null) {
                    try {
                        assetFileDescriptorOpenNonAssetFd.close();
                    } catch (IOException e3) {
                        android.util.Log.e(LOGTAG, "Unable to close AssetFileDescriptor", e3);
                    }
                }
                return jArr2;
            }
        } catch (Throwable th) {
            if (assetFileDescriptorOpenNonAssetFd != null) {
                try {
                    assetFileDescriptorOpenNonAssetFd.close();
                } catch (IOException e4) {
                    android.util.Log.e(LOGTAG, "Unable to close AssetFileDescriptor", e4);
                }
            }
            throw th;
        }
    }
}
