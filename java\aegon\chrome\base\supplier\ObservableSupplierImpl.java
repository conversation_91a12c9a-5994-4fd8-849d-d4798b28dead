package aegon.chrome.base.supplier;

import aegon.chrome.base.Callback;
import aegon.chrome.base.ObserverList;
import aegon.chrome.base.supplier.Supplier;
import android.os.Handler;
import java.util.Iterator;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class ObservableSupplierImpl<E> implements ObservableSupplier<E> {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static boolean sIgnoreThreadChecksForTesting;
    private E mObject;
    private final Thread mThread = Thread.currentThread();
    private final Handler mHandler = new Handler();
    private final ObserverList<Callback<E>> mObservers = new ObserverList<>();

    private void checkThread() {
    }

    @Override // aegon.chrome.base.supplier.Supplier
    public /* synthetic */ boolean hasValue() {
        return Supplier.CC.$default$hasValue(this);
    }

    @Override // aegon.chrome.base.supplier.ObservableSupplier
    public E addObserver(final Callback<E> callback) {
        checkThread();
        this.mObservers.addObserver(callback);
        final E e = this.mObject;
        if (e != null) {
            this.mHandler.post(new Runnable() { // from class: aegon.chrome.base.supplier.-$$Lambda$ObservableSupplierImpl$OURVq0IAvcy0jMRxUEpn4Ny1zP4
                @Override // java.lang.Runnable
                public final void run() {
                    this.f$0.lambda$addObserver$0$ObservableSupplierImpl(e, callback);
                }
            });
        }
        return this.mObject;
    }

    public /* synthetic */ void lambda$addObserver$0$ObservableSupplierImpl(Object obj, Callback callback) {
        if (this.mObject == obj && this.mObservers.hasObserver(callback)) {
            callback.onResult(this.mObject);
        }
    }

    @Override // aegon.chrome.base.supplier.ObservableSupplier
    public void removeObserver(Callback<E> callback) {
        checkThread();
        this.mObservers.removeObserver(callback);
    }

    public void set(E e) {
        checkThread();
        if (e == this.mObject) {
            return;
        }
        this.mObject = e;
        Iterator<Callback<E>> itIterator2 = this.mObservers.iterator2();
        while (itIterator2.hasNext()) {
            itIterator2.mo35924next().onResult(this.mObject);
        }
    }

    @Override // aegon.chrome.base.supplier.Supplier
    public E get() {
        checkThread();
        return this.mObject;
    }

    public static void setIgnoreThreadChecksForTesting(boolean z) {
        sIgnoreThreadChecksForTesting = z;
    }
}
