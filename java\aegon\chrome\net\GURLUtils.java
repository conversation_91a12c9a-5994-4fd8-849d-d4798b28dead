package aegon.chrome.net;

import aegon.chrome.base.annotations.JNINamespace;
import com.kwad.components.offline.api.p367tk.model.report.TKDownloadReason;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace(TKDownloadReason.KSAD_TK_NET)
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class GURLUtils {

    public interface Natives {
        String getOrigin(String str);
    }

    public static String getOrigin(String str) {
        return GURLUtilsJni.get().getOrigin(str);
    }
}
