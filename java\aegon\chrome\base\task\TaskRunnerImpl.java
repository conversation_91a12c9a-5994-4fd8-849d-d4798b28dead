package aegon.chrome.base.task;

import aegon.chrome.base.TraceEvent;
import aegon.chrome.base.annotations.JNINamespace;
import android.os.Process;
import android.util.Pair;
import androidx.media3.extractor.text.ttml.TtmlNode;
import java.lang.ref.ReferenceQueue;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace(TtmlNode.RUBY_BASE)
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class TaskRunnerImpl implements TaskRunner {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private boolean mDidOneTimeInitialization;
    private volatile long mNativeTaskRunnerAndroid;
    private List<Pair<Runnable, Long>> mPreNativeDelayedTasks;
    private final Object mPreNativeTaskLock;
    private LinkedList<Runnable> mPreNativeTasks;
    protected final Runnable mRunPreNativeTaskClosure;
    private final int mTaskRunnerType;
    private final TaskTraits mTaskTraits;
    private final String mTraceEvent;
    private static final ReferenceQueue<Object> sQueue = new ReferenceQueue<>();
    private static final Set<TaskRunnerCleaner> sCleaners = new HashSet();

    interface Natives {
        boolean belongsToCurrentThread(long j);

        void destroy(long j);

        long init(int i, int i2, boolean z, boolean z2, byte b2, byte[] bArr);

        void postDelayedTask(long j, Runnable runnable, long j2, String str);
    }

    static class TaskRunnerCleaner extends WeakReference<TaskRunnerImpl> {
        final long mNativePtr;

        TaskRunnerCleaner(TaskRunnerImpl taskRunnerImpl) {
            super(taskRunnerImpl, TaskRunnerImpl.sQueue);
            this.mNativePtr = taskRunnerImpl.mNativeTaskRunnerAndroid;
        }

        void destroy() {
            TaskRunnerImplJni.get().destroy(this.mNativePtr);
        }
    }

    private static void destroyGarbageCollectedTaskRunners() {
        while (true) {
            TaskRunnerCleaner taskRunnerCleaner = (TaskRunnerCleaner) sQueue.poll();
            if (taskRunnerCleaner == null) {
                return;
            }
            taskRunnerCleaner.destroy();
            synchronized (sCleaners) {
                sCleaners.remove(taskRunnerCleaner);
            }
        }
    }

    TaskRunnerImpl(TaskTraits taskTraits) {
        this(taskTraits, "TaskRunnerImpl", 0);
        destroyGarbageCollectedTaskRunners();
    }

    protected TaskRunnerImpl(TaskTraits taskTraits, String str, int i) {
        this.mRunPreNativeTaskClosure = new Runnable() { // from class: aegon.chrome.base.task.-$$Lambda$qtzG3qWtv-B-GdSre5H9UUiGel0
            @Override // java.lang.Runnable
            public final void run() {
                this.f$0.runPreNativeTask();
            }
        };
        this.mPreNativeTaskLock = new Object();
        this.mTaskTraits = taskTraits.withExplicitDestination();
        this.mTraceEvent = str + ".PreNativeTask.run";
        this.mTaskRunnerType = i;
    }

    @Override // aegon.chrome.base.task.TaskRunner
    public void postTask(Runnable runnable) {
        postDelayedTask(runnable, 0L);
    }

    @Override // aegon.chrome.base.task.TaskRunner
    public void postDelayedTask(Runnable runnable, long j) {
        if (this.mNativeTaskRunnerAndroid != 0) {
            TaskRunnerImplJni.get().postDelayedTask(this.mNativeTaskRunnerAndroid, runnable, j, runnable.getClass().getName());
            return;
        }
        synchronized (this.mPreNativeTaskLock) {
            oneTimeInitialization();
            if (this.mNativeTaskRunnerAndroid != 0) {
                TaskRunnerImplJni.get().postDelayedTask(this.mNativeTaskRunnerAndroid, runnable, j, runnable.getClass().getName());
                return;
            }
            if (j == 0) {
                this.mPreNativeTasks.add(runnable);
                schedulePreNativeTask();
            } else {
                this.mPreNativeDelayedTasks.add(new Pair<>(runnable, Long.valueOf(j)));
            }
        }
    }

    protected Boolean belongsToCurrentThreadInternal() {
        synchronized (this.mPreNativeTaskLock) {
            oneTimeInitialization();
        }
        if (this.mNativeTaskRunnerAndroid == 0) {
            return null;
        }
        return Boolean.valueOf(TaskRunnerImplJni.get().belongsToCurrentThread(this.mNativeTaskRunnerAndroid));
    }

    private void oneTimeInitialization() {
        if (this.mDidOneTimeInitialization) {
            return;
        }
        this.mDidOneTimeInitialization = true;
        if (!PostTask.registerPreNativeTaskRunner(this)) {
            initNativeTaskRunner();
        } else {
            this.mPreNativeTasks = new LinkedList<>();
            this.mPreNativeDelayedTasks = new ArrayList();
        }
    }

    protected void schedulePreNativeTask() {
        PostTask.getPrenativeThreadPoolExecutor().execute(this.mRunPreNativeTaskClosure);
    }

    protected void runPreNativeTask() {
        TraceEvent traceEventScoped = TraceEvent.scoped(this.mTraceEvent);
        try {
            synchronized (this.mPreNativeTaskLock) {
                if (this.mPreNativeTasks == null) {
                    if (traceEventScoped != null) {
                        traceEventScoped.close();
                        return;
                    }
                    return;
                }
                Runnable runnablePoll = this.mPreNativeTasks.poll();
                int i = this.mTaskTraits.mPriority;
                if (i == 1) {
                    Process.setThreadPriority(0);
                } else if (i == 2) {
                    Process.setThreadPriority(-1);
                } else {
                    Process.setThreadPriority(10);
                }
                runnablePoll.run();
                if (traceEventScoped != null) {
                    traceEventScoped.close();
                }
            }
        } catch (Throwable th) {
            if (traceEventScoped != null) {
                try {
                    traceEventScoped.close();
                } catch (Throwable unused) {
                }
            }
            throw th;
        }
    }

    void initNativeTaskRunner() {
        long jInit = TaskRunnerImplJni.get().init(this.mTaskRunnerType, this.mTaskTraits.mPriority, this.mTaskTraits.mMayBlock, this.mTaskTraits.mUseThreadPool, this.mTaskTraits.mExtensionId, this.mTaskTraits.mExtensionData);
        synchronized (this.mPreNativeTaskLock) {
            if (this.mPreNativeTasks != null) {
                Iterator<Runnable> it = this.mPreNativeTasks.iterator2();
                while (it.hasNext()) {
                    Runnable runnableMo35924next = it.mo35924next();
                    TaskRunnerImplJni.get().postDelayedTask(jInit, runnableMo35924next, 0L, runnableMo35924next.getClass().getName());
                }
                this.mPreNativeTasks = null;
            }
            if (this.mPreNativeDelayedTasks != null) {
                for (Pair<Runnable, Long> pair : this.mPreNativeDelayedTasks) {
                    TaskRunnerImplJni.get().postDelayedTask(jInit, (Runnable) pair.first, ((Long) pair.second).longValue(), pair.getClass().getName());
                }
                this.mPreNativeDelayedTasks = null;
            }
            this.mNativeTaskRunnerAndroid = jInit;
        }
        synchronized (sCleaners) {
            sCleaners.add(new TaskRunnerCleaner(this));
        }
        destroyGarbageCollectedTaskRunners();
    }
}
