package aegon.chrome.base;

import aegon.chrome.base.memory.MemoryPressureCallback;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class MemoryPressureListener$$Lambda$1 implements MemoryPressureCallback {
    private static final MemoryPressureListener$$Lambda$1 instance = new MemoryPressureListener$$Lambda$1();

    private MemoryPressureListener$$Lambda$1() {
    }

    @Override // aegon.chrome.base.memory.MemoryPressureCallback
    public final void onPressure(int i) {
        MemoryPressureListener.access$lambda$0(i);
    }
}
