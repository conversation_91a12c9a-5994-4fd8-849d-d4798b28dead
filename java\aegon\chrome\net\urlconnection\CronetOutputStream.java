package aegon.chrome.net.urlconnection;

import aegon.chrome.net.UploadDataProvider;
import java.p654io.IOException;
import java.p654io.OutputStream;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
abstract class CronetOutputStream extends OutputStream {
    private boolean mClosed;
    private IOException mException;
    private boolean mRequestCompleted;

    abstract void checkReceivedEnoughContent();

    abstract UploadDataProvider getUploadDataProvider();

    abstract void setConnected();

    CronetOutputStream() {
    }

    @Override // java.p654io.OutputStream, java.p654io.Closeable, java.lang.AutoCloseable
    public void close() {
        this.mClosed = true;
    }

    void setRequestCompleted(IOException iOException) {
        this.mException = iOException;
        this.mRequestCompleted = true;
    }

    protected void checkNotClosed() throws IOException {
        if (this.mRequestCompleted) {
            checkNoException();
            throw new IOException("Writing after request completed.");
        }
        if (this.mClosed) {
            throw new IOException("Stream has been closed.");
        }
    }

    protected void checkNoException() throws IOException {
        IOException iOException = this.mException;
        if (iOException != null) {
            throw iOException;
        }
    }
}
