package aegon.chrome.base.task;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class TaskRunnerImpl$$Lambda$1 implements Runnable {
    private final TaskRunnerImpl arg$1;

    private TaskRunnerImpl$$Lambda$1(TaskRunnerImpl taskRunnerImpl) {
        this.arg$1 = taskRunnerImpl;
    }

    public static Runnable lambdaFactory$(TaskRunnerImpl taskRunnerImpl) {
        return new TaskRunnerImpl$$Lambda$1(taskRunnerImpl);
    }

    @Override // java.lang.Runnable
    public final void run() {
        this.arg$1.runPreNativeTask();
    }
}
