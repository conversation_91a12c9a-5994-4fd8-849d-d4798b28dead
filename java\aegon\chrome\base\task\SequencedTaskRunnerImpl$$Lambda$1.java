package aegon.chrome.base.task;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class SequencedTaskRunnerImpl$$Lambda$1 implements Runnable {
    private final SequencedTaskRunnerImpl arg$1;
    private final Runnable arg$2;

    private SequencedTaskRunnerImpl$$Lambda$1(SequencedTaskRunnerImpl sequencedTaskRunnerImpl, Runnable runnable) {
        this.arg$1 = sequencedTaskRunnerImpl;
        this.arg$2 = runnable;
    }

    public static Runnable lambdaFactory$(SequencedTaskRunnerImpl sequencedTaskRunnerImpl, Runnable runnable) {
        return new SequencedTaskRunnerImpl$$Lambda$1(sequencedTaskRunnerImpl, runnable);
    }

    @Override // java.lang.Runnable
    public final void run() {
        SequencedTaskRunnerImpl.lambda$postDelayedTaskToNative$0(this.arg$1, this.arg$2);
    }
}
