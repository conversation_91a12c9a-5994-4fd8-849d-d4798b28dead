package aegon.chrome.net;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.net.NetworkChangeNotifier;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class NetworkChangeNotifierJni implements NetworkChangeNotifier.Natives {
    public static final JniStaticTestMocker<NetworkChangeNotifier.Natives> TEST_HOOKS = new JniStaticTestMocker<NetworkChangeNotifier.Natives>() { // from class: aegon.chrome.net.NetworkChangeNotifierJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(NetworkChangeNotifier.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                NetworkChangeNotifier.Natives unused = NetworkChangeNotifierJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static NetworkChangeNotifier.Natives testInstance;

    NetworkChangeNotifierJni() {
    }

    @Override // aegon.chrome.net.NetworkChangeNotifier.Natives
    public void notifyConnectionTypeChanged(long j, NetworkChangeNotifier networkChangeNotifier, int i, long j2) {
        GEN_JNI.m57xcc4996a8(j, networkChangeNotifier, i, j2);
    }

    @Override // aegon.chrome.net.NetworkChangeNotifier.Natives
    public void notifyMaxBandwidthChanged(long j, NetworkChangeNotifier networkChangeNotifier, int i) {
        GEN_JNI.org_chromium_net_NetworkChangeNotifier_notifyMaxBandwidthChanged(j, networkChangeNotifier, i);
    }

    @Override // aegon.chrome.net.NetworkChangeNotifier.Natives
    public void notifyOfNetworkConnect(long j, NetworkChangeNotifier networkChangeNotifier, long j2, int i) {
        GEN_JNI.org_chromium_net_NetworkChangeNotifier_notifyOfNetworkConnect(j, networkChangeNotifier, j2, i);
    }

    @Override // aegon.chrome.net.NetworkChangeNotifier.Natives
    public void notifyOfNetworkSoonToDisconnect(long j, NetworkChangeNotifier networkChangeNotifier, long j2) {
        GEN_JNI.m58x39dfa115(j, networkChangeNotifier, j2);
    }

    @Override // aegon.chrome.net.NetworkChangeNotifier.Natives
    public void notifyOfNetworkDisconnect(long j, NetworkChangeNotifier networkChangeNotifier, long j2) {
        GEN_JNI.org_chromium_net_NetworkChangeNotifier_notifyOfNetworkDisconnect(j, networkChangeNotifier, j2);
    }

    @Override // aegon.chrome.net.NetworkChangeNotifier.Natives
    public void notifyPurgeActiveNetworkList(long j, NetworkChangeNotifier networkChangeNotifier, long[] jArr) {
        GEN_JNI.m59x8a4d36cf(j, networkChangeNotifier, jArr);
    }

    public static NetworkChangeNotifier.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            NetworkChangeNotifier.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.net.NetworkChangeNotifier.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new NetworkChangeNotifierJni();
    }
}
