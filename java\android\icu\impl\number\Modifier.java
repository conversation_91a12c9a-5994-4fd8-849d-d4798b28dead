package android.icu.impl.number;

import android.icu.impl.FormattedStringBuilder;
import android.icu.impl.StandardPlural;
import java.text.Format;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public interface Modifier {

    public static class Parameters {
        public ModifierStore obj;
        public StandardPlural plural;
        public int signum;
    }

    int apply(FormattedStringBuilder formattedStringBuilder, int i, int i2);

    boolean containsField(Format.Field field);

    int getCodePointCount();

    Parameters getParameters();

    int getPrefixLength();

    boolean isStrong();

    boolean semanticallyEquivalent(Modifier modifier);
}
