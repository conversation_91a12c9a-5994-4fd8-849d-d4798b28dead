package aegon.chrome.base.task;

import aegon.chrome.base.annotations.JNINamespace;
import androidx.media3.extractor.text.ttml.TtmlNode;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Executor;
import java.util.concurrent.FutureTask;
import java.util.concurrent.atomic.AtomicReferenceArray;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace(TtmlNode.RUBY_BASE)
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class PostTask {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static volatile boolean sNativeInitialized;
    private static volatile Executor sPrenativeThreadPoolExecutorOverride;
    private static final Object sPreNativeTaskRunnerLock = new Object();
    private static List<TaskRunnerImpl> sPreNativeTaskRunners = new ArrayList();
    private static final Executor sPrenativeThreadPoolExecutor = new ChromeThreadPoolExecutor();
    private static AtomicReferenceArray<TaskExecutor> sTaskExecutors = getInitialTaskExecutors();

    interface Natives {
        void postDelayedTask(int i, boolean z, boolean z2, byte b2, byte[] bArr, Runnable runnable, long j, String str);
    }

    private static AtomicReferenceArray<TaskExecutor> getInitialTaskExecutors() {
        AtomicReferenceArray<TaskExecutor> atomicReferenceArray = new AtomicReferenceArray<>(5);
        atomicReferenceArray.set(0, new DefaultTaskExecutor());
        return atomicReferenceArray;
    }

    public static TaskRunner createTaskRunner(TaskTraits taskTraits) {
        return getTaskExecutorForTraits(taskTraits).createTaskRunner(taskTraits);
    }

    public static SequencedTaskRunner createSequencedTaskRunner(TaskTraits taskTraits) {
        return getTaskExecutorForTraits(taskTraits).createSequencedTaskRunner(taskTraits);
    }

    public static SingleThreadTaskRunner createSingleThreadTaskRunner(TaskTraits taskTraits) {
        return getTaskExecutorForTraits(taskTraits).createSingleThreadTaskRunner(taskTraits);
    }

    public static void postTask(TaskTraits taskTraits, Runnable runnable) {
        postDelayedTask(taskTraits, runnable, 0L);
    }

    public static void postDelayedTask(TaskTraits taskTraits, Runnable runnable, long j) {
        if (!sNativeInitialized || taskTraits.mIsChoreographerFrame) {
            getTaskExecutorForTraits(taskTraits).postDelayedTask(taskTraits, runnable, j);
        } else {
            TaskTraits taskTraitsWithExplicitDestination = taskTraits.withExplicitDestination();
            PostTaskJni.get().postDelayedTask(taskTraitsWithExplicitDestination.mPriority, taskTraitsWithExplicitDestination.mMayBlock, taskTraitsWithExplicitDestination.mUseThreadPool, taskTraitsWithExplicitDestination.mExtensionId, taskTraitsWithExplicitDestination.mExtensionData, runnable, j, runnable.getClass().getName());
        }
    }

    public static void runOrPostTask(TaskTraits taskTraits, Runnable runnable) {
        if (getTaskExecutorForTraits(taskTraits).canRunTaskImmediately(taskTraits)) {
            runnable.run();
        } else {
            postTask(taskTraits, runnable);
        }
    }

    @Deprecated
    public static <T> T runSynchronously(TaskTraits taskTraits, Callable<T> callable) {
        return (T) runSynchronouslyInternal(taskTraits, new FutureTask(callable));
    }

    @Deprecated
    public static void runSynchronously(TaskTraits taskTraits, Runnable runnable) {
        runSynchronouslyInternal(taskTraits, new FutureTask(runnable, null));
    }

    private static <T> T runSynchronouslyInternal(TaskTraits taskTraits, FutureTask<T> futureTask) {
        runOrPostTask(taskTraits, futureTask);
        try {
            return futureTask.get();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void registerTaskExecutor(int i, TaskExecutor taskExecutor) {
        sTaskExecutors.set(i, taskExecutor);
    }

    public static void setPrenativeThreadPoolExecutorForTesting(Executor executor) {
        sPrenativeThreadPoolExecutorOverride = executor;
    }

    public static void resetPrenativeThreadPoolExecutorForTesting() {
        sPrenativeThreadPoolExecutorOverride = null;
    }

    static Executor getPrenativeThreadPoolExecutor() {
        if (sPrenativeThreadPoolExecutorOverride != null) {
            return sPrenativeThreadPoolExecutorOverride;
        }
        return sPrenativeThreadPoolExecutor;
    }

    static boolean registerPreNativeTaskRunner(TaskRunnerImpl taskRunnerImpl) {
        synchronized (sPreNativeTaskRunnerLock) {
            if (sPreNativeTaskRunners == null) {
                return false;
            }
            sPreNativeTaskRunners.add(taskRunnerImpl);
            return true;
        }
    }

    private static TaskExecutor getTaskExecutorForTraits(TaskTraits taskTraits) {
        return sTaskExecutors.get(taskTraits.mExtensionId);
    }

    private static void onNativeSchedulerReady() {
        List<TaskRunnerImpl> list;
        sNativeInitialized = true;
        synchronized (sPreNativeTaskRunnerLock) {
            list = sPreNativeTaskRunners;
            sPreNativeTaskRunners = null;
        }
        Iterator<TaskRunnerImpl> it = list.iterator2();
        while (it.hasNext()) {
            it.mo35924next().initNativeTaskRunner();
        }
    }

    private static void onNativeSchedulerShutdownForTesting() {
        synchronized (sPreNativeTaskRunnerLock) {
            sPreNativeTaskRunners = new ArrayList();
        }
        sNativeInitialized = false;
        sTaskExecutors.set(0, new DefaultTaskExecutor());
        for (int i = 1; i < sTaskExecutors.length(); i++) {
            sTaskExecutors.set(i, null);
        }
    }
}
