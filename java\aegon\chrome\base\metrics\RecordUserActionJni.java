package aegon.chrome.base.metrics;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.metrics.RecordUserAction;
import aegon.chrome.base.natives.GEN_JNI;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class RecordUserActionJni implements RecordUserAction.Natives {
    public static final JniStaticTestMocker<RecordUserAction.Natives> TEST_HOOKS = new JniStaticTestMocker<RecordUserAction.Natives>() { // from class: aegon.chrome.base.metrics.RecordUserActionJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(RecordUserAction.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                RecordUserAction.Natives unused = RecordUserActionJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static RecordUserAction.Natives testInstance;

    RecordUserActionJni() {
    }

    @Override // aegon.chrome.base.metrics.RecordUserAction.Natives
    public long addActionCallbackForTesting(RecordUserAction.UserActionCallback userActionCallback) {
        return GEN_JNI.m55x3f62a0b2(userActionCallback);
    }

    @Override // aegon.chrome.base.metrics.RecordUserAction.Natives
    public void removeActionCallbackForTesting(long j) {
        GEN_JNI.m56x3ebb9757(j);
    }

    public static RecordUserAction.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            RecordUserAction.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.metrics.RecordUserAction.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new RecordUserActionJni();
    }
}
