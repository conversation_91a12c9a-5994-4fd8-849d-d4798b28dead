package aegon.chrome.net.impl;

import aegon.chrome.net.CronetEngine;
import aegon.chrome.net.ExperimentalCronetEngine;
import aegon.chrome.net.ICronetEngineBuilder;
import android.content.Context;
import java.util.Date;
import java.util.Set;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class NativeCronetEngineBuilderImpl extends CronetEngineBuilderImpl {
    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder addPublicKeyPins(String str, Set set, boolean z, Date date) {
        return super.addPublicKeyPins(str, (Set<byte[]>) set, z, date);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder addQuicHint(String str, int i, int i2) {
        return super.addQuicHint(str, i, i2);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder enableBrotli(boolean z) {
        return super.enableBrotli(z);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder enableHttp2(boolean z) {
        return super.enableHttp2(z);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder enableHttpCache(int i, long j) {
        return super.enableHttpCache(i, j);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder enableNetworkQualityEstimator(boolean z) {
        return super.enableNetworkQualityEstimator(z);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder enablePublicKeyPinningBypassForLocalTrustAnchors(boolean z) {
        return super.enablePublicKeyPinningBypassForLocalTrustAnchors(z);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder enableQuic(boolean z) {
        return super.enableQuic(z);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder enableSdch(boolean z) {
        return super.enableSdch(z);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder setExperimentalOptions(String str) {
        return super.setExperimentalOptions(str);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder setLibraryLoader(CronetEngine.Builder.LibraryLoader libraryLoader) {
        return super.setLibraryLoader(libraryLoader);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder setStoragePath(String str) {
        return super.setStoragePath(str);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder setThreadPriority(int i) {
        return super.setThreadPriority(i);
    }

    @Override // aegon.chrome.net.impl.CronetEngineBuilderImpl, aegon.chrome.net.ICronetEngineBuilder
    public /* bridge */ /* synthetic */ ICronetEngineBuilder setUserAgent(String str) {
        return super.setUserAgent(str);
    }

    public NativeCronetEngineBuilderImpl(Context context) {
        super(context);
    }

    @Override // aegon.chrome.net.ICronetEngineBuilder
    public ExperimentalCronetEngine build() {
        if (getUserAgent() == null) {
            setUserAgent(getDefaultUserAgent());
        }
        CronetUrlRequestContext cronetUrlRequestContext = new CronetUrlRequestContext(this);
        this.mMockCertVerifier = 0L;
        return cronetUrlRequestContext;
    }
}
