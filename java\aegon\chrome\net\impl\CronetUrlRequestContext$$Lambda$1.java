package aegon.chrome.net.impl;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class CronetUrlRequestContext$$Lambda$1 implements Runnable {
    private final CronetUrlRequestContext arg$1;

    private CronetUrlRequestContext$$Lambda$1(CronetUrlRequestContext cronetUrlRequestContext) {
        this.arg$1 = cronetUrlRequestContext;
    }

    public static Runnable lambdaFactory$(CronetUrlRequestContext cronetUrlRequestContext) {
        return new CronetUrlRequestContext$$Lambda$1(cronetUrlRequestContext);
    }

    @Override // java.lang.Runnable
    public final void run() {
        CronetUrlRequestContext.lambda$new$0(this.arg$1);
    }
}
