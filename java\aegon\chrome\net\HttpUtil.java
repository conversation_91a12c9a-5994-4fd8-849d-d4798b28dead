package aegon.chrome.net;

import aegon.chrome.base.annotations.JNINamespace;
import com.kwad.components.offline.api.p367tk.model.report.TKDownloadReason;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace(TKDownloadReason.KSAD_TK_NET)
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class HttpUtil {

    interface Natives {
        boolean isAllowedHeader(String str, String str2);
    }

    public static boolean isAllowedHeader(String str, String str2) {
        return HttpUtilJni.get().isAllowedHeader(str, str2);
    }
}
