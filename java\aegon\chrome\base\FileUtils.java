package aegon.chrome.base;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import java.p654io.ByteArrayOutputStream;
import java.p654io.File;
import java.p654io.FileDescriptor;
import java.p654io.FileOutputStream;
import java.p654io.IOException;
import java.p654io.InputStream;
import java.p654io.OutputStream;
import java.util.List;
import java.util.Locale;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class FileUtils {
    public static Function<String, Boolean> DELETE_ALL = new Function() { // from class: aegon.chrome.base.-$$Lambda$FileUtils$iQFhiGPZI0vBzLl_q49x8wU-vSE
        @Override // aegon.chrome.base.Function
        public final Object apply(Object obj) {
            return Boolean.TRUE;
        }
    };
    private static final String TAG = "FileUtils";

    public static boolean recursivelyDeleteFile(File file, Function<String, Boolean> function) {
        File[] fileArrListFiles;
        if (!file.exists()) {
            file.delete();
            return true;
        }
        if (function != null && !function.apply(file.getPath()).booleanValue()) {
            return true;
        }
        if (file.isDirectory() && (fileArrListFiles = file.listFiles()) != null) {
            for (File file2 : fileArrListFiles) {
                recursivelyDeleteFile(file2, function);
            }
        }
        boolean zDelete = file.delete();
        if (!zDelete) {
            Log.m43e(TAG, "Failed to delete: %s", file);
        }
        return zDelete;
    }

    public static void batchDeleteFiles(List<String> list, Function<String, Boolean> function) {
        for (String str : list) {
            if (function == null || function.apply(str).booleanValue()) {
                if (ContentUriUtils.isContentUri(str)) {
                    ContentUriUtils.delete(str);
                } else {
                    File file = new File(str);
                    if (file.exists()) {
                        recursivelyDeleteFile(file, function);
                    }
                }
            }
        }
    }

    public static long getFileSizeBytes(File file) {
        long fileSizeBytes = 0;
        if (file == null) {
            return 0L;
        }
        if (file.isDirectory()) {
            File[] fileArrListFiles = file.listFiles();
            if (fileArrListFiles == null) {
                return 0L;
            }
            for (File file2 : fileArrListFiles) {
                fileSizeBytes += getFileSizeBytes(file2);
            }
            return fileSizeBytes;
        }
        return file.length();
    }

    public static void copyStream(InputStream inputStream, OutputStream outputStream) throws IOException {
        byte[] bArr = new byte[8192];
        while (true) {
            int i = inputStream.read(bArr);
            if (i == -1) {
                return;
            } else {
                outputStream.write(bArr, 0, i);
            }
        }
    }

    public static void copyStreamToFile(InputStream inputStream, File file) throws IOException {
        File file2 = new File(file.getPath() + ".tmp");
        FileOutputStream fileOutputStream = new FileOutputStream(file2);
        try {
            Log.m44i(TAG, "Writing to %s", file);
            copyStream(inputStream, fileOutputStream);
            fileOutputStream.lambda$new$0();
            if (!file2.renameTo(file)) {
                throw new IOException();
            }
        } catch (Throwable th) {
            try {
                fileOutputStream.lambda$new$0();
            } catch (Throwable unused) {
            }
            throw th;
        }
    }

    public static byte[] readStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        copyStream(inputStream, byteArrayOutputStream);
        return byteArrayOutputStream.toByteArray();
    }

    public static Uri getUriForFile(File file) {
        Uri contentUriFromFile;
        try {
            contentUriFromFile = ContentUriUtils.getContentUriFromFile(file);
        } catch (IllegalArgumentException e) {
            Log.m43e(TAG, "Could not create content uri: " + ((Object) e), new Object[0]);
            contentUriFromFile = null;
        }
        return contentUriFromFile == null ? Uri.fromFile(file) : contentUriFromFile;
    }

    public static String getExtension(String str) {
        int iLastIndexOf = str.lastIndexOf(47);
        int iLastIndexOf2 = str.lastIndexOf(46);
        return iLastIndexOf >= iLastIndexOf2 ? "" : str.substring(iLastIndexOf2 + 1).toLowerCase(Locale.f24101US);
    }

    public static Bitmap queryBitmapFromContentProvider(Context context, Uri uri) throws IOException {
        try {
            ParcelFileDescriptor parcelFileDescriptorOpenFileDescriptor = context.getContentResolver().openFileDescriptor(uri, "r");
            try {
                if (parcelFileDescriptorOpenFileDescriptor == null) {
                    Log.m46w(TAG, "Null ParcelFileDescriptor from uri " + ((Object) uri), new Object[0]);
                    if (parcelFileDescriptorOpenFileDescriptor != null) {
                        parcelFileDescriptorOpenFileDescriptor.close();
                    }
                    return null;
                }
                FileDescriptor fileDescriptor = parcelFileDescriptorOpenFileDescriptor.getFileDescriptor();
                if (fileDescriptor == null) {
                    Log.m46w(TAG, "Null FileDescriptor from uri " + ((Object) uri), new Object[0]);
                    if (parcelFileDescriptorOpenFileDescriptor != null) {
                        parcelFileDescriptorOpenFileDescriptor.close();
                    }
                    return null;
                }
                Bitmap bitmapDecodeFileDescriptor = BitmapFactory.decodeFileDescriptor(fileDescriptor);
                if (bitmapDecodeFileDescriptor != null) {
                    if (parcelFileDescriptorOpenFileDescriptor != null) {
                        parcelFileDescriptorOpenFileDescriptor.close();
                    }
                    return bitmapDecodeFileDescriptor;
                }
                Log.m46w(TAG, "Failed to decode image from uri " + ((Object) uri), new Object[0]);
                if (parcelFileDescriptorOpenFileDescriptor != null) {
                    parcelFileDescriptorOpenFileDescriptor.close();
                }
                return null;
            } catch (Throwable th) {
                if (parcelFileDescriptorOpenFileDescriptor != null) {
                    try {
                        parcelFileDescriptorOpenFileDescriptor.close();
                    } catch (Throwable unused) {
                    }
                }
                throw th;
            }
        } catch (IOException unused2) {
            Log.m46w(TAG, "IO exception when reading uri " + ((Object) uri), new Object[0]);
            return null;
        }
    }
}
