package android.arch.lifecycle;

import com.android.internal.util.Predicate;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes25.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes37.dex */
public abstract class ViewModel {
    protected void onCleared() {
    }

    private static void stub() {
        System.out.println(Predicate.class);
    }
}
