package aegon.chrome.base.supplier;

import aegon.chrome.base.Callback;
import aegon.chrome.base.Promise;
import aegon.chrome.base.ThreadUtils;
import aegon.chrome.base.supplier.Supplier;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class OneshotSupplierImpl<T> implements OneshotSupplier<T> {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private final Promise<T> mPromise = new Promise<>();
    private final ThreadUtils.ThreadChecker mThreadChecker = new ThreadUtils.ThreadChecker();

    @Override // aegon.chrome.base.supplier.Supplier
    public /* synthetic */ boolean hasValue() {
        return Supplier.CC.$default$hasValue(this);
    }

    @Override // aegon.chrome.base.supplier.OneshotSupplier
    public T onAvailable(Callback<T> callback) {
        this.mThreadChecker.assertOnValidThread();
        this.mPromise.then(callback);
        return get();
    }

    @Override // aegon.chrome.base.supplier.Supplier
    public T get() {
        this.mThreadChecker.assertOnValidThread();
        if (this.mPromise.isFulfilled()) {
            return this.mPromise.getResult();
        }
        return null;
    }

    public void set(T t) {
        this.mThreadChecker.assertOnValidThread();
        this.mPromise.fulfill(t);
    }
}
