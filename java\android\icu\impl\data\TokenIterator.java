package android.icu.impl.data;

import java.p654io.IOException;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class TokenIterator {
    private ResourceReader reader;
    private String line = null;
    private boolean done = false;
    private StringBuffer buf = new StringBuffer();
    private int lastpos = -1;
    private int pos = -1;

    public TokenIterator(ResourceReader r) {
        this.reader = r;
    }

    public String next() throws IOException {
        if (this.done) {
            return null;
        }
        while (true) {
            if (this.line == null) {
                String lineSkippingComments = this.reader.readLineSkippingComments();
                this.line = lineSkippingComments;
                if (lineSkippingComments == null) {
                    this.done = true;
                    return null;
                }
                this.pos = 0;
            }
            this.buf.setLength(0);
            int i = this.pos;
            this.lastpos = i;
            int iNextToken = nextToken(i);
            this.pos = iNextToken;
            if (iNextToken < 0) {
                this.line = null;
            } else {
                return this.buf.toString();
            }
        }
    }

    public int getLineNumber() {
        return this.reader.getLineNumber();
    }

    public String describePosition() {
        return this.reader.describePosition() + ':' + (this.lastpos + 1);
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x002d  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private int nextToken(int r10) {
        /*
            r9 = this;
            java.lang.String r0 = r9.line
            int r10 = android.icu.impl.PatternProps.skipWhiteSpace(r0, r10)
            java.lang.String r0 = r9.line
            int r0 = r0.length()
            r1 = -1
            if (r10 != r0) goto L10
            return r1
        L10:
            r0 = r10
            java.lang.String r2 = r9.line
            int r3 = r10 + 1
            char r10 = r2.charAt(r10)
            r2 = 0
            r4 = 34
            r5 = 35
            if (r10 == r4) goto L2d
            if (r10 == r5) goto L2c
            r1 = 39
            if (r10 == r1) goto L2d
            java.lang.StringBuffer r1 = r9.buf
            r1.append(r10)
            goto L2f
        L2c:
            return r1
        L2d:
            r2 = r10
        L2f:
            r1 = 0
        L30:
            java.lang.String r4 = r9.line
            int r4 = r4.length()
            r6 = 58
            if (r3 >= r4) goto L9c
            java.lang.String r4 = r9.line
            char r10 = r4.charAt(r3)
            r4 = 92
            r7 = 1
            if (r10 != r4) goto L81
            if (r1 != 0) goto L49
            int[] r1 = new int[r7]
        L49:
            int r4 = r3 + 1
            r7 = 0
            r1[r7] = r4
            java.lang.String r4 = r9.line
            int r4 = android.icu.impl.Utility.unescapeAt(r4, r1)
            if (r4 < 0) goto L5e
            java.lang.StringBuffer r6 = r9.buf
            android.icu.text.UTF16.append(r6, r4)
            r3 = r1[r7]
            goto L30
        L5e:
            java.lang.RuntimeException r5 = new java.lang.RuntimeException
            java.lang.StringBuilder r7 = new java.lang.StringBuilder
            r7.<init>()
            java.lang.String r8 = "Invalid escape at "
            r7.append(r8)
            android.icu.impl.data.ResourceReader r8 = r9.reader
            java.lang.String r8 = r8.describePosition()
            r7.append(r8)
            r7.append(r6)
            r7.append(r3)
            java.lang.String r6 = r7.toString()
            r5.<init>(r6)
            throw r5
        L81:
            if (r2 == 0) goto L85
            if (r10 == r2) goto L8d
        L85:
            if (r2 != 0) goto L8f
            boolean r4 = android.icu.impl.PatternProps.isWhiteSpace(r10)
            if (r4 == 0) goto L8f
        L8d:
            int r3 = r3 + r7
            return r3
        L8f:
            if (r2 != 0) goto L94
            if (r10 != r5) goto L94
            return r3
        L94:
            java.lang.StringBuffer r4 = r9.buf
            r4.append(r10)
            int r3 = r3 + 1
            goto L30
        L9c:
            if (r2 != 0) goto L9f
            return r3
        L9f:
            java.lang.RuntimeException r4 = new java.lang.RuntimeException
            java.lang.StringBuilder r5 = new java.lang.StringBuilder
            r5.<init>()
            java.lang.String r7 = "Unterminated quote at "
            r5.append(r7)
            android.icu.impl.data.ResourceReader r7 = r9.reader
            java.lang.String r7 = r7.describePosition()
            r5.append(r7)
            r5.append(r6)
            r5.append(r0)
            java.lang.String r5 = r5.toString()
            r4.<init>(r5)
            throw r4
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.data.TokenIterator.nextToken(int):int");
    }
}
