package aegon.chrome.base;

import aegon.chrome.base.Callback;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class CallbackController {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private ArrayList<WeakReference<Cancelable>> mCancelables = new ArrayList<>();
    private final ReentrantLock mReentrantLock = new ReentrantLock(true);

    interface Cancelable {
        void cancel();
    }

    class CancelableCallback<T> implements Callback<T>, Cancelable {
        private Callback<T> mCallback;

        @Override // aegon.chrome.base.Callback
        public /* synthetic */ Runnable bind(T t) {
            return Callback.CC.$default$bind(this, t);
        }

        private CancelableCallback(Callback<T> callback) {
            this.mCallback = callback;
        }

        @Override // aegon.chrome.base.CallbackController.Cancelable
        public void cancel() {
            this.mCallback = null;
        }

        @Override // aegon.chrome.base.Callback
        public void onResult(T t) {
            AutoCloseableLock autoCloseableLockLock = AutoCloseableLock.lock(CallbackController.this.mReentrantLock);
            try {
                if (this.mCallback != null) {
                    this.mCallback.onResult(t);
                }
                if (autoCloseableLockLock != null) {
                    autoCloseableLockLock.lambda$new$0();
                }
            } catch (Throwable th) {
                if (autoCloseableLockLock != null) {
                    try {
                        autoCloseableLockLock.lambda$new$0();
                    } catch (Throwable unused) {
                    }
                }
                throw th;
            }
        }
    }

    class CancelableRunnable implements Cancelable, Runnable {
        private Runnable mRunnable;

        private CancelableRunnable(Runnable runnable) {
            this.mRunnable = runnable;
        }

        @Override // aegon.chrome.base.CallbackController.Cancelable
        public void cancel() {
            this.mRunnable = null;
        }

        @Override // java.lang.Runnable
        public void run() {
            AutoCloseableLock autoCloseableLockLock = AutoCloseableLock.lock(CallbackController.this.mReentrantLock);
            try {
                if (this.mRunnable != null) {
                    this.mRunnable.run();
                }
                if (autoCloseableLockLock != null) {
                    autoCloseableLockLock.lambda$new$0();
                }
            } catch (Throwable th) {
                if (autoCloseableLockLock != null) {
                    try {
                        autoCloseableLockLock.lambda$new$0();
                    } catch (Throwable unused) {
                    }
                }
                throw th;
            }
        }
    }

    static class AutoCloseableLock implements AutoCloseable {
        private boolean mIsLocked;
        private final Lock mLock;

        private AutoCloseableLock(Lock lock, boolean z) {
            this.mLock = lock;
            this.mIsLocked = z;
        }

        static AutoCloseableLock lock(Lock lock) {
            lock.lock();
            return new AutoCloseableLock(lock, true);
        }

        @Override // java.lang.AutoCloseable
        /* renamed from: close */
        public void lambda$new$0() {
            if (!this.mIsLocked) {
                throw new IllegalStateException("mLock isn't locked.");
            }
            this.mIsLocked = false;
            this.mLock.unlock();
        }
    }

    public final <T> Callback<T> makeCancelable(Callback<T> callback) {
        AutoCloseableLock autoCloseableLockLock = AutoCloseableLock.lock(this.mReentrantLock);
        try {
            checkNotCanceled();
            CancelableCallback cancelableCallback = new CancelableCallback(callback);
            this.mCancelables.add(new WeakReference<>(cancelableCallback));
            if (autoCloseableLockLock != null) {
                autoCloseableLockLock.lambda$new$0();
            }
            return cancelableCallback;
        } catch (Throwable th) {
            if (autoCloseableLockLock != null) {
                try {
                    autoCloseableLockLock.lambda$new$0();
                } catch (Throwable unused) {
                }
            }
            throw th;
        }
    }

    public final Runnable makeCancelable(Runnable runnable) {
        AutoCloseableLock autoCloseableLockLock = AutoCloseableLock.lock(this.mReentrantLock);
        try {
            checkNotCanceled();
            CancelableRunnable cancelableRunnable = new CancelableRunnable(runnable);
            this.mCancelables.add(new WeakReference<>(cancelableRunnable));
            if (autoCloseableLockLock != null) {
                autoCloseableLockLock.lambda$new$0();
            }
            return cancelableRunnable;
        } catch (Throwable th) {
            if (autoCloseableLockLock != null) {
                try {
                    autoCloseableLockLock.lambda$new$0();
                } catch (Throwable unused) {
                }
            }
            throw th;
        }
    }

    public final void destroy() {
        AutoCloseableLock autoCloseableLockLock = AutoCloseableLock.lock(this.mReentrantLock);
        try {
            checkNotCanceled();
            Iterator itIterator2 = CollectionUtil.strengthen(this.mCancelables).iterator2();
            while (itIterator2.hasNext()) {
                ((Cancelable) itIterator2.mo35924next()).cancel();
            }
            this.mCancelables = null;
            if (autoCloseableLockLock != null) {
                autoCloseableLockLock.lambda$new$0();
            }
        } catch (Throwable th) {
            if (autoCloseableLockLock != null) {
                try {
                    autoCloseableLockLock.lambda$new$0();
                } catch (Throwable unused) {
                }
            }
            throw th;
        }
    }

    private void checkNotCanceled() {
        if (this.mCancelables == null) {
            throw new IllegalStateException("This CallbackController has already been destroyed.");
        }
    }
}
