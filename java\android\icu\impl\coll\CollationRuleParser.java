package android.icu.impl.coll;

import android.icu.impl.IllegalIcuArgumentException;
import android.icu.impl.PatternProps;
import android.icu.impl.PatternTokenizer;
import android.icu.lang.UCharacter;
import android.icu.lang.UProperty;
import android.icu.text.Normalizer2;
import android.icu.text.PluralRules;
import android.icu.text.UnicodeSet;
import android.icu.util.ULocale;
import com.xiaomi.stat.MiStat;
import io.netty.util.internal.StringUtil;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Iterator;
import org.apache.xalan.templates.Constants;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationRuleParser {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final String BEFORE = "[before";
    private static final int OFFSET_SHIFT = 8;
    static final char POS_BASE = 10240;
    static final char POS_LEAD = 65534;
    private static final int STARRED_FLAG = 16;
    private static final int STRENGTH_MASK = 15;
    private static final int UCOL_DEFAULT = -1;
    private static final int UCOL_OFF = 0;
    private static final int UCOL_ON = 1;
    private static final int U_PARSE_CONTEXT_LEN = 16;
    private final CollationData baseData;
    private Importer importer;
    private int ruleIndex;
    private String rules;
    private CollationSettings settings;
    private Sink sink;
    static final Position[] POSITION_VALUES = Position.values();
    private static final String[] positions = {"first tertiary ignorable", "last tertiary ignorable", "first secondary ignorable", "last secondary ignorable", "first primary ignorable", "last primary ignorable", "first variable", "last variable", "first regular", "last regular", "first implicit", "last implicit", "first trailing", "last trailing"};
    private static final String[] gSpecialReorderCodes = {"space", "punct", "symbol", MiStat.Param.CURRENCY, Constants.ATTRNAME_DIGIT};
    private final StringBuilder rawBuilder = new StringBuilder();
    private Normalizer2 nfd = Normalizer2.getNFDInstance();
    private Normalizer2 nfc = Normalizer2.getNFCInstance();

    interface Importer {
        String getRules(String str, String str2);
    }

    enum Position {
        FIRST_TERTIARY_IGNORABLE,
        LAST_TERTIARY_IGNORABLE,
        FIRST_SECONDARY_IGNORABLE,
        LAST_SECONDARY_IGNORABLE,
        FIRST_PRIMARY_IGNORABLE,
        LAST_PRIMARY_IGNORABLE,
        FIRST_VARIABLE,
        LAST_VARIABLE,
        FIRST_REGULAR,
        LAST_REGULAR,
        FIRST_IMPLICIT,
        LAST_IMPLICIT,
        FIRST_TRAILING,
        LAST_TRAILING
    }

    static abstract class Sink {
        abstract void addRelation(int i, CharSequence charSequence, CharSequence charSequence2, CharSequence charSequence3);

        abstract void addReset(int i, CharSequence charSequence);

        Sink() {
        }

        void suppressContractions(UnicodeSet set) {
        }

        void optimize(UnicodeSet set) {
        }
    }

    CollationRuleParser(CollationData base) {
        this.baseData = base;
    }

    void setSink(Sink sinkAlias) {
        this.sink = sinkAlias;
    }

    void setImporter(Importer importerAlias) {
        this.importer = importerAlias;
    }

    void parse(String ruleString, CollationSettings outSettings) throws ParseException {
        this.settings = outSettings;
        parse(ruleString);
    }

    private void parse(String ruleString) throws ParseException {
        this.rules = ruleString;
        this.ruleIndex = 0;
        while (this.ruleIndex < this.rules.length()) {
            char c2 = this.rules.charAt(this.ruleIndex);
            if (PatternProps.isWhiteSpace(c2)) {
                this.ruleIndex++;
            } else if (c2 == '!') {
                this.ruleIndex++;
            } else if (c2 == '#') {
                this.ruleIndex = skipComment(this.ruleIndex + 1);
            } else if (c2 == '&') {
                parseRuleChain();
            } else if (c2 == '@') {
                this.settings.setFlag(2048, true);
                this.ruleIndex++;
            } else if (c2 == '[') {
                parseSetting();
            } else {
                setParseError("expected a reset or setting or comment");
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x002c, code lost:
    
        if (r1 == false) goto L36;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x002e, code lost:
    
        setParseError("reset not followed by a relation");
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0033, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:?, code lost:
    
        return;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private void parseRuleChain() throws java.text.ParseException {
        /*
            r6 = this;
            int r0 = r6.parseResetAndPosition()
            r1 = 1
        L5:
            int r2 = r6.parseRelationOperator()
            if (r2 >= 0) goto L34
            int r3 = r6.ruleIndex
            java.lang.String r4 = r6.rules
            int r4 = r4.length()
            if (r3 >= r4) goto L2c
            java.lang.String r3 = r6.rules
            int r4 = r6.ruleIndex
            char r3 = r3.charAt(r4)
            r4 = 35
            if (r3 != r4) goto L2c
            int r3 = r6.ruleIndex
            int r3 = r3 + 1
            int r3 = r6.skipComment(r3)
            r6.ruleIndex = r3
            goto L5
        L2c:
            if (r1 == 0) goto L33
            java.lang.String r3 = "reset not followed by a relation"
            r6.setParseError(r3)
        L33:
            return
        L34:
            r3 = r2 & 15
            r4 = 15
            if (r0 >= r4) goto L4c
            if (r1 == 0) goto L44
            if (r3 == r0) goto L4c
            java.lang.String r4 = "reset-before strength differs from its first relation"
            r6.setParseError(r4)
            return
        L44:
            if (r3 >= r0) goto L4c
            java.lang.String r4 = "reset-before strength followed by a stronger relation"
            r6.setParseError(r4)
            return
        L4c:
            int r4 = r6.ruleIndex
            int r5 = r2 >> 8
            int r4 = r4 + r5
            r5 = r2 & 16
            if (r5 != 0) goto L59
            r6.parseRelationStrings(r3, r4)
            goto L5c
        L59:
            r6.parseStarredCharacters(r3, r4)
        L5c:
            r1 = 0
            goto L5
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationRuleParser.parseRuleChain():void");
    }

    /* JADX WARN: Removed duplicated region for block: B:17:0x0067  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private int parseResetAndPosition() throws java.text.ParseException {
        /*
            r6 = this;
            int r0 = r6.ruleIndex
            int r0 = r0 + 1
            int r0 = r6.skipWhiteSpace(r0)
            java.lang.String r1 = r6.rules
            java.lang.String r2 = "[before"
            int r3 = r2.length()
            r4 = 0
            boolean r1 = r1.regionMatches(r0, r2, r4, r3)
            if (r1 == 0) goto L67
            int r1 = r2.length()
            int r1 = r1 + r0
            r2 = r1
            java.lang.String r3 = r6.rules
            int r3 = r3.length()
            if (r1 >= r3) goto L67
            java.lang.String r1 = r6.rules
            char r1 = r1.charAt(r2)
            boolean r1 = android.icu.impl.PatternProps.isWhiteSpace(r1)
            if (r1 == 0) goto L67
            int r1 = r2 + 1
            int r1 = r6.skipWhiteSpace(r1)
            r2 = r1
            int r1 = r1 + 1
            java.lang.String r3 = r6.rules
            int r3 = r3.length()
            if (r1 >= r3) goto L67
            java.lang.String r1 = r6.rules
            char r1 = r1.charAt(r2)
            r3 = r1
            r5 = 49
            if (r5 > r1) goto L67
            r1 = 51
            if (r3 > r1) goto L67
            java.lang.String r1 = r6.rules
            int r5 = r2 + 1
            char r1 = r1.charAt(r5)
            r5 = 93
            if (r1 != r5) goto L67
            int r1 = r3 + (-49)
            int r1 = r1 + r4
            int r4 = r2 + 2
            int r0 = r6.skipWhiteSpace(r4)
            goto L69
        L67:
            r1 = 15
        L69:
            java.lang.String r2 = r6.rules
            int r2 = r2.length()
            r3 = -1
            if (r0 < r2) goto L78
            java.lang.String r2 = "reset without position"
            r6.setParseError(r2)
            return r3
        L78:
            java.lang.String r2 = r6.rules
            char r2 = r2.charAt(r0)
            r4 = 91
            if (r2 != r4) goto L89
            java.lang.StringBuilder r2 = r6.rawBuilder
            int r0 = r6.parseSpecialPosition(r0, r2)
            goto L8f
        L89:
            java.lang.StringBuilder r2 = r6.rawBuilder
            int r0 = r6.parseTailoringString(r0, r2)
        L8f:
            android.icu.impl.coll.CollationRuleParser$Sink r2 = r6.sink     // Catch: java.lang.Exception -> L9a
            java.lang.StringBuilder r4 = r6.rawBuilder     // Catch: java.lang.Exception -> L9a
            r2.addReset(r1, r4)     // Catch: java.lang.Exception -> L9a
            r6.ruleIndex = r0
            return r1
        L9a:
            r2 = move-exception
            java.lang.String r4 = "adding reset failed"
            r6.setParseError(r4, r2)
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationRuleParser.parseResetAndPosition():int");
    }

    private int parseRelationOperator() {
        int strength;
        int iSkipWhiteSpace = skipWhiteSpace(this.ruleIndex);
        this.ruleIndex = iSkipWhiteSpace;
        if (iSkipWhiteSpace >= this.rules.length()) {
            return -1;
        }
        int i = this.ruleIndex;
        int i2 = i + 1;
        char c2 = this.rules.charAt(i);
        if (c2 != ',') {
            switch (c2) {
                case ';':
                    strength = 1;
                    break;
                case '<':
                    if (i2 < this.rules.length() && this.rules.charAt(i2) == '<') {
                        i2++;
                        if (i2 < this.rules.length() && this.rules.charAt(i2) == '<') {
                            i2++;
                            if (i2 < this.rules.length() && this.rules.charAt(i2) == '<') {
                                i2++;
                                strength = 3;
                            } else {
                                strength = 2;
                            }
                        } else {
                            strength = 1;
                        }
                    } else {
                        strength = 0;
                    }
                    if (i2 < this.rules.length() && this.rules.charAt(i2) == '*') {
                        i2++;
                        strength |= 16;
                        break;
                    }
                    break;
                case '=':
                    strength = 15;
                    if (i2 < this.rules.length() && this.rules.charAt(i2) == '*') {
                        i2++;
                        strength = 15 | 16;
                        break;
                    }
                    break;
                default:
                    return -1;
            }
        } else {
            strength = 2;
        }
        return ((i2 - this.ruleIndex) << 8) | strength;
    }

    private void parseRelationStrings(int strength, int i) throws ParseException {
        String prefix = "";
        CharSequence extension = "";
        int i2 = parseTailoringString(i, this.rawBuilder);
        char next = i2 < this.rules.length() ? this.rules.charAt(i2) : (char) 0;
        if (next == '|') {
            prefix = this.rawBuilder.toString();
            i2 = parseTailoringString(i2 + 1, this.rawBuilder);
            next = i2 < this.rules.length() ? this.rules.charAt(i2) : (char) 0;
        }
        if (next == '/') {
            StringBuilder extBuilder = new StringBuilder();
            i2 = parseTailoringString(i2 + 1, extBuilder);
            extension = extBuilder;
        }
        if (prefix.length() != 0) {
            int prefix0 = prefix.codePointAt(0);
            int c2 = this.rawBuilder.codePointAt(0);
            if (!this.nfc.hasBoundaryBefore(prefix0) || !this.nfc.hasBoundaryBefore(c2)) {
                setParseError("in 'prefix|str', prefix and str must each start with an NFC boundary");
                return;
            }
        }
        try {
            this.sink.addRelation(strength, prefix, this.rawBuilder, extension);
            this.ruleIndex = i2;
        } catch (Exception e) {
            setParseError("adding relation failed", e);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:56:0x00d4, code lost:
    
        r7.ruleIndex = skipWhiteSpace(r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x00da, code lost:
    
        return;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private void parseStarredCharacters(int r8, int r9) throws java.text.ParseException {
        /*
            r7 = this;
            java.lang.String r0 = ""
            int r1 = r7.skipWhiteSpace(r9)
            java.lang.StringBuilder r2 = r7.rawBuilder
            int r9 = r7.parseString(r1, r2)
            java.lang.StringBuilder r1 = r7.rawBuilder
            int r1 = r1.length()
            if (r1 != 0) goto L1a
            java.lang.String r1 = "missing starred-relation string"
            r7.setParseError(r1)
            return
        L1a:
            r1 = -1
            r2 = 0
        L1c:
            java.lang.StringBuilder r3 = r7.rawBuilder
            int r3 = r3.length()
            java.lang.String r4 = "adding relation failed"
            if (r2 >= r3) goto L50
            java.lang.StringBuilder r3 = r7.rawBuilder
            int r3 = r3.codePointAt(r2)
            android.icu.text.Normalizer2 r5 = r7.nfd
            boolean r5 = r5.isInert(r3)
            if (r5 != 0) goto L3a
            java.lang.String r4 = "starred-relation string is not all NFD-inert"
            r7.setParseError(r4)
            return
        L3a:
            android.icu.impl.coll.CollationRuleParser$Sink r5 = r7.sink     // Catch: java.lang.Exception -> L4b
            java.lang.String r6 = android.icu.text.UTF16.valueOf(r3)     // Catch: java.lang.Exception -> L4b
            r5.addRelation(r8, r0, r6, r0)     // Catch: java.lang.Exception -> L4b
            int r4 = java.lang.Character.charCount(r3)
            int r2 = r2 + r4
            r1 = r3
            goto L1c
        L4b:
            r5 = move-exception
            r7.setParseError(r4, r5)
            return
        L50:
            java.lang.String r3 = r7.rules
            int r3 = r3.length()
            if (r9 >= r3) goto Ld4
            java.lang.String r3 = r7.rules
            char r3 = r3.charAt(r9)
            r5 = 45
            if (r3 == r5) goto L63
            goto Ld4
        L63:
            if (r1 >= 0) goto L6b
            java.lang.String r3 = "range without start in starred-relation string"
            r7.setParseError(r3)
            return
        L6b:
            int r3 = r9 + 1
            java.lang.StringBuilder r5 = r7.rawBuilder
            int r9 = r7.parseString(r3, r5)
            java.lang.StringBuilder r3 = r7.rawBuilder
            int r3 = r3.length()
            if (r3 != 0) goto L81
            java.lang.String r3 = "range without end in starred-relation string"
            r7.setParseError(r3)
            return
        L81:
            java.lang.StringBuilder r3 = r7.rawBuilder
            r5 = 0
            int r3 = r3.codePointAt(r5)
            if (r3 >= r1) goto L90
            java.lang.String r4 = "range start greater than end in starred-relation string"
            r7.setParseError(r4)
            return
        L90:
            int r1 = r1 + 1
            if (r1 > r3) goto Lcd
            android.icu.text.Normalizer2 r5 = r7.nfd
            boolean r5 = r5.isInert(r1)
            if (r5 != 0) goto La2
            java.lang.String r4 = "starred-relation string range is not all NFD-inert"
            r7.setParseError(r4)
            return
        La2:
            boolean r5 = isSurrogate(r1)
            if (r5 == 0) goto Lae
            java.lang.String r4 = "starred-relation string range contains a surrogate"
            r7.setParseError(r4)
            return
        Lae:
            r5 = 65533(0xfffd, float:9.1831E-41)
            if (r5 > r1) goto Lbe
            r5 = 65535(0xffff, float:9.1834E-41)
            if (r1 > r5) goto Lbe
            java.lang.String r4 = "starred-relation string range contains U+FFFD, U+FFFE or U+FFFF"
            r7.setParseError(r4)
            return
        Lbe:
            android.icu.impl.coll.CollationRuleParser$Sink r5 = r7.sink     // Catch: java.lang.Exception -> Lc8
            java.lang.String r6 = android.icu.text.UTF16.valueOf(r1)     // Catch: java.lang.Exception -> Lc8
            r5.addRelation(r8, r0, r6, r0)     // Catch: java.lang.Exception -> Lc8
            goto L90
        Lc8:
            r5 = move-exception
            r7.setParseError(r4, r5)
            return
        Lcd:
            r1 = -1
            int r2 = java.lang.Character.charCount(r3)
            goto L1c
        Ld4:
            int r3 = r7.skipWhiteSpace(r9)
            r7.ruleIndex = r3
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationRuleParser.parseStarredCharacters(int, int):void");
    }

    private int parseTailoringString(int i, StringBuilder raw) throws ParseException {
        int i2 = parseString(skipWhiteSpace(i), raw);
        if (raw.length() == 0) {
            setParseError("missing relation string");
        }
        return skipWhiteSpace(i2);
    }

    private int parseString(int i, StringBuilder raw) throws ParseException {
        raw.setLength(0);
        while (true) {
            if (i >= this.rules.length()) {
                break;
            }
            int i2 = i + 1;
            char c2 = this.rules.charAt(i);
            if (isSyntaxChar(c2)) {
                if (c2 == '\'') {
                    if (i2 < this.rules.length() && this.rules.charAt(i2) == '\'') {
                        raw.append(PatternTokenizer.SINGLE_QUOTE);
                        i = i2 + 1;
                    } else {
                        while (i2 != this.rules.length()) {
                            int i3 = i2 + 1;
                            char c3 = this.rules.charAt(i2);
                            if (c3 == '\'') {
                                if (i3 < this.rules.length() && this.rules.charAt(i3) == '\'') {
                                    i3++;
                                } else {
                                    i = i3;
                                }
                            }
                            i2 = i3;
                            raw.append(c3);
                        }
                        setParseError("quoted literal text missing terminating apostrophe");
                        return i2;
                    }
                } else if (c2 == '\\') {
                    if (i2 == this.rules.length()) {
                        setParseError("backslash escape at the end of the rule string");
                        return i2;
                    }
                    int cp = this.rules.codePointAt(i2);
                    raw.appendCodePoint(cp);
                    i = i2 + Character.charCount(cp);
                } else {
                    i = i2 - 1;
                    break;
                }
            } else {
                if (PatternProps.isWhiteSpace(c2)) {
                    i = i2 - 1;
                    break;
                }
                raw.append(c2);
                i = i2;
            }
        }
        int j = 0;
        while (j < raw.length()) {
            int c4 = raw.codePointAt(j);
            if (isSurrogate(c4)) {
                setParseError("string contains an unpaired surrogate");
                return i;
            }
            if (65533 <= c4 && c4 <= 65535) {
                setParseError("string contains U+FFFD, U+FFFE or U+FFFF");
                return i;
            }
            j += Character.charCount(c4);
        }
        return i;
    }

    private static final boolean isSurrogate(int c2) {
        return (c2 & (-2048)) == 55296;
    }

    private int parseSpecialPosition(int i, StringBuilder str) throws ParseException {
        int j = readWords(i + 1, this.rawBuilder);
        if (j > i && this.rules.charAt(j) == ']' && this.rawBuilder.length() != 0) {
            int j2 = j + 1;
            String raw = this.rawBuilder.toString();
            str.setLength(0);
            int pos = 0;
            while (true) {
                String[] strArr = positions;
                if (pos < strArr.length) {
                    if (!raw.equals(strArr[pos])) {
                        pos++;
                    } else {
                        str.append(POS_LEAD);
                        str.append((char) (pos + 10240));
                        return j2;
                    }
                } else {
                    if (raw.equals("top")) {
                        str.append(POS_LEAD);
                        str.append((char) (Position.LAST_REGULAR.ordinal() + 10240));
                        return j2;
                    }
                    if (raw.equals("variable top")) {
                        str.append(POS_LEAD);
                        str.append((char) (Position.LAST_VARIABLE.ordinal() + 10240));
                        return j2;
                    }
                }
            }
        }
        setParseError("not a valid special reset position");
        return i;
    }

    private void parseSetting() throws ParseException {
        String v;
        int i = this.ruleIndex + 1;
        int j = readWords(i, this.rawBuilder);
        if (j <= i || this.rawBuilder.length() == 0) {
            setParseError("expected a setting/option at '['");
        }
        String raw = this.rawBuilder.toString();
        if (this.rules.charAt(j) == ']') {
            int j2 = j + 1;
            if (raw.startsWith("reorder") && (raw.length() == 7 || raw.charAt(7) == ' ')) {
                parseReordering(raw);
                this.ruleIndex = j2;
                return;
            }
            if (raw.equals("backwards 2")) {
                this.settings.setFlag(2048, true);
                this.ruleIndex = j2;
                return;
            }
            int valueIndex = raw.lastIndexOf(32);
            if (valueIndex >= 0) {
                v = raw.substring(valueIndex + 1);
                raw = raw.substring(0, valueIndex);
            } else {
                v = "";
            }
            if (raw.equals("strength") && v.length() == 1) {
                int value = -1;
                char c2 = v.charAt(0);
                if ('1' <= c2 && c2 <= '4') {
                    value = (c2 - '1') + 0;
                } else if (c2 == 'I') {
                    value = 15;
                }
                if (value != -1) {
                    this.settings.setStrength(value);
                    this.ruleIndex = j2;
                    return;
                }
            } else if (raw.equals("alternate")) {
                int value2 = -1;
                if (v.equals("non-ignorable")) {
                    value2 = 0;
                } else if (v.equals("shifted")) {
                    value2 = 1;
                }
                if (value2 != -1) {
                    this.settings.setAlternateHandlingShifted(value2 > 0);
                    this.ruleIndex = j2;
                    return;
                }
            } else if (raw.equals("maxVariable")) {
                int value3 = -1;
                if (v.equals("space")) {
                    value3 = 0;
                } else if (v.equals("punct")) {
                    value3 = 1;
                } else if (v.equals("symbol")) {
                    value3 = 2;
                } else if (v.equals(MiStat.Param.CURRENCY)) {
                    value3 = 3;
                }
                if (value3 != -1) {
                    this.settings.setMaxVariable(value3, 0);
                    this.settings.variableTop = this.baseData.getLastPrimaryForGroup(value3 + 4096);
                    this.ruleIndex = j2;
                    return;
                }
            } else if (raw.equals("caseFirst")) {
                int value4 = -1;
                if (v.equals("off")) {
                    value4 = 0;
                } else if (v.equals("lower")) {
                    value4 = 512;
                } else if (v.equals("upper")) {
                    value4 = 768;
                }
                if (value4 != -1) {
                    this.settings.setCaseFirst(value4);
                    this.ruleIndex = j2;
                    return;
                }
            } else if (raw.equals("caseLevel")) {
                int value5 = getOnOffValue(v);
                if (value5 != -1) {
                    this.settings.setFlag(1024, value5 > 0);
                    this.ruleIndex = j2;
                    return;
                }
            } else if (raw.equals("normalization")) {
                int value6 = getOnOffValue(v);
                if (value6 != -1) {
                    this.settings.setFlag(1, value6 > 0);
                    this.ruleIndex = j2;
                    return;
                }
            } else if (raw.equals("numericOrdering")) {
                int value7 = getOnOffValue(v);
                if (value7 != -1) {
                    this.settings.setFlag(2, value7 > 0);
                    this.ruleIndex = j2;
                    return;
                }
            } else if (raw.equals("hiraganaQ")) {
                int value8 = getOnOffValue(v);
                if (value8 != -1) {
                    if (value8 == 1) {
                        setParseError("[hiraganaQ on] is not supported");
                    }
                    this.ruleIndex = j2;
                    return;
                }
            } else if (raw.equals(Constants.ELEMNAME_IMPORT_STRING)) {
                try {
                    ULocale localeID = new ULocale.Builder().setLanguageTag(v).build();
                    String baseID = localeID.getBaseName();
                    String collationType = localeID.getKeywordValue("collation");
                    Importer importer = this.importer;
                    if (importer == null) {
                        setParseError("[import langTag] is not supported");
                        return;
                    }
                    try {
                        String importedRules = importer.getRules(baseID, collationType != null ? collationType : "standard");
                        String outerRules = this.rules;
                        int outerRuleIndex = this.ruleIndex;
                        try {
                            parse(importedRules);
                        } catch (Exception e) {
                            this.ruleIndex = outerRuleIndex;
                            setParseError("parsing imported rules failed", e);
                        }
                        this.rules = outerRules;
                        this.ruleIndex = j2;
                        return;
                    } catch (Exception e2) {
                        setParseError("[import langTag] failed", e2);
                        return;
                    }
                } catch (Exception e3) {
                    setParseError("expected language tag in [import langTag]", e3);
                    return;
                }
            }
        } else if (this.rules.charAt(j) == '[') {
            UnicodeSet set = new UnicodeSet();
            int j3 = parseUnicodeSet(j, set);
            if (raw.equals("optimize")) {
                try {
                    this.sink.optimize(set);
                } catch (Exception e4) {
                    setParseError("[optimize set] failed", e4);
                }
                this.ruleIndex = j3;
                return;
            }
            if (raw.equals("suppressContractions")) {
                try {
                    this.sink.suppressContractions(set);
                } catch (Exception e5) {
                    setParseError("[suppressContractions set] failed", e5);
                }
                this.ruleIndex = j3;
                return;
            }
        }
        setParseError("not a valid setting/option");
    }

    private void parseReordering(CharSequence raw) throws ParseException {
        int limit;
        if (7 == raw.length()) {
            this.settings.resetReordering();
            return;
        }
        ArrayList<Integer> reorderCodes = new ArrayList<>();
        for (int i = 7; i < raw.length(); i = limit) {
            int i2 = i + 1;
            limit = i2;
            while (limit < raw.length() && raw.charAt(limit) != ' ') {
                limit++;
            }
            String word = raw.subSequence(i2, limit).toString();
            int code = getReorderCode(word);
            if (code < 0) {
                setParseError("unknown script or reorder code");
                return;
            }
            reorderCodes.add(Integer.valueOf(code));
        }
        if (reorderCodes.isEmpty()) {
            this.settings.resetReordering();
            return;
        }
        int[] codes = new int[reorderCodes.size()];
        int j = 0;
        Iterator<Integer> itIterator2 = reorderCodes.iterator2();
        while (itIterator2.hasNext()) {
            codes[j] = itIterator2.mo35924next().intValue();
            j++;
        }
        this.settings.setReordering(this.baseData, codes);
    }

    public static int getReorderCode(String word) {
        int i = 0;
        while (true) {
            String[] strArr = gSpecialReorderCodes;
            if (i < strArr.length) {
                if (!word.equalsIgnoreCase(strArr[i])) {
                    i++;
                } else {
                    return i + 4096;
                }
            } else {
                try {
                    int script = UCharacter.getPropertyValueEnum(UProperty.SCRIPT, word);
                    if (script >= 0) {
                        return script;
                    }
                } catch (IllegalIcuArgumentException e) {
                }
                if (word.equalsIgnoreCase("others")) {
                    return 103;
                }
                return -1;
            }
        }
    }

    private static int getOnOffValue(String s) {
        if (s.equals("on")) {
            return 1;
        }
        if (s.equals("off")) {
            return 0;
        }
        return -1;
    }

    private int parseUnicodeSet(int i, UnicodeSet set) throws ParseException {
        int level = 0;
        int j = i;
        while (j != this.rules.length()) {
            int j2 = j + 1;
            char c2 = this.rules.charAt(j);
            if (c2 == '[') {
                level++;
            } else if (c2 == ']' && level - 1 == 0) {
                try {
                    set.applyPattern(this.rules.substring(i, j2));
                } catch (Exception e) {
                    setParseError("not a valid UnicodeSet pattern: " + e.getMessage());
                }
                int j3 = skipWhiteSpace(j2);
                if (j3 == this.rules.length() || this.rules.charAt(j3) != ']') {
                    setParseError("missing option-terminating ']' after UnicodeSet pattern");
                    return j3;
                }
                return j3 + 1;
            }
            j = j2;
        }
        setParseError("unbalanced UnicodeSet pattern brackets");
        return j;
    }

    private int readWords(int i, StringBuilder raw) {
        raw.setLength(0);
        int i2 = skipWhiteSpace(i);
        while (i2 < this.rules.length()) {
            char c2 = this.rules.charAt(i2);
            if (isSyntaxChar(c2) && c2 != '-' && c2 != '_') {
                if (raw.length() == 0) {
                    return i2;
                }
                int lastIndex = raw.length() - 1;
                if (raw.charAt(lastIndex) == ' ') {
                    raw.setLength(lastIndex);
                }
                return i2;
            }
            if (PatternProps.isWhiteSpace(c2)) {
                raw.append(' ');
                i2 = skipWhiteSpace(i2 + 1);
            } else {
                raw.append(c2);
                i2++;
            }
        }
        return 0;
    }

    private int skipComment(int i) {
        while (i < this.rules.length()) {
            int i2 = i + 1;
            char c2 = this.rules.charAt(i);
            if (c2 != '\n' && c2 != '\f' && c2 != '\r' && c2 != 133 && c2 != 8232 && c2 != 8233) {
                i = i2;
            } else {
                return i2;
            }
        }
        return i;
    }

    private void setParseError(String reason) throws ParseException {
        throw makeParseException(reason);
    }

    private void setParseError(String reason, Exception e) throws ParseException {
        ParseException newExc = makeParseException(reason + PluralRules.KEYWORD_RULE_SEPARATOR + e.getMessage());
        newExc.initCause(e);
        throw newExc;
    }

    private ParseException makeParseException(String reason) {
        return new ParseException(appendErrorContext(reason), this.ruleIndex);
    }

    private String appendErrorContext(String reason) {
        StringBuilder msg = new StringBuilder(reason);
        msg.append(" at index ");
        msg.append(this.ruleIndex);
        msg.append(" near \"");
        int start = this.ruleIndex - 15;
        if (start < 0) {
            start = 0;
        } else if (start > 0 && Character.isLowSurrogate(this.rules.charAt(start))) {
            start++;
        }
        msg.append((CharSequence) this.rules, start, this.ruleIndex);
        msg.append('!');
        int length = this.rules.length() - this.ruleIndex;
        if (length >= 16) {
            length = 15;
            if (Character.isHighSurrogate(this.rules.charAt((r3 + 15) - 1))) {
                length = 15 - 1;
            }
        }
        String str = this.rules;
        int i = this.ruleIndex;
        msg.append((CharSequence) str, i, i + length);
        msg.append(StringUtil.DOUBLE_QUOTE);
        return msg.toString();
    }

    private static boolean isSyntaxChar(int c2) {
        return 33 <= c2 && c2 <= 126 && (c2 <= 47 || ((58 <= c2 && c2 <= 64) || ((91 <= c2 && c2 <= 96) || 123 <= c2)));
    }

    private int skipWhiteSpace(int i) {
        while (i < this.rules.length() && PatternProps.isWhiteSpace(this.rules.charAt(i))) {
            i++;
        }
        return i;
    }
}
