package aegon.chrome.base;

import aegon.chrome.base.Callback;
import android.os.Handler;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class Promise<T> {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private Exception mRejectReason;
    private T mResult;
    private boolean mThrowingRejectionHandler;
    private int mState = 0;
    private final List<Callback<T>> mFulfillCallbacks = new LinkedList();
    private final List<Callback<Exception>> mRejectCallbacks = new LinkedList();
    private final Thread mThread = Thread.currentThread();
    private final Handler mHandler = new Handler();

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    public interface AsyncFunction<A, RT> extends aegon.chrome.base.Function<A, Promise<RT>> {
    }

    /* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
    public interface Function<A, R> {
        R apply(A a2);
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    @Retention(RetentionPolicy.SOURCE)
    @interface PromiseState {
        public static final int FULFILLED = 1;
        public static final int REJECTED = 2;
        public static final int UNFULFILLED = 0;
    }

    private void checkThread() {
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    public static class UnhandledRejectionException extends RuntimeException {
        public UnhandledRejectionException(String str, Throwable th) {
            super(str, th);
        }
    }

    public void then(Callback<T> callback) {
        checkThread();
        if (this.mThrowingRejectionHandler) {
            thenInner(callback);
        } else {
            then(callback, new Callback() { // from class: aegon.chrome.base.-$$Lambda$Promise$KemTygNd-7Ua98UpdB1hOI7LOK4
                @Override // aegon.chrome.base.Callback
                public /* synthetic */ Runnable bind(T t) {
                    return Callback.CC.$default$bind(this, t);
                }

                @Override // aegon.chrome.base.Callback
                public final void onResult(Object obj) {
                    Promise.lambda$then$0((Exception) obj);
                }
            });
            this.mThrowingRejectionHandler = true;
        }
    }

    static /* synthetic */ void lambda$then$0(Exception exc) {
        throw new UnhandledRejectionException("Promise was rejected without a rejection handler.", exc);
    }

    public void then(Callback<T> callback, Callback<Exception> callback2) {
        checkThread();
        thenInner(callback);
        exceptInner(callback2);
    }

    public void except(Callback<Exception> callback) {
        checkThread();
        exceptInner(callback);
    }

    /* JADX WARN: Multi-variable type inference failed */
    private void thenInner(Callback<T> callback) {
        int i = this.mState;
        if (i == 1) {
            postCallbackToLooper(callback, this.mResult);
        } else if (i == 0) {
            this.mFulfillCallbacks.add(callback);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    private void exceptInner(Callback<Exception> callback) {
        int i = this.mState;
        if (i == 2) {
            postCallbackToLooper(callback, this.mRejectReason);
        } else if (i == 0) {
            this.mRejectCallbacks.add(callback);
        }
    }

    public <RT> Promise<RT> then(final aegon.chrome.base.Function<T, RT> function) {
        checkThread();
        final Promise<RT> promise = new Promise<>();
        thenInner(new Callback() { // from class: aegon.chrome.base.-$$Lambda$Promise$ULeetAErSpEqhAAWtXuKWOzODuw
            @Override // aegon.chrome.base.Callback
            public /* synthetic */ Runnable bind(T t) {
                return Callback.CC.$default$bind(this, t);
            }

            @Override // aegon.chrome.base.Callback
            public final void onResult(Object obj) {
                Promise.lambda$then$1(this.f$0, function, obj);
            }
        });
        promise.getClass();
        exceptInner(new $$Lambda$AOL8pt032rTX4jFbGE2UEfshjrw(promise));
        return promise;
    }

    /* JADX WARN: Multi-variable type inference failed */
    static /* synthetic */ void lambda$then$1(Promise promise, aegon.chrome.base.Function function, Object obj) {
        try {
            promise.fulfill(function.apply(obj));
        } catch (Exception e) {
            promise.reject(e);
        }
    }

    public <RT> Promise<RT> then(final AsyncFunction<T, RT> asyncFunction) {
        checkThread();
        final Promise<RT> promise = new Promise<>();
        thenInner(new Callback() { // from class: aegon.chrome.base.-$$Lambda$Promise$lwkJrSqeuDUnq-p_LglHC6jFRvE
            @Override // aegon.chrome.base.Callback
            public /* synthetic */ Runnable bind(T t) {
                return Callback.CC.$default$bind(this, t);
            }

            @Override // aegon.chrome.base.Callback
            public final void onResult(Object obj) {
                Promise.lambda$then$2(asyncFunction, promise, obj);
            }
        });
        promise.getClass();
        exceptInner(new $$Lambda$AOL8pt032rTX4jFbGE2UEfshjrw(promise));
        return promise;
    }

    static /* synthetic */ void lambda$then$2(AsyncFunction asyncFunction, final Promise promise, Object obj) {
        try {
            Promise promiseApply = asyncFunction.apply(obj);
            promise.getClass();
            Callback<T> callback = new Callback() { // from class: aegon.chrome.base.-$$Lambda$0xjs32ZvubCVEcC61ugD33zO3ZA
                @Override // aegon.chrome.base.Callback
                public /* synthetic */ Runnable bind(T t) {
                    return Callback.CC.$default$bind(this, t);
                }

                @Override // aegon.chrome.base.Callback
                public final void onResult(Object obj2) {
                    this.f$0.fulfill(obj2);
                }
            };
            promise.getClass();
            promiseApply.then(callback, new $$Lambda$AOL8pt032rTX4jFbGE2UEfshjrw(promise));
        } catch (Exception e) {
            promise.reject(e);
        }
    }

    public void fulfill(T t) {
        checkThread();
        this.mState = 1;
        this.mResult = t;
        Iterator<Callback<T>> itIterator2 = this.mFulfillCallbacks.iterator2();
        while (itIterator2.hasNext()) {
            postCallbackToLooper(itIterator2.mo35924next(), t);
        }
        this.mFulfillCallbacks.clear();
    }

    public void reject(Exception exc) {
        checkThread();
        this.mState = 2;
        this.mRejectReason = exc;
        Iterator<Callback<Exception>> itIterator2 = this.mRejectCallbacks.iterator2();
        while (itIterator2.hasNext()) {
            postCallbackToLooper((Callback) itIterator2.mo35924next(), exc);
        }
        this.mRejectCallbacks.clear();
    }

    public void reject() {
        reject(null);
    }

    public boolean isFulfilled() {
        checkThread();
        return this.mState == 1;
    }

    public boolean isRejected() {
        checkThread();
        return this.mState == 2;
    }

    public T getResult() {
        return this.mResult;
    }

    public static <T> Promise<T> fulfilled(T t) {
        Promise<T> promise = new Promise<>();
        promise.fulfill(t);
        return promise;
    }

    public static <T> Promise<T> rejected() {
        Promise<T> promise = new Promise<>();
        promise.reject();
        return promise;
    }

    private <S> void postCallbackToLooper(Callback<S> callback, S s) {
        this.mHandler.post(callback.bind(s));
    }
}
