package android.icu.impl.coll;

import android.icu.impl.Normalizer2Impl;
import android.icu.text.UCharacterIterator;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class FCDIterCollationIterator extends IterCollationIterator {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private int limit;
    private final Normalizer2Impl nfcImpl;
    private StringBuilder normalized;
    private int pos;

    /* renamed from: s */
    private StringBuilder f60s;
    private int start;
    private State state;

    private enum State {
        ITER_CHECK_FWD,
        ITER_CHECK_BWD,
        ITER_IN_FCD_SEGMENT,
        IN_NORM_ITER_AT_LIMIT,
        IN_NORM_ITER_AT_START
    }

    public FCDIterCollationIterator(CollationData data, boolean numeric, UCharacterIterator ui, int startIndex) {
        super(data, numeric, ui);
        this.state = State.ITER_CHECK_FWD;
        this.start = startIndex;
        this.nfcImpl = data.nfcImpl;
    }

    @Override // android.icu.impl.coll.IterCollationIterator, android.icu.impl.coll.CollationIterator
    public void resetToOffset(int newOffset) {
        super.resetToOffset(newOffset);
        this.start = newOffset;
        this.state = State.ITER_CHECK_FWD;
    }

    @Override // android.icu.impl.coll.IterCollationIterator, android.icu.impl.coll.CollationIterator
    public int getOffset() {
        if (this.state.compareTo(State.ITER_CHECK_BWD) <= 0) {
            return this.iter.getIndex();
        }
        if (this.state == State.ITER_IN_FCD_SEGMENT) {
            return this.pos;
        }
        if (this.pos == 0) {
            return this.start;
        }
        return this.limit;
    }

    /* JADX WARN: Code restructure failed: missing block: B:18:0x0038, code lost:
    
        if (isLeadSurrogate(r0) == false) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x003a, code lost:
    
        r1 = r4.iter.next();
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x0044, code lost:
    
        if (isTrailSurrogate(r1) == false) goto L23;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x004c, code lost:
    
        return java.lang.Character.toCodePoint((char) r0, (char) r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x004d, code lost:
    
        if (r1 < 0) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x004f, code lost:
    
        r4.iter.previous();
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0054, code lost:
    
        return r0;
     */
    @Override // android.icu.impl.coll.IterCollationIterator, android.icu.impl.coll.CollationIterator
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public int nextCodePoint() {
        /*
            r4 = this;
        L0:
            android.icu.impl.coll.FCDIterCollationIterator$State r0 = r4.state
            android.icu.impl.coll.FCDIterCollationIterator$State r1 = android.icu.impl.coll.FCDIterCollationIterator.State.ITER_CHECK_FWD
            if (r0 != r1) goto L55
            android.icu.text.UCharacterIterator r0 = r4.iter
            int r0 = r0.next()
            if (r0 >= 0) goto Lf
            return r0
        Lf:
            boolean r1 = android.icu.impl.coll.CollationFCD.hasTccc(r0)
            if (r1 == 0) goto L34
            boolean r1 = android.icu.impl.coll.CollationFCD.maybeTibetanCompositeVowel(r0)
            if (r1 != 0) goto L27
            android.icu.text.UCharacterIterator r1 = r4.iter
            int r1 = r1.current()
            boolean r1 = android.icu.impl.coll.CollationFCD.hasLccc(r1)
            if (r1 == 0) goto L34
        L27:
            android.icu.text.UCharacterIterator r1 = r4.iter
            r1.previous()
            boolean r1 = r4.nextSegment()
            if (r1 != 0) goto L0
            r1 = -1
            return r1
        L34:
            boolean r1 = isLeadSurrogate(r0)
            if (r1 == 0) goto L54
            android.icu.text.UCharacterIterator r1 = r4.iter
            int r1 = r1.next()
            boolean r2 = isTrailSurrogate(r1)
            if (r2 == 0) goto L4d
            char r2 = (char) r0
            char r3 = (char) r1
            int r2 = java.lang.Character.toCodePoint(r2, r3)
            return r2
        L4d:
            if (r1 < 0) goto L54
            android.icu.text.UCharacterIterator r2 = r4.iter
            r2.previous()
        L54:
            return r0
        L55:
            android.icu.impl.coll.FCDIterCollationIterator$State r0 = r4.state
            android.icu.impl.coll.FCDIterCollationIterator$State r1 = android.icu.impl.coll.FCDIterCollationIterator.State.ITER_IN_FCD_SEGMENT
            if (r0 != r1) goto L72
            int r0 = r4.pos
            int r1 = r4.limit
            if (r0 == r1) goto L72
            android.icu.text.UCharacterIterator r0 = r4.iter
            int r0 = r0.nextCodePoint()
            int r1 = r4.pos
            int r2 = java.lang.Character.charCount(r0)
            int r1 = r1 + r2
            r4.pos = r1
            return r0
        L72:
            android.icu.impl.coll.FCDIterCollationIterator$State r0 = r4.state
            android.icu.impl.coll.FCDIterCollationIterator$State r1 = android.icu.impl.coll.FCDIterCollationIterator.State.IN_NORM_ITER_AT_LIMIT
            int r0 = r0.compareTo(r1)
            if (r0 < 0) goto L98
            int r0 = r4.pos
            java.lang.StringBuilder r1 = r4.normalized
            int r1 = r1.length()
            if (r0 == r1) goto L98
            java.lang.StringBuilder r0 = r4.normalized
            int r1 = r4.pos
            int r0 = r0.codePointAt(r1)
            int r1 = r4.pos
            int r2 = java.lang.Character.charCount(r0)
            int r1 = r1 + r2
            r4.pos = r1
            return r0
        L98:
            r4.switchToForward()
            goto L0
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.FCDIterCollationIterator.nextCodePoint():int");
    }

    /* JADX WARN: Code restructure failed: missing block: B:31:0x006a, code lost:
    
        return r0;
     */
    @Override // android.icu.impl.coll.IterCollationIterator, android.icu.impl.coll.CollationIterator
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public int previousCodePoint() {
        /*
            r4 = this;
        L0:
            android.icu.impl.coll.FCDIterCollationIterator$State r0 = r4.state
            android.icu.impl.coll.FCDIterCollationIterator$State r1 = android.icu.impl.coll.FCDIterCollationIterator.State.ITER_CHECK_BWD
            if (r0 != r1) goto L6b
            android.icu.text.UCharacterIterator r0 = r4.iter
            int r0 = r0.previous()
            r1 = -1
            if (r0 >= 0) goto L19
            r2 = 0
            r4.pos = r2
            r4.start = r2
            android.icu.impl.coll.FCDIterCollationIterator$State r2 = android.icu.impl.coll.FCDIterCollationIterator.State.ITER_IN_FCD_SEGMENT
            r4.state = r2
            return r1
        L19:
            boolean r2 = android.icu.impl.coll.CollationFCD.hasLccc(r0)
            if (r2 == 0) goto L6a
            r2 = -1
            boolean r3 = android.icu.impl.coll.CollationFCD.maybeTibetanCompositeVowel(r0)
            if (r3 != 0) goto L57
            android.icu.text.UCharacterIterator r3 = r4.iter
            int r3 = r3.previous()
            r2 = r3
            boolean r3 = android.icu.impl.coll.CollationFCD.hasTccc(r3)
            if (r3 == 0) goto L34
            goto L57
        L34:
            boolean r1 = isTrailSurrogate(r0)
            if (r1 == 0) goto L4f
            if (r2 >= 0) goto L42
            android.icu.text.UCharacterIterator r1 = r4.iter
            int r2 = r1.previous()
        L42:
            boolean r1 = isLeadSurrogate(r2)
            if (r1 == 0) goto L4f
            char r1 = (char) r2
            char r3 = (char) r0
            int r1 = java.lang.Character.toCodePoint(r1, r3)
            return r1
        L4f:
            if (r2 < 0) goto L6a
            android.icu.text.UCharacterIterator r1 = r4.iter
            r1.next()
            goto L6a
        L57:
            android.icu.text.UCharacterIterator r3 = r4.iter
            r3.next()
            if (r2 < 0) goto L63
            android.icu.text.UCharacterIterator r3 = r4.iter
            r3.next()
        L63:
            boolean r3 = r4.previousSegment()
            if (r3 != 0) goto L0
            return r1
        L6a:
            return r0
        L6b:
            android.icu.impl.coll.FCDIterCollationIterator$State r0 = r4.state
            android.icu.impl.coll.FCDIterCollationIterator$State r1 = android.icu.impl.coll.FCDIterCollationIterator.State.ITER_IN_FCD_SEGMENT
            if (r0 != r1) goto L88
            int r0 = r4.pos
            int r1 = r4.start
            if (r0 == r1) goto L88
            android.icu.text.UCharacterIterator r0 = r4.iter
            int r0 = r0.previousCodePoint()
            int r1 = r4.pos
            int r2 = java.lang.Character.charCount(r0)
            int r1 = r1 - r2
            r4.pos = r1
            return r0
        L88:
            android.icu.impl.coll.FCDIterCollationIterator$State r0 = r4.state
            android.icu.impl.coll.FCDIterCollationIterator$State r1 = android.icu.impl.coll.FCDIterCollationIterator.State.IN_NORM_ITER_AT_LIMIT
            int r0 = r0.compareTo(r1)
            if (r0 < 0) goto La6
            int r0 = r4.pos
            if (r0 == 0) goto La6
            java.lang.StringBuilder r1 = r4.normalized
            int r0 = r1.codePointBefore(r0)
            int r1 = r4.pos
            int r2 = java.lang.Character.charCount(r0)
            int r1 = r1 - r2
            r4.pos = r1
            return r0
        La6:
            r4.switchToBackward()
            goto L0
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.FCDIterCollationIterator.previousCodePoint():int");
    }

    @Override // android.icu.impl.coll.IterCollationIterator, android.icu.impl.coll.CollationIterator
    protected long handleNextCE32() {
        int c2;
        while (true) {
            if (this.state == State.ITER_CHECK_FWD) {
                c2 = this.iter.next();
                if (c2 < 0) {
                    return -4294967104L;
                }
                if (!CollationFCD.hasTccc(c2) || (!CollationFCD.maybeTibetanCompositeVowel(c2) && !CollationFCD.hasLccc(this.iter.current()))) {
                    break;
                }
                this.iter.previous();
                if (!nextSegment()) {
                    return 192L;
                }
            } else {
                if (this.state == State.ITER_IN_FCD_SEGMENT && this.pos != this.limit) {
                    c2 = this.iter.next();
                    this.pos++;
                    break;
                }
                if (this.state.compareTo(State.IN_NORM_ITER_AT_LIMIT) >= 0 && this.pos != this.normalized.length()) {
                    StringBuilder sb = this.normalized;
                    int i = this.pos;
                    this.pos = i + 1;
                    c2 = sb.charAt(i);
                    break;
                }
                switchToForward();
            }
        }
        return makeCodePointAndCE32Pair(c2, this.trie.getFromU16SingleLead((char) c2));
    }

    @Override // android.icu.impl.coll.IterCollationIterator, android.icu.impl.coll.CollationIterator
    protected char handleGetTrailSurrogate() {
        if (this.state.compareTo(State.ITER_IN_FCD_SEGMENT) <= 0) {
            int trail = this.iter.next();
            if (isTrailSurrogate(trail)) {
                if (this.state == State.ITER_IN_FCD_SEGMENT) {
                    this.pos++;
                }
            } else if (trail >= 0) {
                this.iter.previous();
            }
            return (char) trail;
        }
        char trail2 = this.normalized.charAt(this.pos);
        if (Character.isLowSurrogate(trail2)) {
            this.pos++;
        }
        return trail2;
    }

    @Override // android.icu.impl.coll.IterCollationIterator, android.icu.impl.coll.CollationIterator
    protected void forwardNumCodePoints(int num) {
        while (num > 0 && nextCodePoint() >= 0) {
            num--;
        }
    }

    @Override // android.icu.impl.coll.IterCollationIterator, android.icu.impl.coll.CollationIterator
    protected void backwardNumCodePoints(int num) {
        while (num > 0 && previousCodePoint() >= 0) {
            num--;
        }
    }

    private void switchToForward() {
        if (this.state == State.ITER_CHECK_BWD) {
            int index = this.iter.getIndex();
            this.pos = index;
            this.start = index;
            if (index == this.limit) {
                this.state = State.ITER_CHECK_FWD;
                return;
            } else {
                this.state = State.ITER_IN_FCD_SEGMENT;
                return;
            }
        }
        if (this.state != State.ITER_IN_FCD_SEGMENT) {
            if (this.state == State.IN_NORM_ITER_AT_START) {
                this.iter.moveIndex(this.limit - this.start);
            }
            this.start = this.limit;
        }
        this.state = State.ITER_CHECK_FWD;
    }

    /* JADX WARN: Code restructure failed: missing block: B:20:0x004b, code lost:
    
        r2 = r8.iter.nextCodePoint();
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0051, code lost:
    
        if (r2 >= 0) goto L23;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x005c, code lost:
    
        if (r8.nfcImpl.getFCD16(r2) > 255) goto L28;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x005e, code lost:
    
        r8.iter.previousCodePoint();
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0064, code lost:
    
        normalize(r8.f60s);
        r6 = r8.pos;
        r8.start = r6;
        r8.limit = r6 + r8.f60s.length();
        r8.state = android.icu.impl.coll.FCDIterCollationIterator.State.IN_NORM_ITER_AT_LIMIT;
        r8.pos = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x007c, code lost:
    
        return true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x007d, code lost:
    
        r8.f60s.appendCodePoint(r2);
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private boolean nextSegment() {
        /*
            r8 = this;
            android.icu.text.UCharacterIterator r0 = r8.iter
            int r0 = r0.getIndex()
            r8.pos = r0
            java.lang.StringBuilder r0 = r8.f60s
            r1 = 0
            if (r0 != 0) goto L16
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            r8.f60s = r0
            goto L19
        L16:
            r0.setLength(r1)
        L19:
            r0 = 0
        L1a:
            android.icu.text.UCharacterIterator r2 = r8.iter
            int r2 = r2.nextCodePoint()
            r3 = 1
            if (r2 >= 0) goto L24
            goto L88
        L24:
            android.icu.impl.Normalizer2Impl r4 = r8.nfcImpl
            int r4 = r4.getFCD16(r2)
            int r5 = r4 >> 8
            if (r5 != 0) goto L3c
            java.lang.StringBuilder r6 = r8.f60s
            int r6 = r6.length()
            if (r6 == 0) goto L3c
            android.icu.text.UCharacterIterator r1 = r8.iter
            r1.previousCodePoint()
            goto L88
        L3c:
            java.lang.StringBuilder r6 = r8.f60s
            r6.appendCodePoint(r2)
            if (r5 == 0) goto L83
            if (r0 > r5) goto L4b
            boolean r6 = android.icu.impl.coll.CollationFCD.isFCD16OfTibetanCompositeVowel(r4)
            if (r6 == 0) goto L83
        L4b:
            android.icu.text.UCharacterIterator r6 = r8.iter
            int r2 = r6.nextCodePoint()
            if (r2 >= 0) goto L54
            goto L64
        L54:
            android.icu.impl.Normalizer2Impl r6 = r8.nfcImpl
            int r6 = r6.getFCD16(r2)
            r7 = 255(0xff, float:3.57E-43)
            if (r6 > r7) goto L7d
            android.icu.text.UCharacterIterator r6 = r8.iter
            r6.previousCodePoint()
        L64:
            java.lang.StringBuilder r6 = r8.f60s
            r8.normalize(r6)
            int r6 = r8.pos
            r8.start = r6
            java.lang.StringBuilder r7 = r8.f60s
            int r7 = r7.length()
            int r6 = r6 + r7
            r8.limit = r6
            android.icu.impl.coll.FCDIterCollationIterator$State r6 = android.icu.impl.coll.FCDIterCollationIterator.State.IN_NORM_ITER_AT_LIMIT
            r8.state = r6
            r8.pos = r1
            return r3
        L7d:
            java.lang.StringBuilder r6 = r8.f60s
            r6.appendCodePoint(r2)
            goto L4b
        L83:
            r0 = r4 & 255(0xff, float:3.57E-43)
            if (r0 != 0) goto La5
        L88:
            int r1 = r8.pos
            java.lang.StringBuilder r2 = r8.f60s
            int r2 = r2.length()
            int r1 = r1 + r2
            r8.limit = r1
            android.icu.text.UCharacterIterator r1 = r8.iter
            java.lang.StringBuilder r2 = r8.f60s
            int r2 = r2.length()
            int r2 = -r2
            r1.moveIndex(r2)
            android.icu.impl.coll.FCDIterCollationIterator$State r1 = android.icu.impl.coll.FCDIterCollationIterator.State.ITER_IN_FCD_SEGMENT
            r8.state = r1
            return r3
        La5:
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.FCDIterCollationIterator.nextSegment():boolean");
    }

    private void switchToBackward() {
        if (this.state == State.ITER_CHECK_FWD) {
            int index = this.iter.getIndex();
            this.pos = index;
            this.limit = index;
            if (index == this.start) {
                this.state = State.ITER_CHECK_BWD;
                return;
            } else {
                this.state = State.ITER_IN_FCD_SEGMENT;
                return;
            }
        }
        if (this.state != State.ITER_IN_FCD_SEGMENT) {
            if (this.state == State.IN_NORM_ITER_AT_LIMIT) {
                this.iter.moveIndex(this.start - this.limit);
            }
            this.limit = this.start;
        }
        this.state = State.ITER_CHECK_BWD;
    }

    /* JADX WARN: Code restructure failed: missing block: B:34:0x0098, code lost:
    
        r7.start = r7.pos - r7.f60s.length();
        r7.iter.moveIndex(r7.f60s.length());
        r7.state = android.icu.impl.coll.FCDIterCollationIterator.State.ITER_IN_FCD_SEGMENT;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x00b3, code lost:
    
        return true;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private boolean previousSegment() {
        /*
            r7 = this;
            android.icu.text.UCharacterIterator r0 = r7.iter
            int r0 = r0.getIndex()
            r7.pos = r0
            java.lang.StringBuilder r0 = r7.f60s
            if (r0 != 0) goto L15
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            r7.f60s = r0
            goto L19
        L15:
            r1 = 0
            r0.setLength(r1)
        L19:
            r0 = 0
        L1a:
            android.icu.text.UCharacterIterator r1 = r7.iter
            int r1 = r1.previousCodePoint()
            r2 = 1
            if (r1 >= 0) goto L25
            goto L98
        L25:
            android.icu.impl.Normalizer2Impl r3 = r7.nfcImpl
            int r3 = r3.getFCD16(r1)
            r4 = r3 & 255(0xff, float:3.57E-43)
            if (r4 != 0) goto L3d
            java.lang.StringBuilder r5 = r7.f60s
            int r5 = r5.length()
            if (r5 == 0) goto L3d
            android.icu.text.UCharacterIterator r5 = r7.iter
            r5.nextCodePoint()
            goto L98
        L3d:
            java.lang.StringBuilder r5 = r7.f60s
            r5.appendCodePoint(r1)
            if (r4 == 0) goto L93
            if (r0 == 0) goto L48
            if (r4 > r0) goto L4e
        L48:
            boolean r5 = android.icu.impl.coll.CollationFCD.isFCD16OfTibetanCompositeVowel(r3)
            if (r5 == 0) goto L93
        L4e:
            r5 = 255(0xff, float:3.57E-43)
            if (r3 <= r5) goto L6f
            android.icu.text.UCharacterIterator r5 = r7.iter
            int r1 = r5.previousCodePoint()
            if (r1 >= 0) goto L5b
            goto L6f
        L5b:
            android.icu.impl.Normalizer2Impl r5 = r7.nfcImpl
            int r3 = r5.getFCD16(r1)
            if (r3 != 0) goto L69
            android.icu.text.UCharacterIterator r5 = r7.iter
            r5.nextCodePoint()
            goto L6f
        L69:
            java.lang.StringBuilder r5 = r7.f60s
            r5.appendCodePoint(r1)
            goto L4e
        L6f:
            java.lang.StringBuilder r5 = r7.f60s
            r5.reverse()
            java.lang.StringBuilder r5 = r7.f60s
            r7.normalize(r5)
            int r5 = r7.pos
            r7.limit = r5
            java.lang.StringBuilder r6 = r7.f60s
            int r6 = r6.length()
            int r5 = r5 - r6
            r7.start = r5
            android.icu.impl.coll.FCDIterCollationIterator$State r5 = android.icu.impl.coll.FCDIterCollationIterator.State.IN_NORM_ITER_AT_START
            r7.state = r5
            java.lang.StringBuilder r5 = r7.normalized
            int r5 = r5.length()
            r7.pos = r5
            return r2
        L93:
            int r0 = r3 >> 8
            if (r0 != 0) goto Lb4
        L98:
            int r1 = r7.pos
            java.lang.StringBuilder r3 = r7.f60s
            int r3 = r3.length()
            int r1 = r1 - r3
            r7.start = r1
            android.icu.text.UCharacterIterator r1 = r7.iter
            java.lang.StringBuilder r3 = r7.f60s
            int r3 = r3.length()
            r1.moveIndex(r3)
            android.icu.impl.coll.FCDIterCollationIterator$State r1 = android.icu.impl.coll.FCDIterCollationIterator.State.ITER_IN_FCD_SEGMENT
            r7.state = r1
            return r2
        Lb4:
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.FCDIterCollationIterator.previousSegment():boolean");
    }

    private void normalize(CharSequence s) {
        if (this.normalized == null) {
            this.normalized = new StringBuilder();
        }
        this.nfcImpl.decompose(s, this.normalized);
    }
}
