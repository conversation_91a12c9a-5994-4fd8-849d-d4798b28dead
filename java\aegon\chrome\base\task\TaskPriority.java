package aegon.chrome.base.task;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@Retention(RetentionPolicy.SOURCE)
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public @interface TaskPriority {
    public static final int BEST_EFFORT = 0;
    public static final int HIGHEST = 2;
    public static final int LOWEST = 0;
    public static final int USER_BLOCKING = 2;
    public static final int USER_VISIBLE = 1;
}
