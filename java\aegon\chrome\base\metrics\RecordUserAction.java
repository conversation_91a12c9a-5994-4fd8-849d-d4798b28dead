package aegon.chrome.base.metrics;

import aegon.chrome.base.annotations.JNINamespace;
import android.os.SystemClock;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class RecordUserAction {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static long sNativeActionCallback;

    interface Natives {
        long addActionCallbackForTesting(UserActionCallback userActionCallback);

        void removeActionCallbackForTesting(long j);
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    public interface UserActionCallback {
        void onActionRecorded(String str);
    }

    public static void record(String str) {
        UmaRecorderHolder.get().recordUserAction(str, SystemClock.elapsedRealtime());
    }

    /* renamed from: aegon.chrome.base.metrics.RecordUserAction$1 */
    /* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
    class RunnableC00431 implements Runnable {
        final /* synthetic */ String val$action;

        RunnableC00431(String str) {
            this.val$action = str;
        }

        @Override // java.lang.Runnable
        public void run() {
            RecordUserAction.access$000(this.val$action);
        }
    }

    public static void setActionCallbackForTesting(UserActionCallback userActionCallback) {
        sNativeActionCallback = RecordUserActionJni.get().addActionCallbackForTesting(userActionCallback);
    }

    public static void removeActionCallbackForTesting() {
        RecordUserActionJni.get().removeActionCallbackForTesting(sNativeActionCallback);
        sNativeActionCallback = 0L;
    }
}
