package android.icu.impl.number;

import android.icu.impl.PatternTokenizer;
import android.icu.impl.StandardPlural;
import android.icu.impl.number.Padder;
import android.icu.number.NumberFormatter;
import android.icu.text.DecimalFormatSymbols;
import com.getui.gtc.extension.distribution.gbd.p150d.C1928e;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import org.apache.xalan.templates.Constants;
import org.apache.xpath.XPath;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class PatternStringUtils {
    static final /* synthetic */ boolean $assertionsDisabled = false;

    public static boolean ignoreRoundingIncrement(BigDecimal roundIncrDec, int maxFrac) {
        double roundIncr = roundIncrDec.doubleValue();
        if (roundIncr == XPath.MATCH_SCORE_QNAME) {
            return true;
        }
        if (maxFrac < 0) {
            return false;
        }
        int frac = 0;
        for (double roundIncr2 = roundIncr * 2.0d; frac <= maxFrac && roundIncr2 <= 1.0d; roundIncr2 *= 10.0d) {
            frac++;
        }
        return frac > maxFrac;
    }

    /* JADX WARN: Removed duplicated region for block: B:110:0x01a8 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:57:0x0192  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static java.lang.String propertiesToPatternString(android.icu.impl.number.DecimalFormatProperties r32) {
        /*
            Method dump skipped, instructions count: 639
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.number.PatternStringUtils.propertiesToPatternString(android.icu.impl.number.DecimalFormatProperties):java.lang.String");
    }

    /* renamed from: android.icu.impl.number.PatternStringUtils$1 */
    static /* synthetic */ class C02741 {
        static final /* synthetic */ int[] $SwitchMap$android$icu$impl$number$Padder$PadPosition;

        static {
            int[] iArr = new int[Padder.PadPosition.values().length];
            $SwitchMap$android$icu$impl$number$Padder$PadPosition = iArr;
            try {
                iArr[Padder.PadPosition.BEFORE_PREFIX.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$android$icu$impl$number$Padder$PadPosition[Padder.PadPosition.AFTER_PREFIX.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$android$icu$impl$number$Padder$PadPosition[Padder.PadPosition.BEFORE_SUFFIX.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$android$icu$impl$number$Padder$PadPosition[Padder.PadPosition.AFTER_SUFFIX.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    private static int escapePaddingString(CharSequence input, StringBuilder output, int startIndex) {
        if (input == null || input.length() == 0) {
            input = " ";
        }
        int startLength = output.length();
        if (input.length() == 1) {
            if (input.equals("'")) {
                output.insert(startIndex, "''");
            } else {
                output.insert(startIndex, input);
            }
        } else {
            output.insert(startIndex, PatternTokenizer.SINGLE_QUOTE);
            int offset = 1;
            for (int i = 0; i < input.length(); i++) {
                char ch = input.charAt(i);
                if (ch == '\'') {
                    output.insert(startIndex + offset, "''");
                    offset += 2;
                } else {
                    output.insert(startIndex + offset, ch);
                    offset++;
                }
            }
            output.insert(startIndex + offset, PatternTokenizer.SINGLE_QUOTE);
        }
        return output.length() - startLength;
    }

    public static String convertLocalized(String str, DecimalFormatSymbols decimalFormatSymbols, boolean z) {
        char c2;
        if (str == null) {
            return null;
        }
        int i = 2;
        String[][] strArr = (String[][]) Array.newInstance((Class<?>) String.class, 21, 2);
        int i2 = !z ? 1 : 0;
        strArr[0][i2] = "%";
        strArr[0][z ? 1 : 0] = decimalFormatSymbols.getPercentString();
        int i3 = 1;
        strArr[1][i2] = "‰";
        strArr[1][z ? 1 : 0] = decimalFormatSymbols.getPerMillString();
        strArr[2][i2] = Constants.ATTRVAL_THIS;
        strArr[2][z ? 1 : 0] = decimalFormatSymbols.getDecimalSeparatorString();
        strArr[3][i2] = C1928e.f5807a;
        strArr[3][z ? 1 : 0] = decimalFormatSymbols.getGroupingSeparatorString();
        strArr[4][i2] = "-";
        strArr[4][z ? 1 : 0] = decimalFormatSymbols.getMinusSignString();
        strArr[5][i2] = "+";
        strArr[5][z ? 1 : 0] = decimalFormatSymbols.getPlusSignString();
        strArr[6][i2] = ";";
        strArr[6][z ? 1 : 0] = Character.toString(decimalFormatSymbols.getPatternSeparator());
        strArr[7][i2] = "@";
        strArr[7][z ? 1 : 0] = Character.toString(decimalFormatSymbols.getSignificantDigit());
        strArr[8][i2] = "E";
        strArr[8][z ? 1 : 0] = decimalFormatSymbols.getExponentSeparator();
        strArr[9][i2] = "*";
        strArr[9][z ? 1 : 0] = Character.toString(decimalFormatSymbols.getPadEscape());
        strArr[10][i2] = "#";
        strArr[10][z ? 1 : 0] = Character.toString(decimalFormatSymbols.getDigit());
        for (int i4 = 0; i4 < 10; i4++) {
            strArr[i4 + 11][i2] = Character.toString((char) (i4 + 48));
            strArr[i4 + 11][z ? 1 : 0] = decimalFormatSymbols.getDigitStringsLocal()[i4];
        }
        int i5 = 0;
        while (true) {
            int length = strArr.length;
            c2 = PatternTokenizer.SINGLE_QUOTE;
            if (i5 >= length) {
                break;
            }
            strArr[i5][z ? 1 : 0] = strArr[i5][z ? 1 : 0].replace(PatternTokenizer.SINGLE_QUOTE, (char) 8217);
            i5++;
        }
        StringBuilder sb = new StringBuilder();
        int i6 = 0;
        int length2 = 0;
        while (length2 < str.length()) {
            char cCharAt = str.charAt(length2);
            if (cCharAt == c2) {
                if (i6 == 0) {
                    sb.append(c2);
                    i6 = 1;
                } else if (i6 == i3) {
                    sb.append(c2);
                    i6 = 0;
                } else if (i6 == i) {
                    i6 = 3;
                } else if (i6 == 3) {
                    sb.append(c2);
                    sb.append(c2);
                    i6 = 1;
                } else if (i6 == 4) {
                    i6 = 5;
                } else {
                    sb.append(c2);
                    sb.append(c2);
                    i6 = 4;
                }
            } else if (i6 == 0 || i6 == 3 || i6 == 4) {
                int length3 = strArr.length;
                int i7 = 0;
                while (true) {
                    if (i7 < length3) {
                        String[] strArr2 = strArr[i7];
                        if (!str.regionMatches(length2, strArr2[0], 0, strArr2[0].length())) {
                            i7++;
                        } else {
                            length2 += strArr2[0].length() - i3;
                            if (i6 == 3 || i6 == 4) {
                                sb.append(PatternTokenizer.SINGLE_QUOTE);
                                i6 = 0;
                            }
                            sb.append(strArr2[i3]);
                        }
                    } else {
                        int length4 = strArr.length;
                        int i8 = 0;
                        while (true) {
                            if (i8 < length4) {
                                String[] strArr3 = strArr[i8];
                                if (!str.regionMatches(length2, strArr3[i3], 0, strArr3[i3].length())) {
                                    i8++;
                                    i3 = 1;
                                } else {
                                    if (i6 == 0) {
                                        sb.append(PatternTokenizer.SINGLE_QUOTE);
                                        i6 = 4;
                                    }
                                    sb.append(cCharAt);
                                }
                            } else {
                                if (i6 == 3 || i6 == 4) {
                                    sb.append(PatternTokenizer.SINGLE_QUOTE);
                                    i6 = 0;
                                }
                                sb.append(cCharAt);
                            }
                        }
                    }
                }
            } else {
                sb.append(cCharAt);
                i6 = 2;
            }
            length2++;
            i3 = 1;
            i = 2;
            c2 = PatternTokenizer.SINGLE_QUOTE;
        }
        if (i6 == 3 || i6 == 4) {
            sb.append(PatternTokenizer.SINGLE_QUOTE);
            i6 = 0;
        }
        if (i6 != 0) {
            throw new IllegalArgumentException("Malformed localized pattern: unterminated quote");
        }
        return sb.toString();
    }

    public static void patternInfoToStringBuilder(AffixPatternProvider patternInfo, boolean isPrefix, int signum, NumberFormatter.SignDisplay signDisplay, StandardPlural plural, boolean perMilleReplacesPercent, StringBuilder output) {
        boolean prependSign;
        char candidate;
        boolean plusReplacesMinusSign = signum != -1 && (signDisplay == NumberFormatter.SignDisplay.ALWAYS || signDisplay == NumberFormatter.SignDisplay.ACCOUNTING_ALWAYS || (signum == 1 && (signDisplay == NumberFormatter.SignDisplay.EXCEPT_ZERO || signDisplay == NumberFormatter.SignDisplay.ACCOUNTING_EXCEPT_ZERO))) && !patternInfo.positiveHasPlusSign();
        boolean useNegativeAffixPattern = patternInfo.hasNegativeSubpattern() && (signum == -1 || (patternInfo.negativeHasMinusSign() && plusReplacesMinusSign));
        int flags = 0;
        if (useNegativeAffixPattern) {
            flags = 0 | 512;
        }
        if (isPrefix) {
            flags |= 256;
        }
        if (plural != null) {
            flags |= plural.ordinal();
        }
        if (!isPrefix || useNegativeAffixPattern) {
            prependSign = false;
        } else if (signum == -1) {
            prependSign = signDisplay != NumberFormatter.SignDisplay.NEVER;
        } else {
            prependSign = plusReplacesMinusSign;
        }
        int length = patternInfo.length(flags) + (prependSign ? 1 : 0);
        output.setLength(0);
        for (int index = 0; index < length; index++) {
            if (prependSign && index == 0) {
                candidate = '-';
            } else if (prependSign) {
                candidate = patternInfo.charAt(flags, index - 1);
            } else {
                candidate = patternInfo.charAt(flags, index);
            }
            if (plusReplacesMinusSign && candidate == '-') {
                candidate = '+';
            }
            if (perMilleReplacesPercent && candidate == '%') {
                candidate = 8240;
            }
            output.append(candidate);
        }
    }
}
