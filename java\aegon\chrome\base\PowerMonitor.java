package aegon.chrome.base;

import aegon.chrome.base.annotations.JNINamespace;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.os.Build;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class PowerMonitor {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static PowerMonitor sInstance;
    private boolean mIsBatteryPower;

    interface Natives {
        void onBatteryChargingChanged();
    }

    public static void createForTests() {
        sInstance = new PowerMonitor();
    }

    public static void create() {
        ThreadUtils.assertOnUiThread();
        if (sInstance != null) {
            return;
        }
        Context applicationContext = ContextUtils.getApplicationContext();
        sInstance = new PowerMonitor();
        Intent intentRegisterReceiver = applicationContext.registerReceiver(null, new IntentFilter("android.intent.action.BATTERY_CHANGED"));
        if (intentRegisterReceiver != null) {
            onBatteryChargingChanged(intentRegisterReceiver.getIntExtra("plugged", 0) == 0);
        }
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("android.intent.action.ACTION_POWER_CONNECTED");
        intentFilter.addAction("android.intent.action.ACTION_POWER_DISCONNECTED");
        applicationContext.registerReceiver(new BroadcastReceiver() { // from class: aegon.chrome.base.PowerMonitor.1
            @Override // android.content.BroadcastReceiver
            public void onReceive(Context context, Intent intent) {
                PowerMonitor.onBatteryChargingChanged(intent.getAction().equals("android.intent.action.ACTION_POWER_DISCONNECTED"));
            }
        }, intentFilter);
    }

    private PowerMonitor() {
    }

    private static void onBatteryChargingChanged(boolean z) {
        sInstance.mIsBatteryPower = z;
        PowerMonitorJni.get().onBatteryChargingChanged();
    }

    private static boolean isBatteryPower() {
        if (sInstance == null) {
            create();
        }
        return sInstance.mIsBatteryPower;
    }

    private static int getRemainingBatteryCapacity() {
        if (Build.VERSION.SDK_INT < 21) {
            return 0;
        }
        if (sInstance == null) {
            create();
        }
        return getRemainingBatteryCapacityImpl();
    }

    private static int getRemainingBatteryCapacityImpl() {
        return ((BatteryManager) ContextUtils.getApplicationContext().getSystemService("batterymanager")).getIntProperty(1);
    }
}
