package android.icu.impl.coll;

import android.net.wifi.hotspot2.pps.UpdateParameter;
import java.util.Arrays;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationWeights {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private int middleLength;
    private int rangeCount;
    private int rangeIndex;
    private int[] minBytes = new int[5];
    private int[] maxBytes = new int[5];
    private WeightRange[] ranges = new WeightRange[7];

    public void initForPrimary(boolean compressible) {
        this.middleLength = 1;
        int[] iArr = this.minBytes;
        iArr[1] = 3;
        int[] iArr2 = this.maxBytes;
        iArr2[1] = 255;
        if (compressible) {
            iArr[2] = 4;
            iArr2[2] = 254;
        } else {
            iArr[2] = 2;
            iArr2[2] = 255;
        }
        int[] iArr3 = this.minBytes;
        iArr3[3] = 2;
        int[] iArr4 = this.maxBytes;
        iArr4[3] = 255;
        iArr3[4] = 2;
        iArr4[4] = 255;
    }

    public void initForSecondary() {
        this.middleLength = 3;
        int[] iArr = this.minBytes;
        iArr[1] = 0;
        int[] iArr2 = this.maxBytes;
        iArr2[1] = 0;
        iArr[2] = 0;
        iArr2[2] = 0;
        iArr[3] = 2;
        iArr2[3] = 255;
        iArr[4] = 2;
        iArr2[4] = 255;
    }

    public void initForTertiary() {
        this.middleLength = 3;
        int[] iArr = this.minBytes;
        iArr[1] = 0;
        int[] iArr2 = this.maxBytes;
        iArr2[1] = 0;
        iArr[2] = 0;
        iArr2[2] = 0;
        iArr[3] = 2;
        iArr2[3] = 63;
        iArr[4] = 2;
        iArr2[4] = 63;
    }

    public boolean allocWeights(long lowerLimit, long upperLimit, int n) {
        if (!getWeightRanges(lowerLimit, upperLimit)) {
            return false;
        }
        while (true) {
            int minLength = this.ranges[0].length;
            if (allocWeightsInShortRanges(n, minLength)) {
                break;
            }
            if (minLength == 4) {
                return false;
            }
            if (allocWeightsInMinLengthRanges(n, minLength)) {
                break;
            }
            for (int i = 0; i < this.rangeCount && this.ranges[i].length == minLength; i++) {
                lengthenRange(this.ranges[i]);
            }
        }
        this.rangeIndex = 0;
        int i2 = this.rangeCount;
        WeightRange[] weightRangeArr = this.ranges;
        if (i2 < weightRangeArr.length) {
            weightRangeArr[i2] = null;
            return true;
        }
        return true;
    }

    public long nextWeight() {
        int i = this.rangeIndex;
        if (i >= this.rangeCount) {
            return UpdateParameter.UPDATE_CHECK_INTERVAL_NEVER;
        }
        WeightRange range = this.ranges[i];
        long weight = range.start;
        int i2 = range.count - 1;
        range.count = i2;
        if (i2 == 0) {
            this.rangeIndex++;
        } else {
            range.start = incWeight(weight, range.length);
        }
        return weight;
    }

    private static final class WeightRange implements Comparable<WeightRange> {
        int count;
        long end;
        int length;
        long start;

        private WeightRange() {
        }

        @Override // java.lang.Comparable
        public int compareTo(WeightRange other) {
            long l = this.start;
            long r = other.start;
            if (l < r) {
                return -1;
            }
            if (l > r) {
                return 1;
            }
            return 0;
        }
    }

    public static int lengthOfWeight(long weight) {
        if ((16777215 & weight) == 0) {
            return 1;
        }
        if ((65535 & weight) == 0) {
            return 2;
        }
        if ((255 & weight) == 0) {
            return 3;
        }
        return 4;
    }

    private static int getWeightTrail(long weight, int length) {
        return ((int) (weight >> ((4 - length) * 8))) & 255;
    }

    private static long setWeightTrail(long weight, int length, int trail) {
        int length2 = (4 - length) * 8;
        return ((CollationRootElements.PRIMARY_SENTINEL << length2) & weight) | (trail << length2);
    }

    private static int getWeightByte(long weight, int idx) {
        return getWeightTrail(weight, idx);
    }

    private static long setWeightByte(long weight, int idx, int b2) {
        long mask;
        int idx2 = idx * 8;
        if (idx2 < 32) {
            mask = UpdateParameter.UPDATE_CHECK_INTERVAL_NEVER >> idx2;
        } else {
            mask = 0;
        }
        int idx3 = 32 - idx2;
        return (weight & (mask | (CollationRootElements.PRIMARY_SENTINEL << idx3))) | (b2 << idx3);
    }

    private static long truncateWeight(long weight, int length) {
        return (UpdateParameter.UPDATE_CHECK_INTERVAL_NEVER << ((4 - length) * 8)) & weight;
    }

    private static long incWeightTrail(long weight, int length) {
        return (1 << ((4 - length) * 8)) + weight;
    }

    private static long decWeightTrail(long weight, int length) {
        return weight - (1 << ((4 - length) * 8));
    }

    private int countBytes(int idx) {
        return (this.maxBytes[idx] - this.minBytes[idx]) + 1;
    }

    private long incWeight(long weight, int length) {
        while (true) {
            int b2 = getWeightByte(weight, length);
            if (b2 < this.maxBytes[length]) {
                return setWeightByte(weight, length, b2 + 1);
            }
            weight = setWeightByte(weight, length, this.minBytes[length]);
            length--;
        }
    }

    private long incWeightByOffset(long weight, int length, int offset) {
        while (true) {
            int offset2 = offset + getWeightByte(weight, length);
            if (offset2 <= this.maxBytes[length]) {
                return setWeightByte(weight, length, offset2);
            }
            int[] iArr = this.minBytes;
            int offset3 = offset2 - iArr[length];
            weight = setWeightByte(weight, length, iArr[length] + (offset3 % countBytes(length)));
            offset = offset3 / countBytes(length);
            length--;
        }
    }

    private void lengthenRange(WeightRange range) {
        int length = range.length + 1;
        range.start = setWeightTrail(range.start, length, this.minBytes[length]);
        range.end = setWeightTrail(range.end, length, this.maxBytes[length]);
        range.count *= countBytes(length);
        range.length = length;
    }

    /* JADX WARN: Multi-variable type inference failed */
    private boolean getWeightRanges(long lowerLimit, long upperLimit) {
        int i;
        int i2;
        long weight;
        C02641 c02641;
        int i3;
        WeightRange[] lower;
        int lowerLength = lengthOfWeight(lowerLimit);
        int upperLength = lengthOfWeight(upperLimit);
        if (lowerLimit >= upperLimit) {
            return false;
        }
        if (lowerLength < upperLength && lowerLimit == truncateWeight(upperLimit, lowerLength)) {
            return false;
        }
        WeightRange[] lower2 = new WeightRange[5];
        C02641 c026412 = null;
        WeightRange middle = new WeightRange();
        WeightRange[] upper = new WeightRange[5];
        long weight2 = lowerLimit;
        int length = lowerLength;
        while (true) {
            i = this.middleLength;
            if (length <= i) {
                break;
            }
            int trail = getWeightTrail(weight2, length);
            if (trail >= this.maxBytes[length]) {
                lower = lower2;
            } else {
                lower2[length] = new WeightRange();
                lower = lower2;
                lower2[length].start = incWeightTrail(weight2, length);
                lower[length].end = setWeightTrail(weight2, length, this.maxBytes[length]);
                lower[length].length = length;
                lower[length].count = this.maxBytes[length] - trail;
            }
            weight2 = truncateWeight(weight2, length - 1);
            length--;
            lower2 = lower;
        }
        WeightRange[] lower3 = lower2;
        if (weight2 < 4278190080L) {
            middle.start = incWeightTrail(weight2, i);
        } else {
            middle.start = UpdateParameter.UPDATE_CHECK_INTERVAL_NEVER;
        }
        long weight3 = upperLimit;
        int length2 = upperLength;
        while (true) {
            i2 = this.middleLength;
            if (length2 <= i2) {
                break;
            }
            int trail2 = getWeightTrail(weight3, length2);
            if (trail2 > this.minBytes[length2]) {
                upper[length2] = new WeightRange();
                upper[length2].start = setWeightTrail(weight3, length2, this.minBytes[length2]);
                upper[length2].end = decWeightTrail(weight3, length2);
                upper[length2].length = length2;
                upper[length2].count = trail2 - this.minBytes[length2];
            }
            weight3 = truncateWeight(weight3, length2 - 1);
            length2--;
        }
        middle.end = decWeightTrail(weight3, i2);
        middle.length = this.middleLength;
        if (middle.end >= middle.start) {
            middle.count = ((int) ((middle.end - middle.start) >> ((4 - this.middleLength) * 8))) + 1;
        } else {
            int length3 = 4;
            while (true) {
                if (length3 <= this.middleLength) {
                    break;
                }
                if (lower3[length3] == null || upper[length3] == null || lower3[length3].count <= 0 || upper[length3].count <= 0) {
                    weight = weight3;
                    c02641 = c026412;
                } else {
                    long lowerEnd = lower3[length3].end;
                    long upperStart = upper[length3].start;
                    boolean merged = false;
                    if (lowerEnd > upperStart) {
                        lower3[length3].end = upper[length3].end;
                        WeightRange weightRange = lower3[length3];
                        int weightTrail = getWeightTrail(lower3[length3].end, length3);
                        weight = weight3;
                        long weight4 = lower3[length3].start;
                        weightRange.count = (weightTrail - getWeightTrail(weight4, length3)) + 1;
                        merged = true;
                    } else {
                        weight = weight3;
                        if (lowerEnd != upperStart && incWeight(lowerEnd, length3) == upperStart) {
                            lower3[length3].end = upper[length3].end;
                            lower3[length3].count += upper[length3].count;
                            merged = true;
                        }
                    }
                    if (!merged) {
                        c02641 = null;
                    } else {
                        upper[length3].count = 0;
                        while (true) {
                            length3--;
                            if (length3 <= this.middleLength) {
                                break;
                            }
                            upper[length3] = null;
                            lower3[length3] = null;
                        }
                    }
                }
                length3--;
                c026412 = c02641;
                weight3 = weight;
            }
        }
        this.rangeCount = 0;
        if (middle.count > 0) {
            this.ranges[0] = middle;
            i3 = 1;
            this.rangeCount = 1;
        } else {
            i3 = 1;
        }
        for (int length4 = this.middleLength + i3; length4 <= 4; length4++) {
            if (upper[length4] != null && upper[length4].count > 0) {
                WeightRange[] weightRangeArr = this.ranges;
                int i4 = this.rangeCount;
                this.rangeCount = i4 + 1;
                weightRangeArr[i4] = upper[length4];
            }
            if (lower3[length4] != null && lower3[length4].count > 0) {
                WeightRange[] weightRangeArr2 = this.ranges;
                int i5 = this.rangeCount;
                this.rangeCount = i5 + 1;
                weightRangeArr2[i5] = lower3[length4];
            }
        }
        int length5 = this.rangeCount;
        if (length5 > 0) {
            return i3;
        }
        return false;
    }

    private boolean allocWeightsInShortRanges(int n, int minLength) {
        for (int i = 0; i < this.rangeCount && this.ranges[i].length <= minLength + 1; i++) {
            if (n <= this.ranges[i].count) {
                if (this.ranges[i].length > minLength) {
                    this.ranges[i].count = n;
                }
                int i2 = i + 1;
                this.rangeCount = i2;
                if (i2 > 1) {
                    Arrays.sort(this.ranges, 0, i2);
                }
                return true;
            }
            n -= this.ranges[i].count;
        }
        return false;
    }

    private boolean allocWeightsInMinLengthRanges(int n, int minLength) {
        int count = 0;
        int minLengthRangeCount = 0;
        while (minLengthRangeCount < this.rangeCount && this.ranges[minLengthRangeCount].length == minLength) {
            count += this.ranges[minLengthRangeCount].count;
            minLengthRangeCount++;
        }
        int nextCountBytes = countBytes(minLength + 1);
        if (n > count * nextCountBytes) {
            return false;
        }
        long start = this.ranges[0].start;
        long end = this.ranges[0].end;
        for (int i = 1; i < minLengthRangeCount; i++) {
            if (this.ranges[i].start < start) {
                start = this.ranges[i].start;
            }
            if (this.ranges[i].end > end) {
                end = this.ranges[i].end;
            }
        }
        int i2 = n - count;
        int count2 = i2 / (nextCountBytes - 1);
        int count1 = count - count2;
        if (count2 == 0 || (count2 * nextCountBytes) + count1 < n) {
            count2++;
            count1--;
        }
        this.ranges[0].start = start;
        if (count1 != 0) {
            this.ranges[0].end = incWeightByOffset(start, minLength, count1 - 1);
            this.ranges[0].count = count1;
            WeightRange[] weightRangeArr = this.ranges;
            if (weightRangeArr[1] == null) {
                weightRangeArr[1] = new WeightRange();
            }
            WeightRange[] weightRangeArr2 = this.ranges;
            weightRangeArr2[1].start = incWeight(weightRangeArr2[0].end, minLength);
            this.ranges[1].end = end;
            this.ranges[1].length = minLength;
            this.ranges[1].count = count2;
            lengthenRange(this.ranges[1]);
            this.rangeCount = 2;
        } else {
            this.ranges[0].end = end;
            this.ranges[0].count = count;
            lengthenRange(this.ranges[0]);
            this.rangeCount = 1;
        }
        return true;
    }
}
