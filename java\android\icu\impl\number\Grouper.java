package android.icu.impl.number;

import android.icu.impl.ICUData;
import android.icu.impl.ICUResourceBundle;
import android.icu.impl.number.PatternStringParser;
import android.icu.number.NumberFormatter;
import android.icu.util.ULocale;
import android.icu.util.UResourceBundle;
import java.util.MissingResourceException;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class Grouper {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private final short grouping1;
    private final short grouping2;
    private final short minGrouping;
    private static final Grouper GROUPER_NEVER = new Grouper(-1, -1, -2);
    private static final Grouper GROUPER_MIN2 = new Grouper(-2, -2, -3);
    private static final Grouper GROUPER_AUTO = new Grouper(-2, -2, -2);
    private static final Grouper GROUPER_ON_ALIGNED = new Grouper(-4, -4, 1);
    private static final Grouper GROUPER_WESTERN = new Grouper(3, 3, 1);
    private static final Grouper GROUPER_INDIC = new Grouper(3, 2, 1);
    private static final Grouper GROUPER_WESTERN_MIN2 = new Grouper(3, 3, 2);
    private static final Grouper GROUPER_INDIC_MIN2 = new Grouper(3, 2, 2);

    /* renamed from: android.icu.impl.number.Grouper$1 */
    static /* synthetic */ class C02711 {
        static final /* synthetic */ int[] $SwitchMap$android$icu$number$NumberFormatter$GroupingStrategy;

        static {
            int[] iArr = new int[NumberFormatter.GroupingStrategy.values().length];
            $SwitchMap$android$icu$number$NumberFormatter$GroupingStrategy = iArr;
            try {
                iArr[NumberFormatter.GroupingStrategy.OFF.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$android$icu$number$NumberFormatter$GroupingStrategy[NumberFormatter.GroupingStrategy.MIN2.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$android$icu$number$NumberFormatter$GroupingStrategy[NumberFormatter.GroupingStrategy.AUTO.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$android$icu$number$NumberFormatter$GroupingStrategy[NumberFormatter.GroupingStrategy.ON_ALIGNED.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                $SwitchMap$android$icu$number$NumberFormatter$GroupingStrategy[NumberFormatter.GroupingStrategy.THOUSANDS.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    public static Grouper forStrategy(NumberFormatter.GroupingStrategy grouping) {
        int i = C02711.$SwitchMap$android$icu$number$NumberFormatter$GroupingStrategy[grouping.ordinal()];
        if (i == 1) {
            return GROUPER_NEVER;
        }
        if (i == 2) {
            return GROUPER_MIN2;
        }
        if (i == 3) {
            return GROUPER_AUTO;
        }
        if (i == 4) {
            return GROUPER_ON_ALIGNED;
        }
        if (i == 5) {
            return GROUPER_WESTERN;
        }
        throw new AssertionError();
    }

    public static Grouper forProperties(DecimalFormatProperties properties) {
        if (!properties.getGroupingUsed()) {
            return GROUPER_NEVER;
        }
        short grouping1 = (short) properties.getGroupingSize();
        short grouping2 = (short) properties.getSecondaryGroupingSize();
        short minGrouping = (short) properties.getMinimumGroupingDigits();
        short grouping12 = (grouping1 <= 0 && grouping2 > 0) ? grouping2 : grouping1;
        return getInstance(grouping12, grouping2 > 0 ? grouping2 : grouping12, minGrouping);
    }

    public static Grouper getInstance(short grouping1, short grouping2, short minGrouping) {
        if (grouping1 == -1) {
            return GROUPER_NEVER;
        }
        if (grouping1 == 3 && grouping2 == 3 && minGrouping == 1) {
            return GROUPER_WESTERN;
        }
        if (grouping1 == 3 && grouping2 == 2 && minGrouping == 1) {
            return GROUPER_INDIC;
        }
        if (grouping1 == 3 && grouping2 == 3 && minGrouping == 2) {
            return GROUPER_WESTERN_MIN2;
        }
        if (grouping1 == 3 && grouping2 == 2 && minGrouping == 2) {
            return GROUPER_INDIC_MIN2;
        }
        return new Grouper(grouping1, grouping2, minGrouping);
    }

    private static short getMinGroupingForLocale(ULocale locale) throws MissingResourceException {
        ICUResourceBundle resource = (ICUResourceBundle) UResourceBundle.getBundleInstance(ICUData.ICU_BASE_NAME, locale);
        String result = resource.getStringWithFallback("NumberElements/minimumGroupingDigits");
        return Short.valueOf(result).shortValue();
    }

    private Grouper(short grouping1, short grouping2, short minGrouping) {
        this.grouping1 = grouping1;
        this.grouping2 = grouping2;
        this.minGrouping = minGrouping;
    }

    public Grouper withLocaleData(ULocale locale, PatternStringParser.ParsedPatternInfo patternInfo) throws MissingResourceException {
        short minGrouping;
        short s = this.grouping1;
        if (s != -2 && s != -4) {
            return this;
        }
        short grouping1 = (short) (patternInfo.positive.groupingSizes & 65535);
        short grouping2 = (short) ((patternInfo.positive.groupingSizes >>> 16) & 65535);
        short grouping3 = (short) ((patternInfo.positive.groupingSizes >>> 32) & 65535);
        if (grouping2 == -1) {
            grouping1 = this.grouping1 == -4 ? (short) 3 : (short) -1;
        }
        if (grouping3 == -1) {
            grouping2 = grouping1;
        }
        short s2 = this.minGrouping;
        if (s2 == -2) {
            minGrouping = getMinGroupingForLocale(locale);
        } else if (s2 == -3) {
            minGrouping = (short) Math.max(2, (int) getMinGroupingForLocale(locale));
        } else {
            minGrouping = this.minGrouping;
        }
        return getInstance(grouping1, grouping2, minGrouping);
    }

    public boolean groupAtPosition(int position, DecimalQuantity value) {
        int position2;
        short s = this.grouping1;
        return s != -1 && s != 0 && (position2 = position - s) >= 0 && position2 % this.grouping2 == 0 && (value.getUpperDisplayMagnitude() - this.grouping1) + 1 >= this.minGrouping;
    }

    public short getPrimary() {
        return this.grouping1;
    }

    public short getSecondary() {
        return this.grouping2;
    }
}
