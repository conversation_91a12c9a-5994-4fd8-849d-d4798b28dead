package android.icu.impl.number.parse;

import android.icu.impl.StringSegment;
import android.icu.impl.number.AffixPatternProvider;
import android.icu.impl.number.CurrencyPluralInfoAffixProvider;
import android.icu.impl.number.CustomSymbolCurrency;
import android.icu.impl.number.DecimalFormatProperties;
import android.icu.impl.number.Grouper;
import android.icu.impl.number.PatternStringParser;
import android.icu.impl.number.PropertiesAffixPatternProvider;
import android.icu.impl.number.RoundingUtils;
import android.icu.number.NumberFormatter;
import android.icu.number.Scale;
import android.icu.text.DecimalFormatSymbols;
import android.icu.util.Currency;
import android.icu.util.CurrencyAmount;
import android.icu.util.ULocale;
import java.text.ParsePosition;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.MissingResourceException;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class NumberParserImpl {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private final int parseFlags;
    private final List<NumberParseMatcher> matchers = new ArrayList();
    private boolean frozen = false;

    public static NumberParserImpl createSimpleParser(ULocale locale, String pattern, int parseFlags) throws MissingResourceException {
        NumberParserImpl parser = new NumberParserImpl(parseFlags);
        Currency currency = Currency.getInstance("USD");
        DecimalFormatSymbols symbols = DecimalFormatSymbols.getInstance(locale);
        IgnorablesMatcher ignorables = IgnorablesMatcher.getInstance(parseFlags);
        AffixTokenMatcherFactory factory = new AffixTokenMatcherFactory();
        factory.currency = currency;
        factory.symbols = symbols;
        factory.ignorables = ignorables;
        factory.locale = locale;
        factory.parseFlags = parseFlags;
        PatternStringParser.ParsedPatternInfo patternInfo = PatternStringParser.parseToPatternInfo(pattern);
        AffixMatcher.createMatchers(patternInfo, parser, factory, ignorables, parseFlags);
        Grouper grouper = Grouper.forStrategy(NumberFormatter.GroupingStrategy.AUTO).withLocaleData(locale, patternInfo);
        parser.addMatcher(ignorables);
        parser.addMatcher(DecimalMatcher.getInstance(symbols, grouper, parseFlags));
        parser.addMatcher(MinusSignMatcher.getInstance(symbols, false));
        parser.addMatcher(PlusSignMatcher.getInstance(symbols, false));
        parser.addMatcher(PercentMatcher.getInstance(symbols));
        parser.addMatcher(PermilleMatcher.getInstance(symbols));
        parser.addMatcher(NanMatcher.getInstance(symbols, parseFlags));
        parser.addMatcher(InfinityMatcher.getInstance(symbols));
        parser.addMatcher(PaddingMatcher.getInstance("@"));
        parser.addMatcher(ScientificMatcher.getInstance(symbols, grouper));
        parser.addMatcher(CombinedCurrencyMatcher.getInstance(currency, symbols, parseFlags));
        parser.addMatcher(new RequireNumberValidator());
        parser.freeze();
        return parser;
    }

    public static Number parseStatic(String input, ParsePosition ppos, DecimalFormatProperties properties, DecimalFormatSymbols symbols) {
        NumberParserImpl parser = createParserFromProperties(properties, symbols, false);
        ParsedNumber result = new ParsedNumber();
        parser.parse(input, true, result);
        if (result.success()) {
            ppos.setIndex(result.charEnd);
            return result.getNumber();
        }
        ppos.setErrorIndex(result.charEnd);
        return null;
    }

    public static CurrencyAmount parseStaticCurrency(String input, ParsePosition ppos, DecimalFormatProperties properties, DecimalFormatSymbols symbols) {
        NumberParserImpl parser = createParserFromProperties(properties, symbols, true);
        ParsedNumber result = new ParsedNumber();
        parser.parse(input, true, result);
        if (result.success()) {
            ppos.setIndex(result.charEnd);
            return new CurrencyAmount(result.getNumber(), Currency.getInstance(result.currencyCode));
        }
        ppos.setErrorIndex(result.charEnd);
        return null;
    }

    public static NumberParserImpl createDefaultParserForLocale(ULocale loc) {
        DecimalFormatSymbols symbols = DecimalFormatSymbols.getInstance(loc);
        DecimalFormatProperties properties = PatternStringParser.parseToProperties("0");
        return createParserFromProperties(properties, symbols, false);
    }

    public static NumberParserImpl createParserFromProperties(DecimalFormatProperties properties, DecimalFormatSymbols symbols, boolean parseCurrency) {
        AffixPatternProvider affixProvider;
        int parseFlags;
        ULocale locale = symbols.getULocale();
        if (properties.getCurrencyPluralInfo() == null) {
            affixProvider = new PropertiesAffixPatternProvider(properties);
        } else {
            affixProvider = new CurrencyPluralInfoAffixProvider(properties.getCurrencyPluralInfo(), properties);
        }
        Currency currency = CustomSymbolCurrency.resolve(properties.getCurrency(), locale, symbols);
        DecimalFormatProperties.ParseMode parseMode = properties.getParseMode();
        if (parseMode == null) {
            parseMode = DecimalFormatProperties.ParseMode.LENIENT;
        }
        Grouper grouper = Grouper.forProperties(properties);
        int parseFlags2 = 0;
        if (!properties.getParseCaseSensitive()) {
            parseFlags2 = 0 | 1;
        }
        if (properties.getParseIntegerOnly()) {
            parseFlags2 |= 16;
        }
        if (properties.getParseToBigDecimal()) {
            parseFlags2 |= 4096;
        }
        if (properties.getSignAlwaysShown()) {
            parseFlags2 |= 1024;
        }
        if (parseMode == DecimalFormatProperties.ParseMode.JAVA_COMPATIBILITY) {
            parseFlags = parseFlags2 | 4 | 256 | 512 | 65536;
        } else if (parseMode == DecimalFormatProperties.ParseMode.STRICT) {
            parseFlags = parseFlags2 | 8 | 4 | 256 | 512 | 32768;
        } else {
            parseFlags = parseFlags2 | 128;
        }
        if (grouper.getPrimary() <= 0) {
            parseFlags |= 32;
        }
        if (parseCurrency || affixProvider.hasCurrencySign()) {
            parseFlags |= 2;
        }
        if (!parseCurrency) {
            parseFlags |= 8192;
        }
        NumberParserImpl parser = new NumberParserImpl(parseFlags);
        IgnorablesMatcher ignorables = IgnorablesMatcher.getInstance(parseFlags);
        AffixTokenMatcherFactory factory = new AffixTokenMatcherFactory();
        factory.currency = currency;
        factory.symbols = symbols;
        factory.ignorables = ignorables;
        factory.locale = locale;
        factory.parseFlags = parseFlags;
        AffixMatcher.createMatchers(affixProvider, parser, factory, ignorables, parseFlags);
        if (parseCurrency || affixProvider.hasCurrencySign()) {
            parser.addMatcher(CombinedCurrencyMatcher.getInstance(currency, symbols, parseFlags));
        }
        if (parseMode == DecimalFormatProperties.ParseMode.LENIENT && affixProvider.containsSymbolType(-3)) {
            parser.addMatcher(PercentMatcher.getInstance(symbols));
        }
        if (parseMode == DecimalFormatProperties.ParseMode.LENIENT && affixProvider.containsSymbolType(-4)) {
            parser.addMatcher(PermilleMatcher.getInstance(symbols));
        }
        if (parseMode == DecimalFormatProperties.ParseMode.LENIENT) {
            parser.addMatcher(PlusSignMatcher.getInstance(symbols, false));
            parser.addMatcher(MinusSignMatcher.getInstance(symbols, false));
        }
        parser.addMatcher(NanMatcher.getInstance(symbols, parseFlags));
        parser.addMatcher(InfinityMatcher.getInstance(symbols));
        String padString = properties.getPadString();
        if (padString != null && !ignorables.getSet().contains(padString)) {
            parser.addMatcher(PaddingMatcher.getInstance(padString));
        }
        parser.addMatcher(ignorables);
        parser.addMatcher(DecimalMatcher.getInstance(symbols, grouper, parseFlags));
        if (!properties.getParseNoExponent() || properties.getMinimumExponentDigits() > 0) {
            parser.addMatcher(ScientificMatcher.getInstance(symbols, grouper));
        }
        parser.addMatcher(new RequireNumberValidator());
        if (parseMode != DecimalFormatProperties.ParseMode.LENIENT) {
            parser.addMatcher(new RequireAffixValidator());
        }
        if (parseCurrency) {
            parser.addMatcher(new RequireCurrencyValidator());
        }
        if (properties.getDecimalPatternMatchRequired()) {
            boolean patternHasDecimalSeparator = properties.getDecimalSeparatorAlwaysShown() || properties.getMaximumFractionDigits() != 0;
            parser.addMatcher(RequireDecimalSeparatorValidator.getInstance(patternHasDecimalSeparator));
        }
        Scale multiplier = RoundingUtils.scaleFromProperties(properties);
        if (multiplier != null) {
            parser.addMatcher(new MultiplierParseHandler(multiplier));
        }
        parser.freeze();
        return parser;
    }

    public NumberParserImpl(int parseFlags) {
        this.parseFlags = parseFlags;
    }

    public void addMatcher(NumberParseMatcher matcher) {
        this.matchers.add(matcher);
    }

    public void addMatchers(Collection<? extends NumberParseMatcher> matchers) {
        this.matchers.addAll(matchers);
    }

    public void freeze() {
        this.frozen = true;
    }

    public int getParseFlags() {
        return this.parseFlags;
    }

    public void parse(String input, boolean greedy, ParsedNumber result) {
        parse(input, 0, greedy, result);
    }

    public void parse(String input, int start, boolean greedy, ParsedNumber result) {
        StringSegment segment = new StringSegment(input, (this.parseFlags & 1) != 0);
        segment.adjustOffset(start);
        if (greedy) {
            parseGreedy(segment, result);
        } else if ((this.parseFlags & 16384) != 0) {
            parseLongestRecursive(segment, result, 1);
        } else {
            parseLongestRecursive(segment, result, -100);
        }
        for (NumberParseMatcher matcher : this.matchers) {
            matcher.postProcess(result);
        }
        result.postProcess();
    }

    private void parseGreedy(StringSegment segment, ParsedNumber result) {
        int i = 0;
        while (i < this.matchers.size() && segment.length() != 0) {
            NumberParseMatcher matcher = this.matchers.get(i);
            if (!matcher.smokeTest(segment)) {
                i++;
            } else {
                int initialOffset = segment.getOffset();
                matcher.match(segment, result);
                if (segment.getOffset() != initialOffset) {
                    i = 0;
                } else {
                    i++;
                }
            }
        }
    }

    private void parseLongestRecursive(StringSegment segment, ParsedNumber result, int recursionLevels) {
        if (segment.length() == 0 || recursionLevels == 0) {
            return;
        }
        ParsedNumber initial = new ParsedNumber();
        initial.copyFrom(result);
        ParsedNumber candidate = new ParsedNumber();
        int initialOffset = segment.getOffset();
        for (int i = 0; i < this.matchers.size(); i++) {
            NumberParseMatcher matcher = this.matchers.get(i);
            if (matcher.smokeTest(segment)) {
                int charsToConsume = 0;
                while (charsToConsume < segment.length()) {
                    charsToConsume += Character.charCount(segment.codePointAt(charsToConsume));
                    candidate.copyFrom(initial);
                    segment.setLength(charsToConsume);
                    boolean maybeMore = matcher.match(segment, candidate);
                    segment.resetLength();
                    if (segment.getOffset() - initialOffset == charsToConsume) {
                        parseLongestRecursive(segment, candidate, recursionLevels + 1);
                        if (candidate.isBetterThan(result)) {
                            result.copyFrom(candidate);
                        }
                    }
                    segment.setOffset(initialOffset);
                    if (!maybeMore) {
                        break;
                    }
                }
            }
        }
    }

    public String toString() {
        return "<NumberParserImpl matchers=" + this.matchers.toString() + ">";
    }
}
