package aegon.chrome.base.compat;

import aegon.chrome.base.StrictModeContext;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.app.Notification;
import android.content.ClipDescription;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.ConnectivityManager;
import android.net.NetworkRequest;
import android.os.Handler;
import android.view.Display;
import android.view.View;
import android.view.Window;
import android.view.autofill.AutofillManager;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class ApiHelperForO {
    private ApiHelperForO() {
    }

    public static boolean isWideColorGamut(Display display) {
        return display.isWideColorGamut();
    }

    public static void setColorMode(Window window, int i) {
        window.setColorMode(i);
    }

    public static boolean isScreenWideColorGamut(Configuration configuration) {
        return configuration.isScreenWideColorGamut();
    }

    public static boolean isInstantApp(PackageManager packageManager) {
        return packageManager.isInstantApp();
    }

    public static void setDefaultFocusHighlightEnabled(View view, boolean z) {
        view.setDefaultFocusHighlightEnabled(z);
    }

    public static long getTimestamp(ClipDescription clipDescription) {
        return clipDescription.getTimestamp();
    }

    public static String[] getSplitNames(ApplicationInfo applicationInfo) {
        return applicationInfo.splitNames;
    }

    public static Context createContextForSplit(Context context, String str) {
        StrictModeContext strictModeContextAllowDiskReads = StrictModeContext.allowDiskReads();
        try {
            Context contextCreateContextForSplit = context.createContextForSplit(str);
            if (strictModeContextAllowDiskReads != null) {
                strictModeContextAllowDiskReads.close();
            }
            return contextCreateContextForSplit;
        } catch (Throwable th) {
            if (strictModeContextAllowDiskReads != null) {
                try {
                    strictModeContextAllowDiskReads.close();
                } catch (Throwable unused) {
                }
            }
            throw th;
        }
    }

    public static void cancelAutofillSession(Activity activity) {
        AutofillManager autofillManager = (AutofillManager) activity.getSystemService(AutofillManager.class);
        if (autofillManager != null) {
            autofillManager.cancel();
        }
    }

    public static void notifyValueChangedForAutofill(View view) {
        AutofillManager autofillManager = (AutofillManager) view.getContext().getSystemService(AutofillManager.class);
        if (autofillManager != null) {
            autofillManager.notifyValueChanged(view);
        }
    }

    public static void registerNetworkCallback(ConnectivityManager connectivityManager, NetworkRequest networkRequest, ConnectivityManager.NetworkCallback networkCallback, Handler handler) {
        connectivityManager.registerNetworkCallback(networkRequest, networkCallback, handler);
    }

    public static boolean areAnimatorsEnabled() {
        return ValueAnimator.areAnimatorsEnabled();
    }

    public static Notification.Builder setChannelId(Notification.Builder builder, String str) {
        return builder.setChannelId(str);
    }

    public static Notification.Builder setTimeoutAfter(Notification.Builder builder, long j) {
        return builder.setTimeoutAfter(j);
    }

    public static void registerDefaultNetworkCallback(ConnectivityManager connectivityManager, ConnectivityManager.NetworkCallback networkCallback, Handler handler) {
        connectivityManager.registerDefaultNetworkCallback(networkCallback, handler);
    }

    public static String getNotificationChannelId(Notification notification) {
        return notification.getChannelId();
    }
}
