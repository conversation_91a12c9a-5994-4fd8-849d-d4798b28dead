package aegon.chrome.base;

import aegon.chrome.base.annotations.JNINamespace;
import aegon.chrome.base.annotations.MainDex;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@MainDex
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class TimeUtils {
    public static final int NANOSECONDS_PER_MILLISECOND = 1000000;
    public static final int SECONDS_PER_DAY = 86400;
    public static final int SECONDS_PER_HOUR = 3600;
    public static final int SECONDS_PER_MINUTE = 60;

    public interface Natives {
        long getTimeTicksNowUs();
    }

    private TimeUtils() {
    }
}
