package aegon.chrome.base;

import aegon.chrome.base.TimeUtils;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class TimeUtilsJni implements TimeUtils.Natives {
    public static final JniStaticTestMocker<TimeUtils.Natives> TEST_HOOKS = new JniStaticTestMocker<TimeUtils.Natives>() { // from class: aegon.chrome.base.TimeUtilsJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(TimeUtils.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                TimeUtils.Natives unused = TimeUtilsJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static TimeUtils.Natives testInstance;

    @Override // aegon.chrome.base.TimeUtils.Natives
    public long getTimeTicksNowUs() {
        return GEN_JNI.org_chromium_base_TimeUtils_getTimeTicksNowUs();
    }

    public static TimeUtils.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            TimeUtils.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.TimeUtils.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new TimeUtilsJni();
    }
}
