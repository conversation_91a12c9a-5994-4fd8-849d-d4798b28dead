package aegon.chrome.base.task;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class AsyncTask$$Lambda$1 implements Runnable {
    private final AsyncTask arg$1;
    private final Object arg$2;

    private AsyncTask$$Lambda$1(AsyncTask asyncTask, Object obj) {
        this.arg$1 = asyncTask;
        this.arg$2 = obj;
    }

    public static Runnable lambdaFactory$(AsyncTask asyncTask, Object obj) {
        return new AsyncTask$$Lambda$1(asyncTask, obj);
    }

    @Override // java.lang.Runnable
    public final void run() {
        AsyncTask.lambda$postResult$1(this.arg$1, this.arg$2);
    }
}
