package android.icu.impl.coll;

import android.icu.impl.ICUBinary;
import java.nio.ByteBuffer;
import java.p654io.IOException;
import java.util.MissingResourceException;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationRoot {
    private static final RuntimeException exception;
    private static final CollationTailoring rootSingleton;

    public static final CollationTailoring getRoot() {
        RuntimeException runtimeException = exception;
        if (runtimeException != null) {
            throw runtimeException;
        }
        return rootSingleton;
    }

    public static final CollationData getData() {
        CollationTailoring root = getRoot();
        return root.data;
    }

    static final CollationSettings getSettings() {
        CollationTailoring root = getRoot();
        return (CollationSettings) root.settings.readOnly();
    }

    static {
        CollationTailoring t = null;
        RuntimeException e2 = null;
        try {
            ByteBuffer bytes = ICUBinary.getRequiredData("coll/ucadata.icu");
            CollationTailoring t2 = new CollationTailoring(null);
            CollationDataReader.read(null, bytes, t2);
            t = t2;
        } catch (IOException e) {
            e2 = new MissingResourceException("IOException while reading CLDR root data", "CollationRoot", "data/icudt66b/coll/ucadata.icu");
        } catch (RuntimeException e3) {
            e2 = e3;
        }
        rootSingleton = t;
        exception = e2;
    }
}
