package android.icu.impl.number;

import android.icu.impl.coll.Collation;
import android.icu.impl.number.Padder;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class PatternStringParser {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    public static final int IGNORE_ROUNDING_ALWAYS = 2;
    public static final int IGNORE_ROUNDING_IF_CURRENCY = 1;
    public static final int IGNORE_ROUNDING_NEVER = 0;

    public static class ParsedSubpatternInfo {
        public long groupingSizes = 281474976645120L;
        public int integerLeadingHashSigns = 0;
        public int integerTrailingHashSigns = 0;
        public int integerNumerals = 0;
        public int integerAtSigns = 0;
        public int integerTotal = 0;
        public int fractionNumerals = 0;
        public int fractionHashSigns = 0;
        public int fractionTotal = 0;
        public boolean hasDecimal = false;
        public int widthExceptAffixes = 0;
        public Padder.PadPosition paddingLocation = null;
        public DecimalQuantity_DualStorageBCD rounding = null;
        public boolean exponentHasPlusSign = false;
        public int exponentZeros = 0;
        public boolean hasPercentSign = false;
        public boolean hasPerMilleSign = false;
        public boolean hasCurrencySign = false;
        public boolean hasMinusSign = false;
        public boolean hasPlusSign = false;
        public long prefixEndpoints = 0;
        public long suffixEndpoints = 0;
        public long paddingEndpoints = 0;
    }

    public static ParsedPatternInfo parseToPatternInfo(String patternString) {
        ParserState state = new ParserState(patternString);
        ParsedPatternInfo result = new ParsedPatternInfo(patternString);
        consumePattern(state, result);
        return result;
    }

    public static DecimalFormatProperties parseToProperties(String pattern, int ignoreRounding) {
        DecimalFormatProperties properties = new DecimalFormatProperties();
        parseToExistingPropertiesImpl(pattern, properties, ignoreRounding);
        return properties;
    }

    public static DecimalFormatProperties parseToProperties(String pattern) {
        return parseToProperties(pattern, 0);
    }

    public static void parseToExistingProperties(String pattern, DecimalFormatProperties properties, int ignoreRounding) {
        parseToExistingPropertiesImpl(pattern, properties, ignoreRounding);
    }

    public static void parseToExistingProperties(String pattern, DecimalFormatProperties properties) {
        parseToExistingProperties(pattern, properties, 0);
    }

    public static class ParsedPatternInfo implements AffixPatternProvider {
        public ParsedSubpatternInfo negative;
        public String pattern;
        public ParsedSubpatternInfo positive;

        private ParsedPatternInfo(String pattern) {
            this.pattern = pattern;
        }

        @Override // android.icu.impl.number.AffixPatternProvider
        public char charAt(int flags, int index) {
            long endpoints = getEndpoints(flags);
            int left = (int) ((-1) & endpoints);
            int right = (int) (endpoints >>> 32);
            if (index < 0 || index >= right - left) {
                throw new IndexOutOfBoundsException();
            }
            return this.pattern.charAt(left + index);
        }

        @Override // android.icu.impl.number.AffixPatternProvider
        public int length(int flags) {
            return getLengthFromEndpoints(getEndpoints(flags));
        }

        public static int getLengthFromEndpoints(long endpoints) {
            int left = (int) ((-1) & endpoints);
            int right = (int) (endpoints >>> 32);
            return right - left;
        }

        @Override // android.icu.impl.number.AffixPatternProvider
        public String getString(int flags) {
            long endpoints = getEndpoints(flags);
            int left = (int) ((-1) & endpoints);
            int right = (int) (endpoints >>> 32);
            if (left == right) {
                return "";
            }
            return this.pattern.substring(left, right);
        }

        private long getEndpoints(int flags) {
            boolean prefix = (flags & 256) != 0;
            boolean isNegative = (flags & 512) != 0;
            boolean padding = (flags & 1024) != 0;
            if (isNegative && padding) {
                return this.negative.paddingEndpoints;
            }
            if (padding) {
                return this.positive.paddingEndpoints;
            }
            if (prefix && isNegative) {
                return this.negative.prefixEndpoints;
            }
            if (prefix) {
                return this.positive.prefixEndpoints;
            }
            if (isNegative) {
                return this.negative.suffixEndpoints;
            }
            return this.positive.suffixEndpoints;
        }

        @Override // android.icu.impl.number.AffixPatternProvider
        public boolean positiveHasPlusSign() {
            return this.positive.hasPlusSign;
        }

        @Override // android.icu.impl.number.AffixPatternProvider
        public boolean hasNegativeSubpattern() {
            return this.negative != null;
        }

        @Override // android.icu.impl.number.AffixPatternProvider
        public boolean negativeHasMinusSign() {
            return this.negative.hasMinusSign;
        }

        @Override // android.icu.impl.number.AffixPatternProvider
        public boolean hasCurrencySign() {
            ParsedSubpatternInfo parsedSubpatternInfo;
            return this.positive.hasCurrencySign || ((parsedSubpatternInfo = this.negative) != null && parsedSubpatternInfo.hasCurrencySign);
        }

        @Override // android.icu.impl.number.AffixPatternProvider
        public boolean containsSymbolType(int type) {
            return AffixUtils.containsType(this.pattern, type);
        }

        @Override // android.icu.impl.number.AffixPatternProvider
        public boolean hasBody() {
            return this.positive.integerTotal > 0;
        }
    }

    private static class ParserState {
        int offset = 0;
        final String pattern;

        ParserState(String pattern) {
            this.pattern = pattern;
        }

        int peek() {
            if (this.offset == this.pattern.length()) {
                return -1;
            }
            return this.pattern.codePointAt(this.offset);
        }

        int next() {
            int codePoint = peek();
            this.offset += Character.charCount(codePoint);
            return codePoint;
        }

        IllegalArgumentException toParseException(String message) {
            return new IllegalArgumentException("Malformed pattern for ICU DecimalFormat: \"" + this.pattern + "\": " + message + " at position " + this.offset);
        }
    }

    private static void consumePattern(ParserState state, ParsedPatternInfo result) {
        result.positive = new ParsedSubpatternInfo();
        consumeSubpattern(state, result.positive);
        if (state.peek() == 59) {
            state.next();
            if (state.peek() != -1) {
                result.negative = new ParsedSubpatternInfo();
                consumeSubpattern(state, result.negative);
            }
        }
        if (state.peek() != -1) {
            throw state.toParseException("Found unquoted special character");
        }
    }

    private static void consumeSubpattern(ParserState state, ParsedSubpatternInfo result) {
        consumePadding(state, result, Padder.PadPosition.BEFORE_PREFIX);
        result.prefixEndpoints = consumeAffix(state, result);
        consumePadding(state, result, Padder.PadPosition.AFTER_PREFIX);
        consumeFormat(state, result);
        consumeExponent(state, result);
        consumePadding(state, result, Padder.PadPosition.BEFORE_SUFFIX);
        result.suffixEndpoints = consumeAffix(state, result);
        consumePadding(state, result, Padder.PadPosition.AFTER_SUFFIX);
    }

    private static void consumePadding(ParserState state, ParsedSubpatternInfo result, Padder.PadPosition paddingLocation) {
        if (state.peek() != 42) {
            return;
        }
        if (result.paddingLocation != null) {
            throw state.toParseException("Cannot have multiple pad specifiers");
        }
        result.paddingLocation = paddingLocation;
        state.next();
        result.paddingEndpoints |= state.offset;
        consumeLiteral(state);
        result.paddingEndpoints |= state.offset << 32;
    }

    private static long consumeAffix(ParserState state, ParsedSubpatternInfo result) {
        long endpoints = state.offset;
        while (true) {
            int iPeek = state.peek();
            if (iPeek != -1 && iPeek != 35) {
                if (iPeek == 37) {
                    result.hasPercentSign = true;
                } else if (iPeek != 59 && iPeek != 64) {
                    if (iPeek == 164) {
                        result.hasCurrencySign = true;
                    } else if (iPeek == 8240) {
                        result.hasPerMilleSign = true;
                    } else {
                        switch (iPeek) {
                            case 42:
                            case 44:
                            case 46:
                                break;
                            case 43:
                                result.hasPlusSign = true;
                                break;
                            case 45:
                                result.hasMinusSign = true;
                                break;
                            default:
                                switch (iPeek) {
                                }
                        }
                    }
                }
                consumeLiteral(state);
            }
        }
        return endpoints | (state.offset << 32);
    }

    private static void consumeLiteral(ParserState state) {
        if (state.peek() == -1) {
            throw state.toParseException("Expected unquoted literal but found EOL");
        }
        if (state.peek() == 39) {
            state.next();
            while (state.peek() != 39) {
                if (state.peek() == -1) {
                    throw state.toParseException("Expected quoted literal but found EOL");
                }
                state.next();
            }
            state.next();
            return;
        }
        state.next();
    }

    private static void consumeFormat(ParserState state, ParsedSubpatternInfo result) {
        consumeIntegerFormat(state, result);
        if (state.peek() == 46) {
            state.next();
            result.hasDecimal = true;
            result.widthExceptAffixes++;
            consumeFractionFormat(state, result);
        }
    }

    private static void consumeIntegerFormat(ParserState state, ParsedSubpatternInfo result) {
        while (true) {
            int iPeek = state.peek();
            if (iPeek != 35) {
                if (iPeek == 44) {
                    result.widthExceptAffixes++;
                    result.groupingSizes <<= 16;
                } else if (iPeek == 64) {
                    if (result.integerNumerals > 0) {
                        throw state.toParseException("Cannot mix 0 and @");
                    }
                    if (result.integerTrailingHashSigns > 0) {
                        throw state.toParseException("Cannot nest # inside of a run of @");
                    }
                    result.widthExceptAffixes++;
                    result.groupingSizes++;
                    result.integerAtSigns++;
                    result.integerTotal++;
                } else {
                    switch (iPeek) {
                        case 48:
                        case 49:
                        case 50:
                        case 51:
                        case 52:
                        case 53:
                        case 54:
                        case 55:
                        case 56:
                        case 57:
                            if (result.integerAtSigns > 0) {
                                throw state.toParseException("Cannot mix @ and 0");
                            }
                            result.widthExceptAffixes++;
                            result.groupingSizes++;
                            result.integerNumerals++;
                            result.integerTotal++;
                            if (state.peek() != 48 && result.rounding == null) {
                                result.rounding = new DecimalQuantity_DualStorageBCD();
                            }
                            if (result.rounding == null) {
                                break;
                            } else {
                                result.rounding.appendDigit((byte) (state.peek() - 48), 0, true);
                                break;
                            }
                            break;
                        default:
                            short grouping1 = (short) (result.groupingSizes & 65535);
                            short grouping2 = (short) ((result.groupingSizes >>> 16) & 65535);
                            short grouping3 = (short) (65535 & (result.groupingSizes >>> 32));
                            if (grouping1 == 0 && grouping2 != -1) {
                                throw state.toParseException("Trailing grouping separator is invalid");
                            }
                            if (grouping2 == 0 && grouping3 != -1) {
                                throw state.toParseException("Grouping width of zero is invalid");
                            }
                            return;
                    }
                }
            } else {
                if (result.integerNumerals > 0) {
                    throw state.toParseException("# cannot follow 0 before decimal point");
                }
                result.widthExceptAffixes++;
                result.groupingSizes++;
                if (result.integerAtSigns > 0) {
                    result.integerTrailingHashSigns++;
                } else {
                    result.integerLeadingHashSigns++;
                }
                result.integerTotal++;
            }
            state.next();
        }
    }

    private static void consumeFractionFormat(ParserState state, ParsedSubpatternInfo result) {
        int zeroCounter = 0;
        while (true) {
            int iPeek = state.peek();
            if (iPeek == 35) {
                result.widthExceptAffixes++;
                result.fractionHashSigns++;
                result.fractionTotal++;
                zeroCounter++;
            } else {
                switch (iPeek) {
                    case 48:
                    case 49:
                    case 50:
                    case 51:
                    case 52:
                    case 53:
                    case 54:
                    case 55:
                    case 56:
                    case 57:
                        if (result.fractionHashSigns > 0) {
                            throw state.toParseException("0 cannot follow # after decimal point");
                        }
                        result.widthExceptAffixes++;
                        result.fractionNumerals++;
                        result.fractionTotal++;
                        if (state.peek() == 48) {
                            zeroCounter++;
                            break;
                        } else {
                            if (result.rounding == null) {
                                result.rounding = new DecimalQuantity_DualStorageBCD();
                            }
                            result.rounding.appendDigit((byte) (state.peek() - 48), zeroCounter, false);
                            zeroCounter = 0;
                            break;
                        }
                    default:
                        return;
                }
            }
            state.next();
        }
    }

    private static void consumeExponent(ParserState state, ParsedSubpatternInfo result) {
        if (state.peek() != 69) {
            return;
        }
        if ((result.groupingSizes & Collation.MAX_PRIMARY) != Collation.MAX_PRIMARY) {
            throw state.toParseException("Cannot have grouping separator in scientific notation");
        }
        state.next();
        result.widthExceptAffixes++;
        if (state.peek() == 43) {
            state.next();
            result.exponentHasPlusSign = true;
            result.widthExceptAffixes++;
        }
        while (state.peek() == 48) {
            state.next();
            result.exponentZeros++;
            result.widthExceptAffixes++;
        }
    }

    private static void parseToExistingPropertiesImpl(String pattern, DecimalFormatProperties properties, int ignoreRounding) {
        if (pattern == null || pattern.length() == 0) {
            properties.clear();
        } else {
            ParsedPatternInfo patternInfo = parseToPatternInfo(pattern);
            patternInfoToProperties(properties, patternInfo, ignoreRounding);
        }
    }

    private static void patternInfoToProperties(DecimalFormatProperties properties, ParsedPatternInfo patternInfo, int _ignoreRounding) {
        boolean ignoreRounding;
        int minInt;
        int minFrac;
        ParsedSubpatternInfo positive = patternInfo.positive;
        if (_ignoreRounding == 0) {
            ignoreRounding = false;
        } else if (_ignoreRounding == 1) {
            ignoreRounding = positive.hasCurrencySign;
        } else {
            ignoreRounding = true;
        }
        short grouping1 = (short) (positive.groupingSizes & 65535);
        short grouping2 = (short) ((positive.groupingSizes >>> 16) & 65535);
        short grouping3 = (short) (65535 & (positive.groupingSizes >>> 32));
        if (grouping2 != -1) {
            properties.setGroupingSize(grouping1);
            properties.setGroupingUsed(true);
        } else {
            properties.setGroupingSize(-1);
            properties.setGroupingUsed(false);
        }
        if (grouping3 != -1) {
            properties.setSecondaryGroupingSize(grouping2);
        } else {
            properties.setSecondaryGroupingSize(-1);
        }
        if (positive.integerTotal == 0 && positive.fractionTotal > 0) {
            minInt = 0;
            minFrac = Math.max(1, positive.fractionNumerals);
        } else {
            int minInt2 = positive.integerNumerals;
            if (minInt2 == 0 && positive.fractionNumerals == 0) {
                minInt = 1;
                minFrac = 0;
            } else {
                minInt = positive.integerNumerals;
                minFrac = positive.fractionNumerals;
            }
        }
        if (positive.integerAtSigns > 0) {
            properties.setMinimumFractionDigits(-1);
            properties.setMaximumFractionDigits(-1);
            properties.setRoundingIncrement(null);
            properties.setMinimumSignificantDigits(positive.integerAtSigns);
            properties.setMaximumSignificantDigits(positive.integerAtSigns + positive.integerTrailingHashSigns);
        } else if (positive.rounding != null) {
            if (!ignoreRounding) {
                properties.setMinimumFractionDigits(minFrac);
                properties.setMaximumFractionDigits(positive.fractionTotal);
                properties.setRoundingIncrement(positive.rounding.toBigDecimal().setScale(positive.fractionNumerals));
            } else {
                properties.setMinimumFractionDigits(-1);
                properties.setMaximumFractionDigits(-1);
                properties.setRoundingIncrement(null);
            }
            properties.setMinimumSignificantDigits(-1);
            properties.setMaximumSignificantDigits(-1);
        } else {
            if (!ignoreRounding) {
                properties.setMinimumFractionDigits(minFrac);
                properties.setMaximumFractionDigits(positive.fractionTotal);
                properties.setRoundingIncrement(null);
            } else {
                properties.setMinimumFractionDigits(-1);
                properties.setMaximumFractionDigits(-1);
                properties.setRoundingIncrement(null);
            }
            properties.setMinimumSignificantDigits(-1);
            properties.setMaximumSignificantDigits(-1);
        }
        if (positive.hasDecimal && positive.fractionTotal == 0) {
            properties.setDecimalSeparatorAlwaysShown(true);
        } else {
            properties.setDecimalSeparatorAlwaysShown(false);
        }
        if (positive.exponentZeros > 0) {
            properties.setExponentSignAlwaysShown(positive.exponentHasPlusSign);
            properties.setMinimumExponentDigits(positive.exponentZeros);
            if (positive.integerAtSigns == 0) {
                properties.setMinimumIntegerDigits(positive.integerNumerals);
                properties.setMaximumIntegerDigits(positive.integerTotal);
            } else {
                properties.setMinimumIntegerDigits(1);
                properties.setMaximumIntegerDigits(-1);
            }
        } else {
            properties.setExponentSignAlwaysShown(false);
            properties.setMinimumExponentDigits(-1);
            properties.setMinimumIntegerDigits(minInt);
            properties.setMaximumIntegerDigits(-1);
        }
        String posPrefix = patternInfo.getString(256);
        String posSuffix = patternInfo.getString(0);
        if (positive.paddingLocation == null) {
            properties.setFormatWidth(-1);
            properties.setPadString(null);
            properties.setPadPosition(null);
        } else {
            int paddingWidth = positive.widthExceptAffixes + AffixUtils.estimateLength(posPrefix) + AffixUtils.estimateLength(posSuffix);
            properties.setFormatWidth(paddingWidth);
            String rawPaddingString = patternInfo.getString(1024);
            if (rawPaddingString.length() == 1) {
                properties.setPadString(rawPaddingString);
            } else if (rawPaddingString.length() != 2) {
                properties.setPadString(rawPaddingString.substring(1, rawPaddingString.length() - 1));
            } else if (rawPaddingString.charAt(0) == '\'') {
                properties.setPadString("'");
            } else {
                properties.setPadString(rawPaddingString);
            }
            properties.setPadPosition(positive.paddingLocation);
        }
        properties.setPositivePrefixPattern(posPrefix);
        properties.setPositiveSuffixPattern(posSuffix);
        if (patternInfo.negative != null) {
            properties.setNegativePrefixPattern(patternInfo.getString(768));
            properties.setNegativeSuffixPattern(patternInfo.getString(512));
        } else {
            properties.setNegativePrefixPattern(null);
            properties.setNegativeSuffixPattern(null);
        }
        if (positive.hasPercentSign) {
            properties.setMagnitudeMultiplier(2);
        } else if (positive.hasPerMilleSign) {
            properties.setMagnitudeMultiplier(3);
        } else {
            properties.setMagnitudeMultiplier(0);
        }
    }
}
