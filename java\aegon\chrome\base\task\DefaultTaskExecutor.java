package aegon.chrome.base.task;

import aegon.chrome.base.ThreadUtils;
import android.view.Choreographer;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class DefaultTaskExecutor implements TaskExecutor {
    private final Map<TaskTraits, TaskRunner> mTraitsToRunnerMap = new HashMap();

    @Override // aegon.chrome.base.task.TaskExecutor
    public boolean canRunTaskImmediately(TaskTraits taskTraits) {
        return false;
    }

    DefaultTaskExecutor() {
    }

    @Override // aegon.chrome.base.task.TaskExecutor
    public TaskRunner createTaskRunner(TaskTraits taskTraits) {
        return taskTraits.mIsChoreographerFrame ? createChoreographerTaskRunner() : new TaskRunnerImpl(taskTraits);
    }

    @Override // aegon.chrome.base.task.TaskExecutor
    public SequencedTaskRunner createSequencedTaskRunner(TaskTraits taskTraits) {
        return taskTraits.mIsChoreographerFrame ? createChoreographerTaskRunner() : new SequencedTaskRunnerImpl(taskTraits);
    }

    @Override // aegon.chrome.base.task.TaskExecutor
    public SingleThreadTaskRunner createSingleThreadTaskRunner(TaskTraits taskTraits) {
        return taskTraits.mIsChoreographerFrame ? createChoreographerTaskRunner() : new SingleThreadTaskRunnerImpl(null, taskTraits);
    }

    @Override // aegon.chrome.base.task.TaskExecutor
    public synchronized void postDelayedTask(TaskTraits taskTraits, Runnable runnable, long j) {
        if (taskTraits.hasExtension()) {
            createTaskRunner(taskTraits).postDelayedTask(runnable, j);
            return;
        }
        TaskRunner taskRunnerCreateTaskRunner = this.mTraitsToRunnerMap.get(taskTraits);
        if (taskRunnerCreateTaskRunner == null) {
            taskRunnerCreateTaskRunner = createTaskRunner(taskTraits);
            this.mTraitsToRunnerMap.put(taskTraits, taskRunnerCreateTaskRunner);
        }
        taskRunnerCreateTaskRunner.postDelayedTask(runnable, j);
    }

    private synchronized ChoreographerTaskRunner createChoreographerTaskRunner() {
        return (ChoreographerTaskRunner) ThreadUtils.runOnUiThreadBlockingNoException(new Callable() { // from class: aegon.chrome.base.task.-$$Lambda$DefaultTaskExecutor$zTXv6sqgZBSI7vPYAlZHJn3WYXo
            @Override // java.util.concurrent.Callable
            public final Object call() {
                return DefaultTaskExecutor.lambda$createChoreographerTaskRunner$0();
            }
        });
    }

    static /* synthetic */ ChoreographerTaskRunner lambda$createChoreographerTaskRunner$0() {
        return new ChoreographerTaskRunner(Choreographer.getInstance());
    }
}
