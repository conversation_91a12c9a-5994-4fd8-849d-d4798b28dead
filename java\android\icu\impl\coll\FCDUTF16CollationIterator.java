package android.icu.impl.coll;

import android.icu.impl.Normalizer2Impl;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class FCDUTF16CollationIterator extends UTF16CollationIterator {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final int rawStart = 0;
    private int checkDir;
    private final Normalizer2Impl nfcImpl;
    private StringBuilder normalized;
    private int rawLimit;
    private CharSequence rawSeq;
    private int segmentLimit;
    private int segmentStart;

    public FCDUTF16CollationIterator(CollationData d2) {
        super(d2);
        this.nfcImpl = d2.nfcImpl;
    }

    public FCDUTF16CollationIterator(CollationData data, boolean numeric, CharSequence s, int p) {
        super(data, numeric, s, p);
        this.rawSeq = s;
        this.segmentStart = p;
        this.rawLimit = s.length();
        this.nfcImpl = data.nfcImpl;
        this.checkDir = 1;
    }

    @Override // android.icu.impl.coll.UTF16CollationIterator, android.icu.impl.coll.CollationIterator
    public boolean equals(Object other) {
        if (!(other instanceof CollationIterator) || !equals(other) || !(other instanceof FCDUTF16CollationIterator)) {
            return false;
        }
        FCDUTF16CollationIterator o = (FCDUTF16CollationIterator) other;
        int i = this.checkDir;
        if (i != o.checkDir) {
            return false;
        }
        if (i == 0) {
            if ((this.seq == this.rawSeq) != (o.seq == o.rawSeq)) {
                return false;
            }
        }
        return (this.checkDir != 0 || this.seq == this.rawSeq) ? this.pos - 0 == o.pos - 0 : this.segmentStart - 0 == o.segmentStart - 0 && this.pos - this.start == o.pos - o.start;
    }

    @Override // android.icu.impl.coll.UTF16CollationIterator, android.icu.impl.coll.CollationIterator
    public int hashCode() {
        return 42;
    }

    @Override // android.icu.impl.coll.UTF16CollationIterator, android.icu.impl.coll.CollationIterator
    public void resetToOffset(int newOffset) {
        reset();
        this.seq = this.rawSeq;
        int i = newOffset + 0;
        this.pos = i;
        this.segmentStart = i;
        this.start = i;
        this.limit = this.rawLimit;
        this.checkDir = 1;
    }

    @Override // android.icu.impl.coll.UTF16CollationIterator, android.icu.impl.coll.CollationIterator
    public int getOffset() {
        if (this.checkDir != 0 || this.seq == this.rawSeq) {
            return this.pos + 0;
        }
        if (this.pos == this.start) {
            return this.segmentStart + 0;
        }
        return this.segmentLimit + 0;
    }

    @Override // android.icu.impl.coll.UTF16CollationIterator
    public void setText(boolean numeric, CharSequence s, int p) {
        super.setText(numeric, s, p);
        this.rawSeq = s;
        this.segmentStart = p;
        int length = s.length();
        this.limit = length;
        this.rawLimit = length;
        this.checkDir = 1;
    }

    @Override // android.icu.impl.coll.UTF16CollationIterator, android.icu.impl.coll.CollationIterator
    public int nextCodePoint() {
        char c2;
        while (true) {
            int i = this.checkDir;
            if (i > 0) {
                if (this.pos == this.limit) {
                    return -1;
                }
                CharSequence charSequence = this.seq;
                int i2 = this.pos;
                this.pos = i2 + 1;
                c2 = charSequence.charAt(i2);
                if (CollationFCD.hasTccc(c2) && (CollationFCD.maybeTibetanCompositeVowel(c2) || (this.pos != this.limit && CollationFCD.hasLccc(this.seq.charAt(this.pos))))) {
                    this.pos--;
                    nextSegment();
                    CharSequence charSequence2 = this.seq;
                    int i3 = this.pos;
                    this.pos = i3 + 1;
                    c2 = charSequence2.charAt(i3);
                }
            } else {
                if (i == 0 && this.pos != this.limit) {
                    CharSequence charSequence3 = this.seq;
                    int i4 = this.pos;
                    this.pos = i4 + 1;
                    c2 = charSequence3.charAt(i4);
                    break;
                }
                switchToForward();
            }
        }
        if (Character.isHighSurrogate(c2) && this.pos != this.limit) {
            char trail = this.seq.charAt(this.pos);
            if (Character.isLowSurrogate(trail)) {
                this.pos++;
                return Character.toCodePoint(c2, trail);
            }
        }
        return c2;
    }

    @Override // android.icu.impl.coll.UTF16CollationIterator, android.icu.impl.coll.CollationIterator
    public int previousCodePoint() {
        char c2;
        while (true) {
            int i = this.checkDir;
            if (i < 0) {
                if (this.pos == this.start) {
                    return -1;
                }
                CharSequence charSequence = this.seq;
                int i2 = this.pos - 1;
                this.pos = i2;
                c2 = charSequence.charAt(i2);
                if (CollationFCD.hasLccc(c2) && (CollationFCD.maybeTibetanCompositeVowel(c2) || (this.pos != this.start && CollationFCD.hasTccc(this.seq.charAt(this.pos - 1))))) {
                    this.pos++;
                    previousSegment();
                    CharSequence charSequence2 = this.seq;
                    int i3 = this.pos - 1;
                    this.pos = i3;
                    c2 = charSequence2.charAt(i3);
                }
            } else {
                if (i == 0 && this.pos != this.start) {
                    CharSequence charSequence3 = this.seq;
                    int i4 = this.pos - 1;
                    this.pos = i4;
                    c2 = charSequence3.charAt(i4);
                    break;
                }
                switchToBackward();
            }
        }
        if (Character.isLowSurrogate(c2) && this.pos != this.start) {
            char lead = this.seq.charAt(this.pos - 1);
            if (Character.isHighSurrogate(lead)) {
                this.pos--;
                return Character.toCodePoint(lead, c2);
            }
        }
        return c2;
    }

    @Override // android.icu.impl.coll.UTF16CollationIterator, android.icu.impl.coll.CollationIterator
    protected long handleNextCE32() {
        char c2;
        while (true) {
            int i = this.checkDir;
            if (i > 0) {
                if (this.pos == this.limit) {
                    return -4294967104L;
                }
                CharSequence charSequence = this.seq;
                int i2 = this.pos;
                this.pos = i2 + 1;
                c2 = charSequence.charAt(i2);
                if (CollationFCD.hasTccc(c2) && (CollationFCD.maybeTibetanCompositeVowel(c2) || (this.pos != this.limit && CollationFCD.hasLccc(this.seq.charAt(this.pos))))) {
                    this.pos--;
                    nextSegment();
                    CharSequence charSequence2 = this.seq;
                    int i3 = this.pos;
                    this.pos = i3 + 1;
                    c2 = charSequence2.charAt(i3);
                }
            } else {
                if (i == 0 && this.pos != this.limit) {
                    CharSequence charSequence3 = this.seq;
                    int i4 = this.pos;
                    this.pos = i4 + 1;
                    c2 = charSequence3.charAt(i4);
                    break;
                }
                switchToForward();
            }
        }
        return makeCodePointAndCE32Pair(c2, this.trie.getFromU16SingleLead(c2));
    }

    @Override // android.icu.impl.coll.UTF16CollationIterator, android.icu.impl.coll.CollationIterator
    protected void forwardNumCodePoints(int num) {
        while (num > 0 && nextCodePoint() >= 0) {
            num--;
        }
    }

    @Override // android.icu.impl.coll.UTF16CollationIterator, android.icu.impl.coll.CollationIterator
    protected void backwardNumCodePoints(int num) {
        while (num > 0 && previousCodePoint() >= 0) {
            num--;
        }
    }

    private void switchToForward() {
        if (this.checkDir < 0) {
            int i = this.pos;
            this.segmentStart = i;
            this.start = i;
            if (this.pos == this.segmentLimit) {
                this.limit = this.rawLimit;
                this.checkDir = 1;
                return;
            } else {
                this.checkDir = 0;
                return;
            }
        }
        CharSequence charSequence = this.seq;
        CharSequence charSequence2 = this.rawSeq;
        if (charSequence != charSequence2) {
            this.seq = charSequence2;
            int i2 = this.segmentLimit;
            this.segmentStart = i2;
            this.start = i2;
            this.pos = i2;
        }
        this.limit = this.rawLimit;
        this.checkDir = 1;
    }

    private void nextSegment() {
        int q;
        int c2;
        int p = this.pos;
        int prevCC = 0;
        do {
            int q2 = p;
            int c3 = Character.codePointAt(this.seq, p);
            p += Character.charCount(c3);
            int fcd16 = this.nfcImpl.getFCD16(c3);
            int leadCC = fcd16 >> 8;
            if (leadCC == 0 && q2 != this.pos) {
                this.segmentLimit = q2;
                this.limit = q2;
                break;
            }
            if (leadCC != 0 && (prevCC > leadCC || CollationFCD.isFCD16OfTibetanCompositeVowel(fcd16))) {
                do {
                    q = p;
                    if (p == this.rawLimit) {
                        break;
                    }
                    c2 = Character.codePointAt(this.seq, p);
                    p += Character.charCount(c2);
                } while (this.nfcImpl.getFCD16(c2) > 255);
                normalize(this.pos, q);
                this.pos = this.start;
            } else {
                prevCC = fcd16 & 255;
                if (p == this.rawLimit) {
                    break;
                }
            }
        } while (prevCC != 0);
        this.segmentLimit = p;
        this.limit = p;
        this.checkDir = 0;
    }

    private void switchToBackward() {
        if (this.checkDir > 0) {
            int i = this.pos;
            this.segmentLimit = i;
            this.limit = i;
            if (this.pos == this.segmentStart) {
                this.start = 0;
                this.checkDir = -1;
                return;
            } else {
                this.checkDir = 0;
                return;
            }
        }
        CharSequence charSequence = this.seq;
        CharSequence charSequence2 = this.rawSeq;
        if (charSequence != charSequence2) {
            this.seq = charSequence2;
            int i2 = this.segmentStart;
            this.segmentLimit = i2;
            this.limit = i2;
            this.pos = i2;
        }
        this.start = 0;
        this.checkDir = -1;
    }

    private void previousSegment() {
        int q;
        int fcd16;
        int p = this.pos;
        int nextCC = 0;
        do {
            int q2 = p;
            int c2 = Character.codePointBefore(this.seq, p);
            p -= Character.charCount(c2);
            int fcd162 = this.nfcImpl.getFCD16(c2);
            int trailCC = fcd162 & 255;
            if (trailCC == 0 && q2 != this.pos) {
                this.segmentStart = q2;
                this.start = q2;
                break;
            }
            if (trailCC != 0 && ((nextCC != 0 && trailCC > nextCC) || CollationFCD.isFCD16OfTibetanCompositeVowel(fcd162))) {
                do {
                    q = p;
                    if (fcd162 <= 255 || p == 0) {
                        break;
                    }
                    int c3 = Character.codePointBefore(this.seq, p);
                    p -= Character.charCount(c3);
                    fcd16 = this.nfcImpl.getFCD16(c3);
                    fcd162 = fcd16;
                } while (fcd16 != 0);
                normalize(q, this.pos);
                this.pos = this.limit;
            } else {
                nextCC = fcd162 >> 8;
                if (p == 0) {
                    break;
                }
            }
        } while (nextCC != 0);
        this.segmentStart = p;
        this.start = p;
        this.checkDir = 0;
    }

    private void normalize(int from, int to) {
        if (this.normalized == null) {
            this.normalized = new StringBuilder();
        }
        this.nfcImpl.decompose(this.rawSeq, from, to, this.normalized, to - from);
        this.segmentStart = from;
        this.segmentLimit = to;
        this.seq = this.normalized;
        this.start = 0;
        this.limit = this.start + this.normalized.length();
    }
}
