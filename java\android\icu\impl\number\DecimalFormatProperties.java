package android.icu.impl.number;

import android.icu.impl.number.Padder;
import android.icu.text.CompactDecimalFormat;
import android.icu.text.CurrencyPluralInfo;
import android.icu.text.PluralRules;
import android.icu.util.Currency;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.p654io.IOException;
import java.p654io.ObjectInputStream;
import java.p654io.ObjectOutputStream;
import java.p654io.Serializable;
import java.util.ArrayList;
import java.util.Map;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class DecimalFormatProperties implements Cloneable, Serializable {
    private static final DecimalFormatProperties DEFAULT = new DecimalFormatProperties();
    private static final long serialVersionUID = 4095518955889349243L;
    private transient Map<String, Map<String, String>> compactCustomData;
    private transient CompactDecimalFormat.CompactStyle compactStyle;
    private transient Currency currency;
    private transient CurrencyPluralInfo currencyPluralInfo;
    private transient Currency.CurrencyUsage currencyUsage;
    private transient boolean decimalPatternMatchRequired;
    private transient boolean decimalSeparatorAlwaysShown;
    private transient boolean exponentSignAlwaysShown;
    private transient int formatWidth;
    private transient int groupingSize;
    private transient boolean groupingUsed;
    private transient int magnitudeMultiplier;
    private transient MathContext mathContext;
    private transient int maximumFractionDigits;
    private transient int maximumIntegerDigits;
    private transient int maximumSignificantDigits;
    private transient int minimumExponentDigits;
    private transient int minimumFractionDigits;
    private transient int minimumGroupingDigits;
    private transient int minimumIntegerDigits;
    private transient int minimumSignificantDigits;
    private transient BigDecimal multiplier;
    private transient String negativePrefix;
    private transient String negativePrefixPattern;
    private transient String negativeSuffix;
    private transient String negativeSuffixPattern;
    private transient Padder.PadPosition padPosition;
    private transient String padString;
    private transient boolean parseCaseSensitive;
    private transient boolean parseIntegerOnly;
    private transient ParseMode parseMode;
    private transient boolean parseNoExponent;
    private transient boolean parseToBigDecimal;
    private transient PluralRules pluralRules;
    private transient String positivePrefix;
    private transient String positivePrefixPattern;
    private transient String positiveSuffix;
    private transient String positiveSuffixPattern;
    private transient BigDecimal roundingIncrement;
    private transient RoundingMode roundingMode;
    private transient int secondaryGroupingSize;
    private transient boolean signAlwaysShown;

    public enum ParseMode {
        LENIENT,
        STRICT,
        JAVA_COMPATIBILITY
    }

    public DecimalFormatProperties() {
        clear();
    }

    private DecimalFormatProperties _clear() {
        this.compactCustomData = null;
        this.compactStyle = null;
        this.currency = null;
        this.currencyPluralInfo = null;
        this.currencyUsage = null;
        this.decimalPatternMatchRequired = false;
        this.decimalSeparatorAlwaysShown = false;
        this.exponentSignAlwaysShown = false;
        this.formatWidth = -1;
        this.groupingSize = -1;
        this.groupingUsed = true;
        this.magnitudeMultiplier = 0;
        this.mathContext = null;
        this.maximumFractionDigits = -1;
        this.maximumIntegerDigits = -1;
        this.maximumSignificantDigits = -1;
        this.minimumExponentDigits = -1;
        this.minimumFractionDigits = -1;
        this.minimumGroupingDigits = -1;
        this.minimumIntegerDigits = -1;
        this.minimumSignificantDigits = -1;
        this.multiplier = null;
        this.negativePrefix = null;
        this.negativePrefixPattern = null;
        this.negativeSuffix = null;
        this.negativeSuffixPattern = null;
        this.padPosition = null;
        this.padString = null;
        this.parseCaseSensitive = false;
        this.parseIntegerOnly = false;
        this.parseMode = null;
        this.parseNoExponent = false;
        this.parseToBigDecimal = false;
        this.pluralRules = null;
        this.positivePrefix = null;
        this.positivePrefixPattern = null;
        this.positiveSuffix = null;
        this.positiveSuffixPattern = null;
        this.roundingIncrement = null;
        this.roundingMode = null;
        this.secondaryGroupingSize = -1;
        this.signAlwaysShown = false;
        return this;
    }

    private DecimalFormatProperties _copyFrom(DecimalFormatProperties other) {
        this.compactCustomData = other.compactCustomData;
        this.compactStyle = other.compactStyle;
        this.currency = other.currency;
        this.currencyPluralInfo = other.currencyPluralInfo;
        this.currencyUsage = other.currencyUsage;
        this.decimalPatternMatchRequired = other.decimalPatternMatchRequired;
        this.decimalSeparatorAlwaysShown = other.decimalSeparatorAlwaysShown;
        this.exponentSignAlwaysShown = other.exponentSignAlwaysShown;
        this.formatWidth = other.formatWidth;
        this.groupingSize = other.groupingSize;
        this.groupingUsed = other.groupingUsed;
        this.magnitudeMultiplier = other.magnitudeMultiplier;
        this.mathContext = other.mathContext;
        this.maximumFractionDigits = other.maximumFractionDigits;
        this.maximumIntegerDigits = other.maximumIntegerDigits;
        this.maximumSignificantDigits = other.maximumSignificantDigits;
        this.minimumExponentDigits = other.minimumExponentDigits;
        this.minimumFractionDigits = other.minimumFractionDigits;
        this.minimumGroupingDigits = other.minimumGroupingDigits;
        this.minimumIntegerDigits = other.minimumIntegerDigits;
        this.minimumSignificantDigits = other.minimumSignificantDigits;
        this.multiplier = other.multiplier;
        this.negativePrefix = other.negativePrefix;
        this.negativePrefixPattern = other.negativePrefixPattern;
        this.negativeSuffix = other.negativeSuffix;
        this.negativeSuffixPattern = other.negativeSuffixPattern;
        this.padPosition = other.padPosition;
        this.padString = other.padString;
        this.parseCaseSensitive = other.parseCaseSensitive;
        this.parseIntegerOnly = other.parseIntegerOnly;
        this.parseMode = other.parseMode;
        this.parseNoExponent = other.parseNoExponent;
        this.parseToBigDecimal = other.parseToBigDecimal;
        this.pluralRules = other.pluralRules;
        this.positivePrefix = other.positivePrefix;
        this.positivePrefixPattern = other.positivePrefixPattern;
        this.positiveSuffix = other.positiveSuffix;
        this.positiveSuffixPattern = other.positiveSuffixPattern;
        this.roundingIncrement = other.roundingIncrement;
        this.roundingMode = other.roundingMode;
        this.secondaryGroupingSize = other.secondaryGroupingSize;
        this.signAlwaysShown = other.signAlwaysShown;
        return this;
    }

    private boolean _equals(DecimalFormatProperties other) {
        boolean eq = 1 != 0 && _equalsHelper(this.compactCustomData, other.compactCustomData);
        boolean eq2 = eq && _equalsHelper(this.compactStyle, other.compactStyle);
        boolean eq3 = eq2 && _equalsHelper(this.currency, other.currency);
        boolean eq4 = eq3 && _equalsHelper(this.currencyPluralInfo, other.currencyPluralInfo);
        boolean eq5 = eq4 && _equalsHelper(this.currencyUsage, other.currencyUsage);
        boolean eq6 = eq5 && _equalsHelper(this.decimalPatternMatchRequired, other.decimalPatternMatchRequired);
        boolean eq7 = eq6 && _equalsHelper(this.decimalSeparatorAlwaysShown, other.decimalSeparatorAlwaysShown);
        boolean eq8 = eq7 && _equalsHelper(this.exponentSignAlwaysShown, other.exponentSignAlwaysShown);
        boolean eq9 = eq8 && _equalsHelper(this.formatWidth, other.formatWidth);
        boolean eq10 = eq9 && _equalsHelper(this.groupingSize, other.groupingSize);
        boolean eq11 = eq10 && _equalsHelper(this.groupingUsed, other.groupingUsed);
        boolean eq12 = eq11 && _equalsHelper(this.magnitudeMultiplier, other.magnitudeMultiplier);
        boolean eq13 = eq12 && _equalsHelper(this.mathContext, other.mathContext);
        boolean eq14 = eq13 && _equalsHelper(this.maximumFractionDigits, other.maximumFractionDigits);
        boolean eq15 = eq14 && _equalsHelper(this.maximumIntegerDigits, other.maximumIntegerDigits);
        boolean eq16 = eq15 && _equalsHelper(this.maximumSignificantDigits, other.maximumSignificantDigits);
        boolean eq17 = eq16 && _equalsHelper(this.minimumExponentDigits, other.minimumExponentDigits);
        boolean eq18 = eq17 && _equalsHelper(this.minimumFractionDigits, other.minimumFractionDigits);
        boolean eq19 = eq18 && _equalsHelper(this.minimumGroupingDigits, other.minimumGroupingDigits);
        boolean eq20 = eq19 && _equalsHelper(this.minimumIntegerDigits, other.minimumIntegerDigits);
        boolean eq21 = eq20 && _equalsHelper(this.minimumSignificantDigits, other.minimumSignificantDigits);
        boolean eq22 = eq21 && _equalsHelper(this.multiplier, other.multiplier);
        boolean eq23 = eq22 && _equalsHelper(this.negativePrefix, other.negativePrefix);
        boolean eq24 = eq23 && _equalsHelper(this.negativePrefixPattern, other.negativePrefixPattern);
        boolean eq25 = eq24 && _equalsHelper(this.negativeSuffix, other.negativeSuffix);
        boolean eq26 = eq25 && _equalsHelper(this.negativeSuffixPattern, other.negativeSuffixPattern);
        boolean eq27 = eq26 && _equalsHelper(this.padPosition, other.padPosition);
        boolean eq28 = eq27 && _equalsHelper(this.padString, other.padString);
        boolean eq29 = eq28 && _equalsHelper(this.parseCaseSensitive, other.parseCaseSensitive);
        boolean eq30 = eq29 && _equalsHelper(this.parseIntegerOnly, other.parseIntegerOnly);
        boolean eq31 = eq30 && _equalsHelper(this.parseMode, other.parseMode);
        boolean eq32 = eq31 && _equalsHelper(this.parseNoExponent, other.parseNoExponent);
        boolean eq33 = eq32 && _equalsHelper(this.parseToBigDecimal, other.parseToBigDecimal);
        boolean eq34 = eq33 && _equalsHelper(this.pluralRules, other.pluralRules);
        boolean eq35 = eq34 && _equalsHelper(this.positivePrefix, other.positivePrefix);
        boolean eq36 = eq35 && _equalsHelper(this.positivePrefixPattern, other.positivePrefixPattern);
        boolean eq37 = eq36 && _equalsHelper(this.positiveSuffix, other.positiveSuffix);
        boolean eq38 = eq37 && _equalsHelper(this.positiveSuffixPattern, other.positiveSuffixPattern);
        boolean eq39 = eq38 && _equalsHelper(this.roundingIncrement, other.roundingIncrement);
        boolean eq40 = eq39 && _equalsHelper(this.roundingMode, other.roundingMode);
        boolean eq41 = eq40 && _equalsHelper(this.secondaryGroupingSize, other.secondaryGroupingSize);
        boolean eq42 = eq41 && _equalsHelper(this.signAlwaysShown, other.signAlwaysShown);
        return eq42;
    }

    private boolean _equalsHelper(boolean mine, boolean theirs) {
        return mine == theirs;
    }

    private boolean _equalsHelper(int mine, int theirs) {
        return mine == theirs;
    }

    private boolean _equalsHelper(Object mine, Object theirs) {
        if (mine == theirs) {
            return true;
        }
        if (mine == null) {
            return false;
        }
        return mine.equals(theirs);
    }

    private int _hashCode() {
        int hashCode = 0 ^ _hashCodeHelper(this.compactCustomData);
        return ((((((((((((((((((((((((((((((((((((((((hashCode ^ _hashCodeHelper(this.compactStyle)) ^ _hashCodeHelper(this.currency)) ^ _hashCodeHelper(this.currencyPluralInfo)) ^ _hashCodeHelper(this.currencyUsage)) ^ _hashCodeHelper(this.decimalPatternMatchRequired)) ^ _hashCodeHelper(this.decimalSeparatorAlwaysShown)) ^ _hashCodeHelper(this.exponentSignAlwaysShown)) ^ _hashCodeHelper(this.formatWidth)) ^ _hashCodeHelper(this.groupingSize)) ^ _hashCodeHelper(this.groupingUsed)) ^ _hashCodeHelper(this.magnitudeMultiplier)) ^ _hashCodeHelper(this.mathContext)) ^ _hashCodeHelper(this.maximumFractionDigits)) ^ _hashCodeHelper(this.maximumIntegerDigits)) ^ _hashCodeHelper(this.maximumSignificantDigits)) ^ _hashCodeHelper(this.minimumExponentDigits)) ^ _hashCodeHelper(this.minimumFractionDigits)) ^ _hashCodeHelper(this.minimumGroupingDigits)) ^ _hashCodeHelper(this.minimumIntegerDigits)) ^ _hashCodeHelper(this.minimumSignificantDigits)) ^ _hashCodeHelper(this.multiplier)) ^ _hashCodeHelper(this.negativePrefix)) ^ _hashCodeHelper(this.negativePrefixPattern)) ^ _hashCodeHelper(this.negativeSuffix)) ^ _hashCodeHelper(this.negativeSuffixPattern)) ^ _hashCodeHelper(this.padPosition)) ^ _hashCodeHelper(this.padString)) ^ _hashCodeHelper(this.parseCaseSensitive)) ^ _hashCodeHelper(this.parseIntegerOnly)) ^ _hashCodeHelper(this.parseMode)) ^ _hashCodeHelper(this.parseNoExponent)) ^ _hashCodeHelper(this.parseToBigDecimal)) ^ _hashCodeHelper(this.pluralRules)) ^ _hashCodeHelper(this.positivePrefix)) ^ _hashCodeHelper(this.positivePrefixPattern)) ^ _hashCodeHelper(this.positiveSuffix)) ^ _hashCodeHelper(this.positiveSuffixPattern)) ^ _hashCodeHelper(this.roundingIncrement)) ^ _hashCodeHelper(this.roundingMode)) ^ _hashCodeHelper(this.secondaryGroupingSize)) ^ _hashCodeHelper(this.signAlwaysShown);
    }

    private int _hashCodeHelper(boolean z) {
        return z ? 1 : 0;
    }

    private int _hashCodeHelper(int value) {
        return value * 13;
    }

    private int _hashCodeHelper(Object value) {
        if (value == null) {
            return 0;
        }
        return value.hashCode();
    }

    public DecimalFormatProperties clear() {
        return _clear();
    }

    /* renamed from: clone, reason: merged with bridge method [inline-methods] */
    public DecimalFormatProperties m35919clone() {
        try {
            return (DecimalFormatProperties) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new UnsupportedOperationException(e);
        }
    }

    public DecimalFormatProperties copyFrom(DecimalFormatProperties other) {
        return _copyFrom(other);
    }

    public boolean equals(Object other) {
        if (other == null) {
            return false;
        }
        if (this == other) {
            return true;
        }
        if (!(other instanceof DecimalFormatProperties)) {
            return false;
        }
        return _equals((DecimalFormatProperties) other);
    }

    public Map<String, Map<String, String>> getCompactCustomData() {
        return this.compactCustomData;
    }

    public CompactDecimalFormat.CompactStyle getCompactStyle() {
        return this.compactStyle;
    }

    public Currency getCurrency() {
        return this.currency;
    }

    public CurrencyPluralInfo getCurrencyPluralInfo() {
        return this.currencyPluralInfo;
    }

    public Currency.CurrencyUsage getCurrencyUsage() {
        return this.currencyUsage;
    }

    public boolean getDecimalPatternMatchRequired() {
        return this.decimalPatternMatchRequired;
    }

    public boolean getDecimalSeparatorAlwaysShown() {
        return this.decimalSeparatorAlwaysShown;
    }

    public boolean getExponentSignAlwaysShown() {
        return this.exponentSignAlwaysShown;
    }

    public int getFormatWidth() {
        return this.formatWidth;
    }

    public int getGroupingSize() {
        return this.groupingSize;
    }

    public boolean getGroupingUsed() {
        return this.groupingUsed;
    }

    public int getMagnitudeMultiplier() {
        return this.magnitudeMultiplier;
    }

    public MathContext getMathContext() {
        return this.mathContext;
    }

    public int getMaximumFractionDigits() {
        return this.maximumFractionDigits;
    }

    public int getMaximumIntegerDigits() {
        return this.maximumIntegerDigits;
    }

    public int getMaximumSignificantDigits() {
        return this.maximumSignificantDigits;
    }

    public int getMinimumExponentDigits() {
        return this.minimumExponentDigits;
    }

    public int getMinimumFractionDigits() {
        return this.minimumFractionDigits;
    }

    public int getMinimumGroupingDigits() {
        return this.minimumGroupingDigits;
    }

    public int getMinimumIntegerDigits() {
        return this.minimumIntegerDigits;
    }

    public int getMinimumSignificantDigits() {
        return this.minimumSignificantDigits;
    }

    public BigDecimal getMultiplier() {
        return this.multiplier;
    }

    public String getNegativePrefix() {
        return this.negativePrefix;
    }

    public String getNegativePrefixPattern() {
        return this.negativePrefixPattern;
    }

    public String getNegativeSuffix() {
        return this.negativeSuffix;
    }

    public String getNegativeSuffixPattern() {
        return this.negativeSuffixPattern;
    }

    public Padder.PadPosition getPadPosition() {
        return this.padPosition;
    }

    public String getPadString() {
        return this.padString;
    }

    public boolean getParseCaseSensitive() {
        return this.parseCaseSensitive;
    }

    public boolean getParseIntegerOnly() {
        return this.parseIntegerOnly;
    }

    public ParseMode getParseMode() {
        return this.parseMode;
    }

    public boolean getParseNoExponent() {
        return this.parseNoExponent;
    }

    public boolean getParseToBigDecimal() {
        return this.parseToBigDecimal;
    }

    public PluralRules getPluralRules() {
        return this.pluralRules;
    }

    public String getPositivePrefix() {
        return this.positivePrefix;
    }

    public String getPositivePrefixPattern() {
        return this.positivePrefixPattern;
    }

    public String getPositiveSuffix() {
        return this.positiveSuffix;
    }

    public String getPositiveSuffixPattern() {
        return this.positiveSuffixPattern;
    }

    public BigDecimal getRoundingIncrement() {
        return this.roundingIncrement;
    }

    public RoundingMode getRoundingMode() {
        return this.roundingMode;
    }

    public int getSecondaryGroupingSize() {
        return this.secondaryGroupingSize;
    }

    public boolean getSignAlwaysShown() {
        return this.signAlwaysShown;
    }

    public int hashCode() {
        return _hashCode();
    }

    private void readObject(ObjectInputStream ois) throws IOException, ClassNotFoundException {
        readObjectImpl(ois);
    }

    void readObjectImpl(ObjectInputStream ois) throws IOException, ClassNotFoundException {
        Field field;
        ois.defaultReadObject();
        clear();
        ois.readInt();
        int count = ois.readInt();
        for (int i = 0; i < count; i++) {
            String name = (String) ois.readObject();
            Object value = ois.readObject();
            try {
                field = DecimalFormatProperties.class.getDeclaredField(name);
            } catch (NoSuchFieldException e) {
            } catch (SecurityException e2) {
                throw new AssertionError(e2);
            }
            try {
                field.set(this, value);
            } catch (IllegalAccessException e3) {
                throw new AssertionError(e3);
            } catch (IllegalArgumentException e4) {
                throw new AssertionError(e4);
            }
        }
    }

    public DecimalFormatProperties setCompactCustomData(Map<String, Map<String, String>> compactCustomData) {
        this.compactCustomData = compactCustomData;
        return this;
    }

    public DecimalFormatProperties setCompactStyle(CompactDecimalFormat.CompactStyle compactStyle) {
        this.compactStyle = compactStyle;
        return this;
    }

    public DecimalFormatProperties setCurrency(Currency currency) {
        this.currency = currency;
        return this;
    }

    public DecimalFormatProperties setCurrencyPluralInfo(CurrencyPluralInfo currencyPluralInfo) {
        if (currencyPluralInfo != null) {
            currencyPluralInfo = (CurrencyPluralInfo) currencyPluralInfo.clone();
        }
        this.currencyPluralInfo = currencyPluralInfo;
        return this;
    }

    public DecimalFormatProperties setCurrencyUsage(Currency.CurrencyUsage currencyUsage) {
        this.currencyUsage = currencyUsage;
        return this;
    }

    public DecimalFormatProperties setDecimalPatternMatchRequired(boolean decimalPatternMatchRequired) {
        this.decimalPatternMatchRequired = decimalPatternMatchRequired;
        return this;
    }

    public DecimalFormatProperties setDecimalSeparatorAlwaysShown(boolean alwaysShowDecimal) {
        this.decimalSeparatorAlwaysShown = alwaysShowDecimal;
        return this;
    }

    public DecimalFormatProperties setExponentSignAlwaysShown(boolean exponentSignAlwaysShown) {
        this.exponentSignAlwaysShown = exponentSignAlwaysShown;
        return this;
    }

    public DecimalFormatProperties setFormatWidth(int paddingWidth) {
        this.formatWidth = paddingWidth;
        return this;
    }

    public DecimalFormatProperties setGroupingSize(int groupingSize) {
        this.groupingSize = groupingSize;
        return this;
    }

    public DecimalFormatProperties setGroupingUsed(boolean groupingUsed) {
        this.groupingUsed = groupingUsed;
        return this;
    }

    public DecimalFormatProperties setMagnitudeMultiplier(int magnitudeMultiplier) {
        this.magnitudeMultiplier = magnitudeMultiplier;
        return this;
    }

    public DecimalFormatProperties setMathContext(MathContext mathContext) {
        this.mathContext = mathContext;
        return this;
    }

    public DecimalFormatProperties setMaximumFractionDigits(int maximumFractionDigits) {
        this.maximumFractionDigits = maximumFractionDigits;
        return this;
    }

    public DecimalFormatProperties setMaximumIntegerDigits(int maximumIntegerDigits) {
        this.maximumIntegerDigits = maximumIntegerDigits;
        return this;
    }

    public DecimalFormatProperties setMaximumSignificantDigits(int maximumSignificantDigits) {
        this.maximumSignificantDigits = maximumSignificantDigits;
        return this;
    }

    public DecimalFormatProperties setMinimumExponentDigits(int minimumExponentDigits) {
        this.minimumExponentDigits = minimumExponentDigits;
        return this;
    }

    public DecimalFormatProperties setMinimumFractionDigits(int minimumFractionDigits) {
        this.minimumFractionDigits = minimumFractionDigits;
        return this;
    }

    public DecimalFormatProperties setMinimumGroupingDigits(int minimumGroupingDigits) {
        this.minimumGroupingDigits = minimumGroupingDigits;
        return this;
    }

    public DecimalFormatProperties setMinimumIntegerDigits(int minimumIntegerDigits) {
        this.minimumIntegerDigits = minimumIntegerDigits;
        return this;
    }

    public DecimalFormatProperties setMinimumSignificantDigits(int minimumSignificantDigits) {
        this.minimumSignificantDigits = minimumSignificantDigits;
        return this;
    }

    public DecimalFormatProperties setMultiplier(BigDecimal multiplier) {
        this.multiplier = multiplier;
        return this;
    }

    public DecimalFormatProperties setNegativePrefix(String negativePrefix) {
        this.negativePrefix = negativePrefix;
        return this;
    }

    public DecimalFormatProperties setNegativePrefixPattern(String negativePrefixPattern) {
        this.negativePrefixPattern = negativePrefixPattern;
        return this;
    }

    public DecimalFormatProperties setNegativeSuffix(String negativeSuffix) {
        this.negativeSuffix = negativeSuffix;
        return this;
    }

    public DecimalFormatProperties setNegativeSuffixPattern(String negativeSuffixPattern) {
        this.negativeSuffixPattern = negativeSuffixPattern;
        return this;
    }

    public DecimalFormatProperties setPadPosition(Padder.PadPosition paddingLocation) {
        this.padPosition = paddingLocation;
        return this;
    }

    public DecimalFormatProperties setPadString(String paddingString) {
        this.padString = paddingString;
        return this;
    }

    public DecimalFormatProperties setParseCaseSensitive(boolean parseCaseSensitive) {
        this.parseCaseSensitive = parseCaseSensitive;
        return this;
    }

    public DecimalFormatProperties setParseIntegerOnly(boolean parseIntegerOnly) {
        this.parseIntegerOnly = parseIntegerOnly;
        return this;
    }

    public DecimalFormatProperties setParseMode(ParseMode parseMode) {
        this.parseMode = parseMode;
        return this;
    }

    public DecimalFormatProperties setParseNoExponent(boolean parseNoExponent) {
        this.parseNoExponent = parseNoExponent;
        return this;
    }

    public DecimalFormatProperties setParseToBigDecimal(boolean parseToBigDecimal) {
        this.parseToBigDecimal = parseToBigDecimal;
        return this;
    }

    public DecimalFormatProperties setPluralRules(PluralRules pluralRules) {
        this.pluralRules = pluralRules;
        return this;
    }

    public DecimalFormatProperties setPositivePrefix(String positivePrefix) {
        this.positivePrefix = positivePrefix;
        return this;
    }

    public DecimalFormatProperties setPositivePrefixPattern(String positivePrefixPattern) {
        this.positivePrefixPattern = positivePrefixPattern;
        return this;
    }

    public DecimalFormatProperties setPositiveSuffix(String positiveSuffix) {
        this.positiveSuffix = positiveSuffix;
        return this;
    }

    public DecimalFormatProperties setPositiveSuffixPattern(String positiveSuffixPattern) {
        this.positiveSuffixPattern = positiveSuffixPattern;
        return this;
    }

    public DecimalFormatProperties setRoundingIncrement(BigDecimal roundingIncrement) {
        this.roundingIncrement = roundingIncrement;
        return this;
    }

    public DecimalFormatProperties setRoundingMode(RoundingMode roundingMode) {
        this.roundingMode = roundingMode;
        return this;
    }

    public DecimalFormatProperties setSecondaryGroupingSize(int secondaryGroupingSize) {
        this.secondaryGroupingSize = secondaryGroupingSize;
        return this;
    }

    public DecimalFormatProperties setSignAlwaysShown(boolean signAlwaysShown) {
        this.signAlwaysShown = signAlwaysShown;
        return this;
    }

    public String toString() {
        StringBuilder result = new StringBuilder();
        result.append("<Properties");
        toStringBare(result);
        result.append(">");
        return result.toString();
    }

    public void toStringBare(StringBuilder result) {
        Field[] fields = DecimalFormatProperties.class.getDeclaredFields();
        for (Field field : fields) {
            try {
                Object myValue = field.get(this);
                Object defaultValue = field.get(DEFAULT);
                if (myValue != null || defaultValue != null) {
                    if (myValue == null || defaultValue == null) {
                        result.append(" " + field.getName() + ":" + myValue);
                    } else if (!myValue.equals(defaultValue)) {
                        result.append(" " + field.getName() + ":" + myValue);
                    }
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (IllegalArgumentException e2) {
                e2.printStackTrace();
            }
        }
    }

    private void writeObject(ObjectOutputStream oos) throws IOException {
        writeObjectImpl(oos);
    }

    void writeObjectImpl(ObjectOutputStream oos) throws IOException {
        oos.defaultWriteObject();
        oos.writeInt(0);
        ArrayList<Field> fieldsToSerialize = new ArrayList<>();
        ArrayList<Object> valuesToSerialize = new ArrayList<>();
        Field[] fields = DecimalFormatProperties.class.getDeclaredFields();
        for (Field field : fields) {
            if (!java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                try {
                    Object myValue = field.get(this);
                    if (myValue != null) {
                        Object defaultValue = field.get(DEFAULT);
                        if (!myValue.equals(defaultValue)) {
                            fieldsToSerialize.add(field);
                            valuesToSerialize.add(myValue);
                        }
                    }
                } catch (IllegalAccessException e) {
                    throw new AssertionError(e);
                } catch (IllegalArgumentException e2) {
                    throw new AssertionError(e2);
                }
            }
        }
        int count = fieldsToSerialize.size();
        oos.writeInt(count);
        for (int i = 0; i < count; i++) {
            Field field2 = fieldsToSerialize.get(i);
            Object value = valuesToSerialize.get(i);
            oos.writeObject(field2.getName());
            oos.writeObject(value);
        }
    }
}
