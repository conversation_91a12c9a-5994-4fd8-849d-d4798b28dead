package aegon.chrome.base;

import aegon.chrome.base.EarlyTraceEvent;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class EarlyTraceEventJni implements EarlyTraceEvent.Natives {
    public static final JniStaticTestMocker<EarlyTraceEvent.Natives> TEST_HOOKS = new JniStaticTestMocker<EarlyTraceEvent.Natives>() { // from class: aegon.chrome.base.EarlyTraceEventJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(EarlyTraceEvent.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                EarlyTraceEvent.Natives unused = EarlyTraceEventJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static EarlyTraceEvent.Natives testInstance;

    EarlyTraceEventJni() {
    }

    @Override // aegon.chrome.base.EarlyTraceEvent.Natives
    public void recordEarlyBeginEvent(String str, long j, int i, long j2) {
        GEN_JNI.org_chromium_base_EarlyTraceEvent_recordEarlyBeginEvent(str, j, i, j2);
    }

    @Override // aegon.chrome.base.EarlyTraceEvent.Natives
    public void recordEarlyEndEvent(String str, long j, int i, long j2) {
        GEN_JNI.org_chromium_base_EarlyTraceEvent_recordEarlyEndEvent(str, j, i, j2);
    }

    @Override // aegon.chrome.base.EarlyTraceEvent.Natives
    public void recordEarlyToplevelBeginEvent(String str, long j, int i, long j2) {
        GEN_JNI.org_chromium_base_EarlyTraceEvent_recordEarlyToplevelBeginEvent(str, j, i, j2);
    }

    @Override // aegon.chrome.base.EarlyTraceEvent.Natives
    public void recordEarlyToplevelEndEvent(String str, long j, int i, long j2) {
        GEN_JNI.org_chromium_base_EarlyTraceEvent_recordEarlyToplevelEndEvent(str, j, i, j2);
    }

    @Override // aegon.chrome.base.EarlyTraceEvent.Natives
    public void recordEarlyAsyncBeginEvent(String str, long j, long j2) {
        GEN_JNI.org_chromium_base_EarlyTraceEvent_recordEarlyAsyncBeginEvent(str, j, j2);
    }

    @Override // aegon.chrome.base.EarlyTraceEvent.Natives
    public void recordEarlyAsyncEndEvent(String str, long j, long j2) {
        GEN_JNI.org_chromium_base_EarlyTraceEvent_recordEarlyAsyncEndEvent(str, j, j2);
    }

    public static EarlyTraceEvent.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            EarlyTraceEvent.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.EarlyTraceEvent.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new EarlyTraceEventJni();
    }
}
