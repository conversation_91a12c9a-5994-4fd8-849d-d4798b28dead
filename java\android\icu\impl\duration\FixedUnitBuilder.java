package android.icu.impl.duration;

import android.icu.impl.duration.BasicPeriodBuilderFactory;

/* compiled from: BasicPeriodBuilderFactory.java */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
class FixedUnitBuilder extends PeriodBuilderImpl {
    private TimeUnit unit;

    public static FixedUnitBuilder get(TimeUnit unit, BasicPeriodBuilderFactory.Settings settingsToUse) {
        if (settingsToUse != null && (settingsToUse.effectiveSet() & (1 << unit.ordinal)) != 0) {
            return new FixedUnitBuilder(unit, settingsToUse);
        }
        return null;
    }

    FixedUnitBuilder(TimeUnit unit, BasicPeriodBuilderFactory.Settings settings) {
        super(settings);
        this.unit = unit;
    }

    @Override // android.icu.impl.duration.PeriodBuilderImpl
    protected PeriodBuilder withSettings(BasicPeriodBuilderFactory.Settings settingsToUse) {
        return get(this.unit, settingsToUse);
    }

    @Override // android.icu.impl.duration.PeriodBuilderImpl
    protected Period handleCreate(long duration, long referenceDate, boolean inPast) {
        TimeUnit timeUnit = this.unit;
        if (timeUnit == null) {
            return null;
        }
        long unitDuration = approximateDurationOf(timeUnit);
        return Period.m89at((float) (duration / unitDuration), this.unit).inPast(inPast);
    }
}
