package android.arch.lifecycle;

import android.arch.lifecycle.Lifecycle;
import com.android.internal.util.Predicate;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes25.dex
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes31.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes37.dex */
class FullLifecycleObserverAdapter implements GenericLifecycleObserver {
    private final FullLifecycleObserver mObserver;

    private static void stub() {
        System.out.println(Predicate.class);
    }

    FullLifecycleObserverAdapter(FullLifecycleObserver fullLifecycleObserver) {
        this.mObserver = fullLifecycleObserver;
    }

    @Override // android.arch.lifecycle.GenericLifecycleObserver
    public void onStateChanged(LifecycleOwner lifecycleOwner, Lifecycle.Event event) {
        switch (event) {
            case ON_CREATE:
                this.mObserver.onCreate(lifecycleOwner);
                return;
            case ON_START:
                this.mObserver.onStart(lifecycleOwner);
                return;
            case ON_RESUME:
                this.mObserver.onResume(lifecycleOwner);
                return;
            case ON_PAUSE:
                this.mObserver.onPause(lifecycleOwner);
                return;
            case ON_STOP:
                this.mObserver.onStop(lifecycleOwner);
                return;
            case ON_DESTROY:
                this.mObserver.onDestroy(lifecycleOwner);
                return;
            case ON_ANY:
                throw new IllegalArgumentException("ON_ANY must not been send by anybody");
            default:
                return;
        }
    }
}
