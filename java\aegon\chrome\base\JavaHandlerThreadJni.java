package aegon.chrome.base;

import aegon.chrome.base.JavaHandlerThread;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class JavaHandlerThreadJni implements JavaHandlerThread.Natives {
    public static final JniStaticTestMocker<JavaHandlerThread.Natives> TEST_HOOKS = new JniStaticTestMocker<JavaHandlerThread.Natives>() { // from class: aegon.chrome.base.JavaHandlerThreadJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(JavaHandlerThread.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                JavaHandlerThread.Natives unused = JavaHandlerThreadJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static JavaHandlerThread.Natives testInstance;

    JavaHandlerThreadJni() {
    }

    @Override // aegon.chrome.base.JavaHandlerThread.Natives
    public void initializeThread(long j, long j2) {
        GEN_JNI.org_chromium_base_JavaHandlerThread_initializeThread(j, j2);
    }

    @Override // aegon.chrome.base.JavaHandlerThread.Natives
    public void onLooperStopped(long j) {
        GEN_JNI.org_chromium_base_JavaHandlerThread_onLooperStopped(j);
    }

    public static JavaHandlerThread.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            JavaHandlerThread.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.JavaHandlerThread.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new JavaHandlerThreadJni();
    }
}
