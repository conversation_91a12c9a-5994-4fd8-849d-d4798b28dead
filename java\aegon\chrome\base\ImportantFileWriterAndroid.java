package aegon.chrome.base;

import aegon.chrome.base.annotations.JNINamespace;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class ImportantFileWriterAndroid {

    interface Natives {
        boolean writeFileAtomically(String str, byte[] bArr);
    }

    public static boolean writeFileAtomically(String str, byte[] bArr) {
        return ImportantFileWriterAndroidJni.get().writeFileAtomically(str, bArr);
    }
}
