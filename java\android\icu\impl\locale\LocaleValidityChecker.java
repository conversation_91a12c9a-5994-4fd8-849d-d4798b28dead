package android.icu.impl.locale;

import android.icu.impl.ValidIdentifiers;
import android.icu.impl.locale.KeyTypeData;
import android.icu.text.DateFormat;
import android.icu.util.IllformedLocaleException;
import android.icu.util.Output;
import android.icu.util.ULocale;
import com.getui.gtc.extension.distribution.gbd.p154f.C1957h;
import com.xiaomi.stat.C7892d;
import com.xiaomi.stat.MiStat;
import java.util.Arrays;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;
import org.apache.xalan.templates.Constants;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class LocaleValidityChecker {
    private final boolean allowsDeprecated;
    private final Set<ValidIdentifiers.Datasubtype> datasubtypes;
    static Pattern SEPARATOR = Pattern.compile("[-_]");
    private static final Pattern VALID_X = Pattern.compile("[a-zA-Z0-9]{2,8}(-[a-zA-Z0-9]{2,8})*");
    static final Set<String> REORDERING_INCLUDE = new HashSet(Arrays.asList("space", "punct", "symbol", MiStat.Param.CURRENCY, Constants.ATTRNAME_DIGIT, "others", DateFormat.SPECIFIC_TZ));
    static final Set<String> REORDERING_EXCLUDE = new HashSet(Arrays.asList("zinh", "zyyy"));
    static final Set<ValidIdentifiers.Datasubtype> REGULAR_ONLY = EnumSet.m35035of(ValidIdentifiers.Datasubtype.regular);

    public static class Where {
        public String codeFailure;
        public ValidIdentifiers.Datatype fieldFailure;

        public boolean set(ValidIdentifiers.Datatype datatype, String code) {
            this.fieldFailure = datatype;
            this.codeFailure = code;
            return false;
        }

        public String toString() {
            if (this.fieldFailure == null) {
                return "OK";
            }
            return "{" + ((Object) this.fieldFailure) + ", " + this.codeFailure + "}";
        }
    }

    public LocaleValidityChecker(Set<ValidIdentifiers.Datasubtype> datasubtypes) {
        this.datasubtypes = EnumSet.copyOf(datasubtypes);
        this.allowsDeprecated = datasubtypes.contains(ValidIdentifiers.Datasubtype.deprecated);
    }

    public LocaleValidityChecker(ValidIdentifiers.Datasubtype... datasubtypes) {
        EnumSet enumSetCopyOf = EnumSet.copyOf(Arrays.asList(datasubtypes));
        this.datasubtypes = enumSetCopyOf;
        this.allowsDeprecated = enumSetCopyOf.contains(ValidIdentifiers.Datasubtype.deprecated);
    }

    public Set<ValidIdentifiers.Datasubtype> getDatasubtypes() {
        return EnumSet.copyOf(this.datasubtypes);
    }

    public boolean isValid(ULocale locale, Where where) {
        where.set(null, null);
        String language = locale.getLanguage();
        String script = locale.getScript();
        String region = locale.getCountry();
        String variantString = locale.getVariant();
        Set<Character> extensionKeys = locale.getExtensionKeys();
        if (!isValid(ValidIdentifiers.Datatype.language, language, where)) {
            if (!language.equals("x")) {
                return false;
            }
            where.set(null, null);
            return true;
        }
        if (!isValid(ValidIdentifiers.Datatype.script, script, where) || !isValid(ValidIdentifiers.Datatype.region, region, where)) {
            return false;
        }
        if (!variantString.isEmpty()) {
            for (String variant : SEPARATOR.split(variantString)) {
                if (!isValid(ValidIdentifiers.Datatype.variant, variant, where)) {
                    return false;
                }
            }
        }
        for (Character c2 : extensionKeys) {
            try {
                ValidIdentifiers.Datatype datatype = ValidIdentifiers.Datatype.valueOf(((Object) c2) + "");
                int i = C02671.$SwitchMap$android$icu$impl$ValidIdentifiers$Datatype[datatype.ordinal()];
                if (i == 1) {
                    return true;
                }
                if (i == 2 || i == 3) {
                    if (!isValidU(locale, datatype, locale.getExtension(c2.charValue()), where)) {
                        return false;
                    }
                }
            } catch (Exception e) {
                return where.set(ValidIdentifiers.Datatype.illegal, ((Object) c2) + "");
            }
        }
        return true;
    }

    enum SpecialCase {
        normal,
        anything,
        reorder,
        codepoints,
        subdivision,
        rgKey;

        static SpecialCase get(String key) {
            if (key.equals("kr")) {
                return reorder;
            }
            if (key.equals("vt")) {
                return codepoints;
            }
            if (key.equals(C1957h.f6456s)) {
                return subdivision;
            }
            if (key.equals(C7892d.f22722d)) {
                return rgKey;
            }
            if (key.equals("x0")) {
                return anything;
            }
            return normal;
        }
    }

    private boolean isValidU(ULocale locale, ValidIdentifiers.Datatype datatype, String extensionString, Where where) {
        SpecialCase specialCase;
        KeyTypeData.ValueType valueType = null;
        SpecialCase specialCase2 = null;
        StringBuilder prefix = new StringBuilder();
        Set<String> seen = new HashSet<>();
        StringBuilder tBuffer = datatype == ValidIdentifiers.Datatype.t ? new StringBuilder() : null;
        String[] strArrSplit = SEPARATOR.split(extensionString);
        int length = strArrSplit.length;
        int i = 0;
        String key = "";
        int typeCount = 0;
        while (i < length) {
            String subtag = strArrSplit[i];
            String[] strArr = strArrSplit;
            if (subtag.length() == 2 && (tBuffer == null || subtag.charAt(1) <= '9')) {
                if (tBuffer != null) {
                    if (tBuffer.length() != 0 && !isValidLocale(tBuffer.toString(), where)) {
                        return false;
                    }
                    tBuffer = null;
                }
                key = KeyTypeData.toBcpKey(subtag);
                if (key == null) {
                    return where.set(datatype, subtag);
                }
                if (!this.allowsDeprecated && KeyTypeData.isDeprecated(key)) {
                    return where.set(datatype, key);
                }
                valueType = KeyTypeData.getValueType(key);
                SpecialCase specialCase3 = SpecialCase.get(key);
                typeCount = 0;
                specialCase = specialCase3;
            } else if (tBuffer != null) {
                if (tBuffer.length() != 0) {
                    tBuffer.append('-');
                }
                tBuffer.append(subtag);
                specialCase = specialCase2;
            } else {
                int typeCount2 = typeCount + 1;
                int i2 = C02671.$SwitchMap$android$icu$impl$locale$KeyTypeData$ValueType[valueType.ordinal()];
                KeyTypeData.ValueType valueType2 = valueType;
                if (i2 != 1) {
                    if (i2 == 2) {
                        if (typeCount2 == 1) {
                            prefix.setLength(0);
                            prefix.append(subtag);
                        } else {
                            prefix.append('-');
                            prefix.append(subtag);
                            subtag = prefix.toString();
                        }
                    } else if (i2 == 3 && typeCount2 == 1) {
                        seen.clear();
                    }
                } else if (typeCount2 > 1) {
                    return where.set(datatype, key + "-" + subtag);
                }
                int i3 = C02671.f80xdbfdffaa[specialCase2.ordinal()];
                if (i3 == 1) {
                    specialCase = specialCase2;
                } else if (i3 != 2) {
                    specialCase = specialCase2;
                    if (i3 == 3) {
                        boolean newlyAdded = seen.add(subtag.equals(DateFormat.SPECIFIC_TZ) ? "others" : subtag);
                        if (!newlyAdded || !isScriptReorder(subtag)) {
                            return where.set(datatype, key + "-" + subtag);
                        }
                    } else if (i3 != 4) {
                        if (i3 == 5) {
                            if (subtag.length() >= 6 && subtag.endsWith(DateFormat.SPECIFIC_TZ)) {
                                if (!isValid(ValidIdentifiers.Datatype.region, subtag.substring(0, subtag.length() - 4), where)) {
                                    return false;
                                }
                            } else {
                                return where.set(datatype, subtag);
                            }
                        } else {
                            Output<Boolean> isKnownKey = new Output<>();
                            Output<Boolean> isSpecialType = new Output<>();
                            String type = KeyTypeData.toBcpType(key, subtag, isKnownKey, isSpecialType);
                            if (type != null) {
                                if (!this.allowsDeprecated && KeyTypeData.isDeprecated(key, subtag)) {
                                    return where.set(datatype, key + "-" + subtag);
                                }
                            } else {
                                return where.set(datatype, key + "-" + subtag);
                            }
                        }
                    } else if (!isSubdivision(locale, subtag)) {
                        return where.set(datatype, key + "-" + subtag);
                    }
                } else {
                    specialCase = specialCase2;
                    try {
                        if (Integer.parseInt(subtag, 16) > 1114111) {
                            return where.set(datatype, key + "-" + subtag);
                        }
                    } catch (NumberFormatException e) {
                        return where.set(datatype, key + "-" + subtag);
                    }
                }
                typeCount = typeCount2;
                valueType = valueType2;
            }
            i++;
            strArrSplit = strArr;
            specialCase2 = specialCase;
        }
        if (tBuffer != null && tBuffer.length() != 0 && !isValidLocale(tBuffer.toString(), where)) {
            return false;
        }
        return true;
    }

    /* renamed from: android.icu.impl.locale.LocaleValidityChecker$1 */
    static /* synthetic */ class C02671 {
        static final /* synthetic */ int[] $SwitchMap$android$icu$impl$ValidIdentifiers$Datatype;
        static final /* synthetic */ int[] $SwitchMap$android$icu$impl$locale$KeyTypeData$ValueType;

        /* renamed from: $SwitchMap$android$icu$impl$locale$LocaleValidityChecker$SpecialCase */
        static final /* synthetic */ int[] f80xdbfdffaa;

        static {
            int[] iArr = new int[SpecialCase.values().length];
            f80xdbfdffaa = iArr;
            try {
                iArr[SpecialCase.anything.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                f80xdbfdffaa[SpecialCase.codepoints.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                f80xdbfdffaa[SpecialCase.reorder.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                f80xdbfdffaa[SpecialCase.subdivision.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                f80xdbfdffaa[SpecialCase.rgKey.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
            int[] iArr2 = new int[KeyTypeData.ValueType.values().length];
            $SwitchMap$android$icu$impl$locale$KeyTypeData$ValueType = iArr2;
            try {
                iArr2[KeyTypeData.ValueType.single.ordinal()] = 1;
            } catch (NoSuchFieldError e6) {
            }
            try {
                $SwitchMap$android$icu$impl$locale$KeyTypeData$ValueType[KeyTypeData.ValueType.incremental.ordinal()] = 2;
            } catch (NoSuchFieldError e7) {
            }
            try {
                $SwitchMap$android$icu$impl$locale$KeyTypeData$ValueType[KeyTypeData.ValueType.multiple.ordinal()] = 3;
            } catch (NoSuchFieldError e8) {
            }
            int[] iArr3 = new int[ValidIdentifiers.Datatype.values().length];
            $SwitchMap$android$icu$impl$ValidIdentifiers$Datatype = iArr3;
            try {
                iArr3[ValidIdentifiers.Datatype.x.ordinal()] = 1;
            } catch (NoSuchFieldError e9) {
            }
            try {
                $SwitchMap$android$icu$impl$ValidIdentifiers$Datatype[ValidIdentifiers.Datatype.t.ordinal()] = 2;
            } catch (NoSuchFieldError e10) {
            }
            try {
                $SwitchMap$android$icu$impl$ValidIdentifiers$Datatype[ValidIdentifiers.Datatype.u.ordinal()] = 3;
            } catch (NoSuchFieldError e11) {
            }
        }
    }

    private boolean isSubdivision(ULocale locale, String subtag) {
        if (subtag.length() < 3) {
            return false;
        }
        String region = subtag.substring(0, subtag.charAt(0) > '9' ? 2 : 3);
        String subdivision = subtag.substring(region.length());
        if (ValidIdentifiers.isValid(ValidIdentifiers.Datatype.subdivision, this.datasubtypes, region, subdivision) == null) {
            return false;
        }
        String localeRegion = locale.getCountry();
        if (localeRegion.isEmpty()) {
            ULocale max = ULocale.addLikelySubtags(locale);
            localeRegion = max.getCountry();
        }
        return region.equalsIgnoreCase(localeRegion);
    }

    private boolean isScriptReorder(String subtag) {
        String subtag2 = AsciiUtil.toLowerString(subtag);
        if (REORDERING_INCLUDE.contains(subtag2)) {
            return true;
        }
        return (REORDERING_EXCLUDE.contains(subtag2) || ValidIdentifiers.isValid(ValidIdentifiers.Datatype.script, REGULAR_ONLY, subtag2) == null) ? false : true;
    }

    private boolean isValidLocale(String extensionString, Where where) {
        try {
            ULocale locale = new ULocale.Builder().setLanguageTag(extensionString).build();
            return isValid(locale, where);
        } catch (IllformedLocaleException e) {
            int startIndex = e.getErrorIndex();
            String[] list = SEPARATOR.split(extensionString.substring(startIndex));
            return where.set(ValidIdentifiers.Datatype.t, list[0]);
        } catch (Exception e2) {
            return where.set(ValidIdentifiers.Datatype.t, e2.getMessage());
        }
    }

    private boolean isValid(ValidIdentifiers.Datatype datatype, String code, Where where) {
        if (code.isEmpty()) {
            return true;
        }
        if ((datatype == ValidIdentifiers.Datatype.variant && "posix".equalsIgnoreCase(code)) || ValidIdentifiers.isValid(datatype, this.datasubtypes, code) != null) {
            return true;
        }
        if (where == null) {
            return false;
        }
        return where.set(datatype, code);
    }
}
