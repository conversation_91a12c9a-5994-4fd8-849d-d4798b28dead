package android.icu.impl.coll;

import android.icu.impl.Norm2AllModes;
import android.icu.impl.Normalizer2Impl;
import android.icu.impl.coll.CollationDataBuilder;
import android.icu.impl.coll.CollationRuleParser;
import android.icu.text.CanonicalIterator;
import android.icu.text.Normalizer2;
import android.icu.text.UnicodeSet;
import android.icu.text.UnicodeSetIterator;
import android.icu.util.ULocale;
import java.text.ParseException;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationBuilder extends CollationRuleParser.Sink {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final UnicodeSet COMPOSITES;
    private static final boolean DEBUG = false;
    private static final int HAS_BEFORE2 = 64;
    private static final int HAS_BEFORE3 = 32;
    private static final int IS_TAILORED = 8;
    private static final int MAX_INDEX = 1048575;
    private CollationTailoring base;
    private CollationData baseData;
    private CollationRootElements rootElements;
    private UnicodeSet optimizeSet = new UnicodeSet();
    private long[] ces = new long[31];
    private Normalizer2 nfd = Normalizer2.getNFDInstance();
    private Normalizer2 fcd = Norm2AllModes.getFCDNormalizer2();
    private Normalizer2Impl nfcImpl = Norm2AllModes.getNFCInstance().impl;
    private long variableTop = 0;
    private CollationDataBuilder dataBuilder = new CollationDataBuilder();
    private boolean fastLatinEnabled = true;
    private int cesLength = 0;
    private UVector32 rootPrimaryIndexes = new UVector32();
    private UVector64 nodes = new UVector64();

    static {
        UnicodeSet unicodeSet = new UnicodeSet("[:NFD_QC=N:]");
        COMPOSITES = unicodeSet;
        unicodeSet.remove(Normalizer2Impl.Hangul.HANGUL_BASE, Normalizer2Impl.Hangul.HANGUL_END);
    }

    private static final class BundleImporter implements CollationRuleParser.Importer {
        BundleImporter() {
        }

        @Override // android.icu.impl.coll.CollationRuleParser.Importer
        public String getRules(String localeID, String collationType) {
            return CollationLoader.loadRules(new ULocale(localeID), collationType);
        }
    }

    public CollationBuilder(CollationTailoring b2) {
        this.base = b2;
        this.baseData = b2.data;
        this.rootElements = new CollationRootElements(b2.data.rootElements);
        this.nfcImpl.ensureCanonIterData();
        this.dataBuilder.initForTailoring(this.baseData);
    }

    public CollationTailoring parseAndBuild(String ruleString) throws ParseException {
        if (this.baseData.rootElements == null) {
            throw new UnsupportedOperationException("missing root elements data, tailoring not supported");
        }
        CollationTailoring tailoring = new CollationTailoring(this.base.settings);
        CollationRuleParser parser = new CollationRuleParser(this.baseData);
        this.variableTop = ((CollationSettings) this.base.settings.readOnly()).variableTop;
        parser.setSink(this);
        parser.setImporter(new BundleImporter());
        CollationSettings ownedSettings = (CollationSettings) tailoring.settings.copyOnWrite();
        parser.parse(ruleString, ownedSettings);
        if (this.dataBuilder.hasMappings()) {
            makeTailoredCEs();
            closeOverComposites();
            finalizeCEs();
            this.optimizeSet.add(0, 127);
            this.optimizeSet.add(192, 255);
            this.optimizeSet.remove(Normalizer2Impl.Hangul.HANGUL_BASE, Normalizer2Impl.Hangul.HANGUL_END);
            this.dataBuilder.optimize(this.optimizeSet);
            tailoring.ensureOwnedData();
            if (this.fastLatinEnabled) {
                this.dataBuilder.enableFastLatin();
            }
            this.dataBuilder.build(tailoring.ownedData);
            this.dataBuilder = null;
        } else {
            tailoring.data = this.baseData;
        }
        ownedSettings.fastLatinOptions = CollationFastLatin.getOptions(tailoring.data, ownedSettings, ownedSettings.fastLatinPrimaries);
        tailoring.setRules(ruleString);
        tailoring.setVersion(this.base.version, 0);
        return tailoring;
    }

    @Override // android.icu.impl.coll.CollationRuleParser.Sink
    void addReset(int strength, CharSequence str) {
        int index;
        int previousWeight16;
        String str2;
        if (str.charAt(0) == 65534) {
            this.ces[0] = getSpecialResetPosition(str);
            this.cesLength = 1;
        } else {
            String nfdString = this.nfd.normalize(str);
            int cEs = this.dataBuilder.getCEs(nfdString, this.ces, 0);
            this.cesLength = cEs;
            if (cEs > 31) {
                throw new IllegalArgumentException("reset position maps to too many collation elements (more than 31)");
            }
        }
        if (strength == 15) {
            return;
        }
        int index2 = findOrInsertNodeForCEs(strength);
        long node = this.nodes.elementAti(index2);
        while (strengthFromNode(node) > strength) {
            index2 = previousIndexFromNode(node);
            node = this.nodes.elementAti(index2);
        }
        if (strengthFromNode(node) == strength && isTailoredNode(node)) {
            index = previousIndexFromNode(node);
        } else if (strength == 0) {
            long p = weight32FromNode(node);
            if (p == 0) {
                throw new UnsupportedOperationException("reset primary-before ignorable not possible");
            }
            if (p <= this.rootElements.getFirstPrimary()) {
                throw new UnsupportedOperationException("reset primary-before first non-ignorable not supported");
            }
            if (p == 4278321664L) {
                throw new UnsupportedOperationException("reset primary-before [first trailing] not supported");
            }
            index = findOrInsertNodeForPrimary(this.rootElements.getPrimaryBefore(p, this.baseData.isCompressiblePrimary(p)));
            while (true) {
                long node2 = this.nodes.elementAti(index);
                int nextIndex = nextIndexFromNode(node2);
                if (nextIndex == 0) {
                    break;
                } else {
                    index = nextIndex;
                }
            }
        } else {
            int index3 = findCommonNode(index2, 1);
            if (strength >= 2) {
                index3 = findCommonNode(index3, 2);
            }
            long node3 = this.nodes.elementAti(index3);
            if (strengthFromNode(node3) == strength) {
                int weight16 = weight16FromNode(node3);
                if (weight16 == 0) {
                    if (strength == 1) {
                        str2 = "reset secondary-before secondary ignorable not possible";
                    } else {
                        str2 = "reset tertiary-before completely ignorable not possible";
                    }
                    throw new UnsupportedOperationException(str2);
                }
                int weight162 = getWeight16Before(index3, node3, strength);
                int previousIndex = previousIndexFromNode(node3);
                int i = previousIndex;
                while (true) {
                    long node4 = this.nodes.elementAti(i);
                    int previousStrength = strengthFromNode(node4);
                    if (previousStrength < strength) {
                        previousWeight16 = 1280;
                        break;
                    } else if (previousStrength != strength || isTailoredNode(node4)) {
                        i = previousIndexFromNode(node4);
                    } else {
                        previousWeight16 = weight16FromNode(node4);
                        break;
                    }
                }
                if (previousWeight16 == weight162) {
                    index = previousIndex;
                } else {
                    long node5 = nodeFromWeight16(weight162) | nodeFromStrength(strength);
                    index = insertNodeBetween(previousIndex, index3, node5);
                }
            } else {
                int weight163 = getWeight16Before(index3, node3, strength);
                index = findOrInsertWeakNode(index3, weight163, strength);
            }
            strength = ceStrength(this.ces[this.cesLength - 1]);
        }
        this.ces[this.cesLength - 1] = tempCEFromIndexAndStrength(index, strength);
    }

    private int getWeight16Before(int index, long node, int level) {
        int t;
        int s;
        if (strengthFromNode(node) == 2) {
            t = weight16FromNode(node);
        } else {
            t = 1280;
        }
        while (strengthFromNode(node) > 1) {
            int index2 = previousIndexFromNode(node);
            node = this.nodes.elementAti(index2);
        }
        if (isTailoredNode(node)) {
            return 256;
        }
        if (strengthFromNode(node) == 1) {
            s = weight16FromNode(node);
        } else {
            s = 1280;
        }
        while (strengthFromNode(node) > 0) {
            int index3 = previousIndexFromNode(node);
            node = this.nodes.elementAti(index3);
        }
        if (isTailoredNode(node)) {
            return 256;
        }
        long p = weight32FromNode(node);
        if (level == 1) {
            int weight16 = this.rootElements.getSecondaryBefore(p, s);
            return weight16;
        }
        int weight162 = this.rootElements.getTertiaryBefore(p, s, t);
        return weight162;
    }

    private long getSpecialResetPosition(CharSequence str) {
        long ce;
        int strength;
        boolean isBoundary;
        int strength2;
        CollationRuleParser.Position pos = CollationRuleParser.POSITION_VALUES[str.charAt(1) - 10240];
        switch (C02621.$SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[pos.ordinal()]) {
            case 1:
                return 0L;
            case 2:
                return 0L;
            case 3:
                int index = nextIndexFromNode(this.nodes.elementAti(findOrInsertNodeForRootCE(0L, 2)));
                if (index != 0) {
                    long node = this.nodes.elementAti(index);
                    if (isTailoredNode(node) && strengthFromNode(node) == 2) {
                        return tempCEFromIndexAndStrength(index, 2);
                    }
                }
                return this.rootElements.getFirstTertiaryCE();
            case 4:
                long ce2 = this.rootElements.getLastTertiaryCE();
                ce = ce2;
                strength = 2;
                isBoundary = false;
                break;
            case 5:
                long node2 = this.nodes.elementAti(findOrInsertNodeForRootCE(0L, 1));
                while (true) {
                    int iNextIndexFromNode = nextIndexFromNode(node2);
                    int index2 = iNextIndexFromNode;
                    if (iNextIndexFromNode != 0 && (strength2 = strengthFromNode((node2 = this.nodes.elementAti(index2)))) >= 1) {
                        if (strength2 == 1) {
                            if (isTailoredNode(node2)) {
                                if (nodeHasBefore3(node2)) {
                                    index2 = nextIndexFromNode(this.nodes.elementAti(nextIndexFromNode(node2)));
                                }
                                return tempCEFromIndexAndStrength(index2, 1);
                            }
                        }
                    }
                }
                long ce3 = this.rootElements.getFirstSecondaryCE();
                strength = 1;
                isBoundary = false;
                ce = ce3;
                break;
            case 6:
                long ce4 = this.rootElements.getLastSecondaryCE();
                ce = ce4;
                strength = 1;
                isBoundary = false;
                break;
            case 7:
                long ce5 = this.rootElements.getFirstPrimaryCE();
                ce = ce5;
                strength = 0;
                isBoundary = true;
                break;
            case 8:
                long ce6 = this.rootElements.lastCEWithPrimaryBefore(this.variableTop + 1);
                ce = ce6;
                strength = 0;
                isBoundary = false;
                break;
            case 9:
                long ce7 = this.rootElements.firstCEWithPrimaryAtLeast(this.variableTop + 1);
                ce = ce7;
                strength = 0;
                isBoundary = true;
                break;
            case 10:
                long ce8 = this.rootElements.firstCEWithPrimaryAtLeast(this.baseData.getFirstPrimaryForGroup(17));
                ce = ce8;
                strength = 0;
                isBoundary = false;
                break;
            case 11:
                long ce9 = this.baseData.getSingleCE(19968);
                ce = ce9;
                strength = 0;
                isBoundary = false;
                break;
            case 12:
                throw new UnsupportedOperationException("reset to [last implicit] not supported");
            case 13:
                long ce10 = Collation.makeCE(4278321664L);
                ce = ce10;
                strength = 0;
                isBoundary = true;
                break;
            case 14:
                throw new IllegalArgumentException("LDML forbids tailoring to U+FFFF");
            default:
                return 0L;
        }
        int index3 = findOrInsertNodeForRootCE(ce, strength);
        long node3 = this.nodes.elementAti(index3);
        if ((pos.ordinal() & 1) == 0) {
            if (!nodeHasAnyBefore(node3) && isBoundary) {
                int iNextIndexFromNode2 = nextIndexFromNode(node3);
                index3 = iNextIndexFromNode2;
                if (iNextIndexFromNode2 != 0) {
                    node3 = this.nodes.elementAti(index3);
                    ce = tempCEFromIndexAndStrength(index3, strength);
                } else {
                    long p = ce >>> 32;
                    int pIndex = this.rootElements.findPrimary(p);
                    boolean isCompressible = this.baseData.isCompressiblePrimary(p);
                    ce = Collation.makeCE(this.rootElements.getPrimaryAfter(p, pIndex, isCompressible));
                    index3 = findOrInsertNodeForRootCE(ce, 0);
                    node3 = this.nodes.elementAti(index3);
                }
            }
            if (nodeHasAnyBefore(node3)) {
                if (nodeHasBefore2(node3)) {
                    index3 = nextIndexFromNode(this.nodes.elementAti(nextIndexFromNode(node3)));
                    node3 = this.nodes.elementAti(index3);
                }
                if (nodeHasBefore3(node3)) {
                    index3 = nextIndexFromNode(this.nodes.elementAti(nextIndexFromNode(node3)));
                }
                long ce11 = tempCEFromIndexAndStrength(index3, strength);
                return ce11;
            }
            return ce;
        }
        while (true) {
            int nextIndex = nextIndexFromNode(node3);
            if (nextIndex != 0) {
                long nextNode = this.nodes.elementAti(nextIndex);
                if (strengthFromNode(nextNode) >= strength) {
                    index3 = nextIndex;
                    node3 = nextNode;
                }
            }
        }
        if (isTailoredNode(node3)) {
            long ce12 = tempCEFromIndexAndStrength(index3, strength);
            return ce12;
        }
        return ce;
    }

    /* renamed from: android.icu.impl.coll.CollationBuilder$1 */
    static /* synthetic */ class C02621 {
        static final /* synthetic */ int[] $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position;

        static {
            int[] iArr = new int[CollationRuleParser.Position.values().length];
            $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position = iArr;
            try {
                iArr[CollationRuleParser.Position.FIRST_TERTIARY_IGNORABLE.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.LAST_TERTIARY_IGNORABLE.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.FIRST_SECONDARY_IGNORABLE.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.LAST_SECONDARY_IGNORABLE.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.FIRST_PRIMARY_IGNORABLE.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.LAST_PRIMARY_IGNORABLE.ordinal()] = 6;
            } catch (NoSuchFieldError e6) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.FIRST_VARIABLE.ordinal()] = 7;
            } catch (NoSuchFieldError e7) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.LAST_VARIABLE.ordinal()] = 8;
            } catch (NoSuchFieldError e8) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.FIRST_REGULAR.ordinal()] = 9;
            } catch (NoSuchFieldError e9) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.LAST_REGULAR.ordinal()] = 10;
            } catch (NoSuchFieldError e10) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.FIRST_IMPLICIT.ordinal()] = 11;
            } catch (NoSuchFieldError e11) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.LAST_IMPLICIT.ordinal()] = 12;
            } catch (NoSuchFieldError e12) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.FIRST_TRAILING.ordinal()] = 13;
            } catch (NoSuchFieldError e13) {
            }
            try {
                $SwitchMap$android$icu$impl$coll$CollationRuleParser$Position[CollationRuleParser.Position.LAST_TRAILING.ordinal()] = 14;
            } catch (NoSuchFieldError e14) {
            }
        }
    }

    @Override // android.icu.impl.coll.CollationRuleParser.Sink
    void addRelation(int strength, CharSequence prefix, CharSequence str, CharSequence extension) {
        String nfdPrefix;
        if (prefix.length() == 0) {
            nfdPrefix = "";
        } else {
            nfdPrefix = this.nfd.normalize(prefix);
        }
        String nfdString = this.nfd.normalize(str);
        int nfdLength = nfdString.length();
        if (nfdLength >= 2) {
            char c2 = nfdString.charAt(0);
            if (Normalizer2Impl.Hangul.isJamoL(c2) || Normalizer2Impl.Hangul.isJamoV(c2)) {
                throw new UnsupportedOperationException("contractions starting with conjoining Jamo L or V not supported");
            }
            char c3 = nfdString.charAt(nfdLength - 1);
            if (Normalizer2Impl.Hangul.isJamoL(c3) || (Normalizer2Impl.Hangul.isJamoV(c3) && Normalizer2Impl.Hangul.isJamoL(nfdString.charAt(nfdLength - 2)))) {
                throw new UnsupportedOperationException("contractions ending with conjoining Jamo L or L+V not supported");
            }
        }
        if (strength != 15) {
            int index = findOrInsertNodeForCEs(strength);
            long ce = this.ces[this.cesLength - 1];
            if (strength == 0 && !isTempCE(ce) && (ce >>> 32) == 0) {
                throw new UnsupportedOperationException("tailoring primary after ignorables not supported");
            }
            if (strength == 3 && ce == 0) {
                throw new UnsupportedOperationException("tailoring quaternary after tertiary ignorables not supported");
            }
            int index2 = insertTailoredNodeAfter(index, strength);
            int tempStrength = ceStrength(ce);
            if (strength < tempStrength) {
                tempStrength = strength;
            }
            this.ces[this.cesLength - 1] = tempCEFromIndexAndStrength(index2, tempStrength);
        }
        setCaseBits(nfdString);
        int cesLengthBeforeExtension = this.cesLength;
        if (extension.length() != 0) {
            String nfdExtension = this.nfd.normalize(extension);
            int cEs = this.dataBuilder.getCEs(nfdExtension, this.ces, this.cesLength);
            this.cesLength = cEs;
            if (cEs > 31) {
                throw new IllegalArgumentException("extension string adds too many collation elements (more than 31 total)");
            }
        }
        int ce32 = -1;
        if ((!nfdPrefix.contentEquals(prefix) || !nfdString.contentEquals(str)) && !ignorePrefix(prefix) && !ignoreString(str)) {
            ce32 = addIfDifferent(prefix, str, this.ces, this.cesLength, -1);
        }
        addWithClosure(nfdPrefix, nfdString, this.ces, this.cesLength, ce32);
        this.cesLength = cesLengthBeforeExtension;
    }

    private int findOrInsertNodeForCEs(int strength) {
        long ce;
        while (true) {
            int i = this.cesLength;
            if (i == 0) {
                this.ces[0] = 0;
                ce = 0;
                this.cesLength = 1;
                break;
            }
            ce = this.ces[i - 1];
            if (ceStrength(ce) <= strength) {
                break;
            }
            this.cesLength--;
        }
        if (isTempCE(ce)) {
            return indexFromTempCE(ce);
        }
        if (((int) (ce >>> 56)) == 254) {
            throw new UnsupportedOperationException("tailoring relative to an unassigned code point not supported");
        }
        return findOrInsertNodeForRootCE(ce, strength);
    }

    private int findOrInsertNodeForRootCE(long ce, int strength) {
        int index = findOrInsertNodeForPrimary(ce >>> 32);
        if (strength >= 1) {
            int lower32 = (int) ce;
            int index2 = findOrInsertWeakNode(index, lower32 >>> 16, 1);
            if (strength >= 2) {
                return findOrInsertWeakNode(index2, lower32 & Collation.ONLY_TERTIARY_MASK, 2);
            }
            return index2;
        }
        return index;
    }

    private static final int binarySearchForRootPrimaryNode(int[] rootPrimaryIndexes, int length, long[] nodes, long p) {
        if (length == 0) {
            return -1;
        }
        int start = 0;
        int limit = length;
        while (true) {
            int i = (int) ((start + limit) / 2);
            long node = nodes[rootPrimaryIndexes[i]];
            long nodePrimary = node >>> 32;
            if (p == nodePrimary) {
                return i;
            }
            if (p < nodePrimary) {
                if (i == start) {
                    return ~start;
                }
                limit = i;
            } else {
                if (i == start) {
                    return ~(start + 1);
                }
                start = i;
            }
        }
    }

    private int findOrInsertNodeForPrimary(long p) {
        int rootIndex = binarySearchForRootPrimaryNode(this.rootPrimaryIndexes.getBuffer(), this.rootPrimaryIndexes.size(), this.nodes.getBuffer(), p);
        if (rootIndex >= 0) {
            return this.rootPrimaryIndexes.elementAti(rootIndex);
        }
        int index = this.nodes.size();
        this.nodes.addElement(nodeFromWeight32(p));
        this.rootPrimaryIndexes.insertElementAt(index, ~rootIndex);
        return index;
    }

    private int findOrInsertWeakNode(int index, int weight16, int level) {
        int nextIndex;
        if (weight16 == 1280) {
            return findCommonNode(index, level);
        }
        long node = this.nodes.elementAti(index);
        if (weight16 != 0 && weight16 < 1280) {
            int hasThisLevelBefore = level == 1 ? 64 : 32;
            if ((hasThisLevelBefore & node) == 0) {
                long commonNode = nodeFromWeight16(1280) | nodeFromStrength(level);
                if (level == 1) {
                    commonNode |= 32 & node;
                    node &= -33;
                }
                this.nodes.setElementAt(hasThisLevelBefore | node, index);
                int nextIndex2 = nextIndexFromNode(node);
                int index2 = insertNodeBetween(index, nextIndex2, nodeFromWeight16(weight16) | nodeFromStrength(level));
                insertNodeBetween(index2, nextIndex2, commonNode);
                return index2;
            }
        }
        while (true) {
            nextIndex = nextIndexFromNode(node);
            if (nextIndex == 0) {
                break;
            }
            node = this.nodes.elementAti(nextIndex);
            int nextStrength = strengthFromNode(node);
            if (nextStrength <= level) {
                if (nextStrength < level) {
                    break;
                }
                if (isTailoredNode(node)) {
                    continue;
                } else {
                    int nextWeight16 = weight16FromNode(node);
                    if (nextWeight16 == weight16) {
                        return nextIndex;
                    }
                    if (nextWeight16 > weight16) {
                        break;
                    }
                }
            }
            index = nextIndex;
        }
        return insertNodeBetween(index, nextIndex, nodeFromWeight16(weight16) | nodeFromStrength(level));
    }

    private int insertTailoredNodeAfter(int index, int strength) {
        int nextIndex;
        if (strength >= 1) {
            index = findCommonNode(index, 1);
            if (strength >= 2) {
                index = findCommonNode(index, 2);
            }
        }
        long node = this.nodes.elementAti(index);
        while (true) {
            nextIndex = nextIndexFromNode(node);
            if (nextIndex == 0) {
                break;
            }
            node = this.nodes.elementAti(nextIndex);
            if (strengthFromNode(node) <= strength) {
                break;
            }
            index = nextIndex;
        }
        long node2 = nodeFromStrength(strength) | 8;
        return insertNodeBetween(index, nextIndex, node2);
    }

    private int insertNodeBetween(int index, int nextIndex, long node) {
        int newIndex = this.nodes.size();
        this.nodes.addElement(node | nodeFromPreviousIndex(index) | nodeFromNextIndex(nextIndex));
        long node2 = this.nodes.elementAti(index);
        this.nodes.setElementAt(changeNodeNextIndex(node2, newIndex), index);
        if (nextIndex != 0) {
            long node3 = this.nodes.elementAti(nextIndex);
            this.nodes.setElementAt(changeNodePreviousIndex(node3, newIndex), nextIndex);
        }
        return newIndex;
    }

    private int findCommonNode(int index, int strength) {
        long node = this.nodes.elementAti(index);
        if (strengthFromNode(node) >= strength) {
            return index;
        }
        if (strength != 1 ? !nodeHasBefore3(node) : !nodeHasBefore2(node)) {
            return index;
        }
        long node2 = this.nodes.elementAti(nextIndexFromNode(node));
        while (true) {
            int index2 = nextIndexFromNode(node2);
            node2 = this.nodes.elementAti(index2);
            if (!isTailoredNode(node2) && strengthFromNode(node2) <= strength && weight16FromNode(node2) >= 1280) {
                return index2;
            }
        }
    }

    private void setCaseBits(CharSequence nfdString) {
        int numTailoredPrimaries = 0;
        for (int i = 0; i < this.cesLength; i++) {
            if (ceStrength(this.ces[i]) == 0) {
                numTailoredPrimaries++;
            }
        }
        long cases = 0;
        int i2 = 14;
        if (numTailoredPrimaries > 0) {
            UTF16CollationIterator baseCEs = new UTF16CollationIterator(this.baseData, false, nfdString, 0);
            int baseCEsLength = baseCEs.fetchCEs() - 1;
            int lastCase = 0;
            int numBasePrimaries = 0;
            int i3 = 0;
            while (true) {
                if (i3 >= baseCEsLength) {
                    break;
                }
                long ce = baseCEs.getCE(i3);
                if ((ce >>> 32) != 0) {
                    numBasePrimaries++;
                    int c2 = (((int) ce) >> i2) & 3;
                    if (numBasePrimaries < numTailoredPrimaries) {
                        cases |= c2 << ((numBasePrimaries - 1) * 2);
                    } else if (numBasePrimaries == numTailoredPrimaries) {
                        lastCase = c2;
                    } else if (c2 != lastCase) {
                        lastCase = 1;
                        break;
                    }
                }
                i3++;
                i2 = 14;
            }
            if (numBasePrimaries >= numTailoredPrimaries) {
                cases |= lastCase << ((numTailoredPrimaries - 1) * 2);
            }
        }
        for (int i4 = 0; i4 < this.cesLength; i4++) {
            long ce2 = this.ces[i4] & (-49153);
            int strength = ceStrength(ce2);
            if (strength == 0) {
                ce2 |= (3 & cases) << 14;
                cases >>>= 2;
            } else if (strength == 2) {
                ce2 |= 32768;
            }
            this.ces[i4] = ce2;
        }
    }

    @Override // android.icu.impl.coll.CollationRuleParser.Sink
    void suppressContractions(UnicodeSet set) {
        this.dataBuilder.suppressContractions(set);
    }

    @Override // android.icu.impl.coll.CollationRuleParser.Sink
    void optimize(UnicodeSet set) {
        this.optimizeSet.addAll(set);
    }

    private int addWithClosure(CharSequence nfdPrefix, CharSequence nfdString, long[] newCEs, int newCEsLength, int ce32) {
        int ce322 = addOnlyClosure(nfdPrefix, nfdString, newCEs, newCEsLength, addIfDifferent(nfdPrefix, nfdString, newCEs, newCEsLength, ce32));
        addTailComposites(nfdPrefix, nfdString);
        return ce322;
    }

    private int addOnlyClosure(CharSequence nfdPrefix, CharSequence nfdString, long[] newCEs, int newCEsLength, int ce32) {
        if (nfdPrefix.length() == 0) {
            CanonicalIterator stringIter = new CanonicalIterator(nfdString.toString());
            int ce322 = ce32;
            while (true) {
                String str = stringIter.next();
                if (str != null) {
                    if (!ignoreString(str) && !str.contentEquals(nfdString)) {
                        ce322 = addIfDifferent("", str, newCEs, newCEsLength, ce322);
                    }
                } else {
                    return ce322;
                }
            }
        } else {
            CanonicalIterator prefixIter = new CanonicalIterator(nfdPrefix.toString());
            CanonicalIterator stringIter2 = new CanonicalIterator(nfdString.toString());
            int ce323 = ce32;
            while (true) {
                String prefix = prefixIter.next();
                if (prefix == null) {
                    return ce323;
                }
                if (!ignorePrefix(prefix)) {
                    boolean samePrefix = prefix.contentEquals(nfdPrefix);
                    int ce324 = ce323;
                    while (true) {
                        String str2 = stringIter2.next();
                        if (str2 == null) {
                            break;
                        }
                        if (!ignoreString(str2) && (!samePrefix || !str2.contentEquals(nfdString))) {
                            ce324 = addIfDifferent(prefix, str2, newCEs, newCEsLength, ce324);
                        }
                    }
                    stringIter2.reset();
                    ce323 = ce324;
                }
            }
        }
    }

    private void addTailComposites(CharSequence nfdPrefix, CharSequence nfdString) {
        int newCEsLength;
        int ce32;
        int indexAfterLastStarter = nfdString.length();
        while (indexAfterLastStarter != 0) {
            int lastStarter = Character.codePointBefore(nfdString, indexAfterLastStarter);
            if (this.nfd.getCombiningClass(lastStarter) != 0) {
                indexAfterLastStarter -= Character.charCount(lastStarter);
            } else {
                if (Normalizer2Impl.Hangul.isJamoL(lastStarter)) {
                    return;
                }
                UnicodeSet composites = new UnicodeSet();
                if (this.nfcImpl.getCanonStartSet(lastStarter, composites)) {
                    StringBuilder newNFDString = new StringBuilder();
                    StringBuilder newString = new StringBuilder();
                    long[] newCEs = new long[31];
                    UnicodeSetIterator iter = new UnicodeSetIterator(composites);
                    while (iter.next()) {
                        int composite = iter.codepoint;
                        String decomp = this.nfd.getDecomposition(composite);
                        if (mergeCompositeIntoString(nfdString, indexAfterLastStarter, composite, decomp, newNFDString, newString) && (newCEsLength = this.dataBuilder.getCEs(nfdPrefix, newNFDString, newCEs, 0)) <= 31 && (ce32 = addIfDifferent(nfdPrefix, newString, newCEs, newCEsLength, -1)) != -1) {
                            addOnlyClosure(nfdPrefix, newNFDString, newCEs, newCEsLength, ce32);
                        }
                    }
                    return;
                }
                return;
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:16:0x0052, code lost:
    
        if (r12 < 0) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0054, code lost:
    
        if (r13 >= r14) goto L19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0056, code lost:
    
        return false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x0057, code lost:
    
        r21.append(r17, r10, r17.length());
        r22.append(r17, r10, r17.length());
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x006a, code lost:
    
        if (r11 >= r20.length()) goto L24;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x006c, code lost:
    
        r21.append(r20, r11, r20.length());
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0076, code lost:
    
        return r7;
     */
    /* JADX WARN: Removed duplicated region for block: B:25:0x0077  */
    /* JADX WARN: Removed duplicated region for block: B:38:0x0052 A[EDGE_INSN: B:38:0x0052->B:16:0x0052 BREAK  A[LOOP:0: B:9:0x0039->B:35:0x00a4], SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private boolean mergeCompositeIntoString(java.lang.CharSequence r17, int r18, int r19, java.lang.CharSequence r20, java.lang.StringBuilder r21, java.lang.StringBuilder r22) {
        /*
            r16 = this;
            r0 = r16
            r1 = r17
            r2 = r18
            r3 = r20
            r4 = r21
            r5 = r22
            r6 = 0
            r7 = 1
            int r8 = java.lang.Character.offsetByCodePoints(r3, r6, r7)
            int r9 = r20.length()
            if (r8 != r9) goto L19
            return r6
        L19:
            boolean r9 = r0.equalSubSequences(r1, r2, r3, r8)
            if (r9 == 0) goto L20
            return r6
        L20:
            r4.setLength(r6)
            r4.append(r1, r6, r2)
            r5.setLength(r6)
            int r9 = r2 - r8
            r5.append(r1, r6, r9)
            r9 = r19
            r5.appendCodePoint(r9)
            r10 = r18
            r11 = r8
            r12 = -1
            r13 = 0
            r14 = 0
        L39:
            if (r12 >= 0) goto L4c
            int r15 = r17.length()
            if (r10 < r15) goto L42
            goto L52
        L42:
            int r12 = java.lang.Character.codePointAt(r1, r10)
            android.icu.text.Normalizer2 r15 = r0.nfd
            int r13 = r15.getCombiningClass(r12)
        L4c:
            int r15 = r20.length()
            if (r11 < r15) goto L77
        L52:
            if (r12 < 0) goto L66
            if (r13 >= r14) goto L57
            return r6
        L57:
            int r6 = r17.length()
            r4.append(r1, r10, r6)
            int r6 = r17.length()
            r5.append(r1, r10, r6)
            goto L73
        L66:
            int r6 = r20.length()
            if (r11 >= r6) goto L73
            int r6 = r20.length()
            r4.append(r3, r11, r6)
        L73:
            return r7
        L77:
            int r15 = java.lang.Character.codePointAt(r3, r11)
            android.icu.text.Normalizer2 r7 = r0.nfd
            int r14 = r7.getCombiningClass(r15)
            if (r14 != 0) goto L84
            return r6
        L84:
            if (r13 >= r14) goto L87
            return r6
        L87:
            if (r14 >= r13) goto L92
            r4.appendCodePoint(r15)
            int r7 = java.lang.Character.charCount(r15)
            int r11 = r11 + r7
            goto La4
        L92:
            if (r15 == r12) goto L95
            return r6
        L95:
            r4.appendCodePoint(r15)
            int r7 = java.lang.Character.charCount(r15)
            int r11 = r11 + r7
            int r7 = java.lang.Character.charCount(r15)
            int r10 = r10 + r7
            r7 = -1
            r12 = r7
        La4:
            r7 = 1
            goto L39
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationBuilder.mergeCompositeIntoString(java.lang.CharSequence, int, int, java.lang.CharSequence, java.lang.StringBuilder, java.lang.StringBuilder):boolean");
    }

    private boolean equalSubSequences(CharSequence left, int leftStart, CharSequence right, int rightStart) {
        int leftLength = left.length();
        if (leftLength - leftStart != right.length() - rightStart) {
            return false;
        }
        while (leftStart < leftLength) {
            int leftStart2 = leftStart + 1;
            int rightStart2 = rightStart + 1;
            if (left.charAt(leftStart) != right.charAt(rightStart)) {
                return false;
            }
            leftStart = leftStart2;
            rightStart = rightStart2;
        }
        return true;
    }

    private boolean ignorePrefix(CharSequence s) {
        return !isFCD(s);
    }

    private boolean ignoreString(CharSequence s) {
        return !isFCD(s) || Normalizer2Impl.Hangul.isHangul(s.charAt(0));
    }

    private boolean isFCD(CharSequence s) {
        return this.fcd.isNormalized(s);
    }

    private void closeOverComposites() {
        UnicodeSetIterator iter = new UnicodeSetIterator(COMPOSITES);
        while (iter.next()) {
            String nfdString = this.nfd.getDecomposition(iter.codepoint);
            int cEs = this.dataBuilder.getCEs(nfdString, this.ces, 0);
            this.cesLength = cEs;
            if (cEs <= 31) {
                String composite = iter.getString();
                addIfDifferent("", composite, this.ces, this.cesLength, -1);
            }
        }
    }

    private int addIfDifferent(CharSequence prefix, CharSequence str, long[] newCEs, int newCEsLength, int ce32) {
        long[] oldCEs = new long[31];
        int oldCEsLength = this.dataBuilder.getCEs(prefix, str, oldCEs, 0);
        if (!sameCEs(newCEs, newCEsLength, oldCEs, oldCEsLength)) {
            if (ce32 == -1) {
                ce32 = this.dataBuilder.encodeCEs(newCEs, newCEsLength);
            }
            this.dataBuilder.addCE32(prefix, str, ce32);
        }
        return ce32;
    }

    private static boolean sameCEs(long[] ces1, int ces1Length, long[] ces2, int ces2Length) {
        if (ces1Length != ces2Length) {
            return false;
        }
        for (int i = 0; i < ces1Length; i++) {
            if (ces1[i] != ces2[i]) {
                return false;
            }
        }
        return true;
    }

    private static final int alignWeightRight(int w) {
        if (w != 0) {
            while ((w & 255) == 0) {
                w >>>= 8;
            }
        }
        return w;
    }

    private void makeTailoredCEs() {
        CollationWeights tertiaries;
        int i;
        int i2;
        long[] nodesArray;
        int rpi;
        int pIndex;
        int nextIndex;
        long p;
        int t;
        long p2;
        int pIndex2;
        int pIndex3;
        int sLimit;
        int s;
        int rpi2;
        int q;
        int pIndex4;
        int rpi3;
        CollationWeights tertiaries2;
        int t2;
        int t3;
        CollationWeights primaries = new CollationWeights();
        CollationWeights secondaries = new CollationWeights();
        CollationWeights tertiaries3 = new CollationWeights();
        long[] nodesArray2 = this.nodes.getBuffer();
        int rpi4 = 0;
        while (rpi4 < this.rootPrimaryIndexes.size()) {
            long node = nodesArray2[this.rootPrimaryIndexes.elementAti(rpi4)];
            long p3 = weight32FromNode(node);
            int s2 = p3 == 0 ? 0 : 1280;
            int t4 = s2;
            int pIndex5 = p3 == 0 ? 0 : this.rootElements.findPrimary(p3);
            int nextIndex2 = nextIndexFromNode(node);
            long p4 = p3;
            int q2 = 0;
            boolean sIsTailored = false;
            boolean tIsTailored = false;
            int s3 = s2;
            int t5 = t4;
            boolean tIsTailored2 = false;
            while (nextIndex2 != 0) {
                int i3 = nextIndex2;
                long node2 = nodesArray2[i3];
                int nextIndex3 = nextIndexFromNode(node2);
                int strength = strengthFromNode(node2);
                if (strength == 3) {
                    if (q2 == 3) {
                        throw new UnsupportedOperationException("quaternary tailoring gap too small");
                    }
                    q2++;
                    rpi = rpi4;
                    nodesArray = nodesArray2;
                    pIndex = pIndex5;
                    tertiaries = tertiaries3;
                    long j = p4;
                    nextIndex = nextIndex3;
                    p2 = j;
                } else {
                    if (strength == 2) {
                        if (isTailoredNode(node2)) {
                            if (tIsTailored) {
                                rpi2 = rpi4;
                                q = q2;
                                pIndex4 = pIndex5;
                                rpi3 = 0;
                            } else {
                                int tCount = countTailoredNodes(nodesArray2, nextIndex3, 2) + 1;
                                if (t5 == 0) {
                                    int t6 = this.rootElements.getTertiaryBoundary() - 256;
                                    tertiaries2 = tertiaries3;
                                    t3 = ((int) this.rootElements.getFirstTertiaryCE()) & Collation.ONLY_TERTIARY_MASK;
                                    t2 = t6;
                                } else {
                                    tertiaries2 = tertiaries3;
                                    if (!tIsTailored2 && !sIsTailored) {
                                        t2 = t5;
                                        t3 = this.rootElements.getTertiaryAfter(pIndex5, s3, t5);
                                    } else if (t5 == 256) {
                                        t2 = t5;
                                        t3 = 1280;
                                    } else {
                                        t2 = t5;
                                        t3 = this.rootElements.getTertiaryBoundary();
                                    }
                                }
                                tertiaries3 = tertiaries2;
                                tertiaries3.initForTertiary();
                                q = q2;
                                pIndex4 = pIndex5;
                                rpi2 = rpi4;
                                rpi3 = 0;
                                if (!tertiaries3.allocWeights(t2, t3, tCount)) {
                                    throw new UnsupportedOperationException("tertiary tailoring gap too small");
                                }
                                tIsTailored = true;
                            }
                            t = (int) tertiaries3.nextWeight();
                            tertiaries = tertiaries3;
                            pIndex = pIndex4;
                            rpi = rpi2;
                            nodesArray = nodesArray2;
                            long j2 = p4;
                            nextIndex = nextIndex3;
                            p2 = j2;
                        } else {
                            t = weight16FromNode(node2);
                            tIsTailored = false;
                            tertiaries = tertiaries3;
                            pIndex = pIndex5;
                            rpi = rpi4;
                            nodesArray = nodesArray2;
                            long j3 = p4;
                            nextIndex = nextIndex3;
                            p2 = j3;
                        }
                    } else {
                        int rpi5 = rpi4;
                        int pIndex6 = pIndex5;
                        if (strength == 1) {
                            if (isTailoredNode(node2)) {
                                if (sIsTailored) {
                                    tertiaries = tertiaries3;
                                    pIndex2 = pIndex6;
                                    i = 1280;
                                } else {
                                    int sCount = countTailoredNodes(nodesArray2, nextIndex3, 1) + 1;
                                    if (s3 == 0) {
                                        s3 = this.rootElements.getSecondaryBoundary() - 256;
                                        sLimit = (int) (this.rootElements.getFirstSecondaryCE() >> 16);
                                        pIndex3 = pIndex6;
                                    } else if (!tIsTailored2) {
                                        pIndex3 = pIndex6;
                                        sLimit = this.rootElements.getSecondaryAfter(pIndex3, s3);
                                    } else {
                                        pIndex3 = pIndex6;
                                        if (s3 == 256) {
                                            sLimit = 1280;
                                        } else {
                                            sLimit = this.rootElements.getSecondaryBoundary();
                                        }
                                    }
                                    if (s3 != 1280) {
                                        s = s3;
                                    } else {
                                        s = this.rootElements.getLastCommonSecondary();
                                    }
                                    secondaries.initForSecondary();
                                    tertiaries = tertiaries3;
                                    pIndex2 = pIndex3;
                                    i = 1280;
                                    if (!secondaries.allocWeights(s, sLimit, sCount)) {
                                        throw new UnsupportedOperationException("secondary tailoring gap too small");
                                    }
                                    sIsTailored = true;
                                }
                                s3 = (int) secondaries.nextWeight();
                                i2 = 0;
                                nodesArray = nodesArray2;
                                rpi = rpi5;
                                pIndex = pIndex2;
                                long j4 = p4;
                                nextIndex = nextIndex3;
                                p = j4;
                            } else {
                                tertiaries = tertiaries3;
                                i = 1280;
                                i2 = 0;
                                nodesArray = nodesArray2;
                                sIsTailored = false;
                                rpi = rpi5;
                                pIndex = pIndex6;
                                s3 = weight16FromNode(node2);
                                long j5 = p4;
                                nextIndex = nextIndex3;
                                p = j5;
                            }
                        } else {
                            tertiaries = tertiaries3;
                            i = 1280;
                            if (tIsTailored2) {
                                i2 = 0;
                                nodesArray = nodesArray2;
                                rpi = rpi5;
                                pIndex = pIndex6;
                                nextIndex = nextIndex3;
                            } else {
                                int pCount = countTailoredNodes(nodesArray2, nextIndex3, 0) + 1;
                                long p5 = p4;
                                boolean isCompressible = this.baseData.isCompressiblePrimary(p5);
                                pIndex = pIndex6;
                                long pLimit = this.rootElements.getPrimaryAfter(p5, pIndex, isCompressible);
                                primaries.initForPrimary(isCompressible);
                                nextIndex = nextIndex3;
                                i2 = 0;
                                rpi = rpi5;
                                nodesArray = nodesArray2;
                                if (!primaries.allocWeights(p5, pLimit, pCount)) {
                                    throw new UnsupportedOperationException("primary tailoring gap too small");
                                }
                                tIsTailored2 = true;
                            }
                            p = primaries.nextWeight();
                            s3 = 1280;
                            sIsTailored = false;
                        }
                        int t7 = s3 == 0 ? i2 : i;
                        tIsTailored = false;
                        long j6 = p;
                        t = t7;
                        p2 = j6;
                    }
                    t5 = t;
                    q2 = 0;
                }
                if (isTailoredNode(node2)) {
                    nodesArray[i3] = Collation.makeCE(p2, s3, t5, q2);
                }
                pIndex5 = pIndex;
                tertiaries3 = tertiaries;
                nextIndex2 = nextIndex;
                rpi4 = rpi;
                nodesArray2 = nodesArray;
                p4 = p2;
            }
            rpi4++;
        }
    }

    private static int countTailoredNodes(long[] nodesArray, int i, int strength) {
        int count = 0;
        while (i != 0) {
            long node = nodesArray[i];
            if (strengthFromNode(node) < strength) {
                break;
            }
            if (strengthFromNode(node) == strength) {
                if (!isTailoredNode(node)) {
                    break;
                }
                count++;
            }
            i = nextIndexFromNode(node);
        }
        return count;
    }

    private static final class CEFinalizer implements CollationDataBuilder.CEModifier {
        static final /* synthetic */ boolean $assertionsDisabled = false;
        private long[] finalCEs;

        CEFinalizer(long[] ces) {
            this.finalCEs = ces;
        }

        @Override // android.icu.impl.coll.CollationDataBuilder.CEModifier
        public long modifyCE32(int ce32) {
            if (CollationBuilder.isTempCE32(ce32)) {
                return this.finalCEs[CollationBuilder.indexFromTempCE32(ce32)] | ((ce32 & 192) << 8);
            }
            return Collation.NO_CE;
        }

        @Override // android.icu.impl.coll.CollationDataBuilder.CEModifier
        public long modifyCE(long ce) {
            if (CollationBuilder.isTempCE(ce)) {
                return this.finalCEs[CollationBuilder.indexFromTempCE(ce)] | (49152 & ce);
            }
            return Collation.NO_CE;
        }
    }

    private void finalizeCEs() {
        CollationDataBuilder newBuilder = new CollationDataBuilder();
        newBuilder.initForTailoring(this.baseData);
        CEFinalizer finalizer = new CEFinalizer(this.nodes.getBuffer());
        newBuilder.copyFrom(this.dataBuilder, finalizer);
        this.dataBuilder = newBuilder;
    }

    private static long tempCEFromIndexAndStrength(int index, int strength) {
        return ((1040384 & index) << 43) + 4629700417037541376L + ((index & 8128) << 42) + ((index & 63) << 24) + (strength << 8);
    }

    private static int indexFromTempCE(long tempCE) {
        long tempCE2 = tempCE - 4629700417037541376L;
        return (((int) (tempCE2 >> 43)) & 1040384) | (((int) (tempCE2 >> 42)) & 8128) | (((int) (tempCE2 >> 24)) & 63);
    }

    private static int strengthFromTempCE(long tempCE) {
        return (((int) tempCE) >> 8) & 3;
    }

    private static boolean isTempCE(long ce) {
        int sec = ((int) ce) >>> 24;
        return 6 <= sec && sec <= 69;
    }

    private static int indexFromTempCE32(int tempCE32) {
        int tempCE322 = tempCE32 - 1077937696;
        return ((tempCE322 >> 11) & 1040384) | ((tempCE322 >> 10) & 8128) | ((tempCE322 >> 8) & 63);
    }

    private static boolean isTempCE32(int ce32) {
        return (ce32 & 255) >= 2 && 6 <= ((ce32 >> 8) & 255) && ((ce32 >> 8) & 255) <= 69;
    }

    private static int ceStrength(long ce) {
        if (isTempCE(ce)) {
            return strengthFromTempCE(ce);
        }
        if (((-72057594037927936L) & ce) != 0) {
            return 0;
        }
        if ((((int) ce) & (-16777216)) != 0) {
            return 1;
        }
        return ce != 0 ? 2 : 15;
    }

    private static long nodeFromWeight32(long weight32) {
        return weight32 << 32;
    }

    private static long nodeFromWeight16(int weight16) {
        return weight16 << 48;
    }

    private static long nodeFromPreviousIndex(int previous) {
        return previous << 28;
    }

    private static long nodeFromNextIndex(int next) {
        return next << 8;
    }

    private static long nodeFromStrength(int strength) {
        return strength;
    }

    private static long weight32FromNode(long node) {
        return node >>> 32;
    }

    private static int weight16FromNode(long node) {
        return ((int) (node >> 48)) & 65535;
    }

    private static int previousIndexFromNode(long node) {
        return ((int) (node >> 28)) & MAX_INDEX;
    }

    private static int nextIndexFromNode(long node) {
        return (((int) node) >> 8) & MAX_INDEX;
    }

    private static int strengthFromNode(long node) {
        return ((int) node) & 3;
    }

    private static boolean nodeHasBefore2(long node) {
        return (64 & node) != 0;
    }

    private static boolean nodeHasBefore3(long node) {
        return (32 & node) != 0;
    }

    private static boolean nodeHasAnyBefore(long node) {
        return (96 & node) != 0;
    }

    private static boolean isTailoredNode(long node) {
        return (8 & node) != 0;
    }

    private static long changeNodePreviousIndex(long node, int previous) {
        return ((-281474708275201L) & node) | nodeFromPreviousIndex(previous);
    }

    private static long changeNodeNextIndex(long node, int next) {
        return ((-268435201) & node) | nodeFromNextIndex(next);
    }
}
