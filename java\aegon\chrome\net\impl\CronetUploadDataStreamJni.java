package aegon.chrome.net.impl;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.net.impl.CronetUploadDataStream;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class CronetUploadDataStreamJni implements CronetUploadDataStream.Natives {
    public static final JniStaticTestMocker<CronetUploadDataStream.Natives> TEST_HOOKS = new JniStaticTestMocker<CronetUploadDataStream.Natives>() { // from class: aegon.chrome.net.impl.CronetUploadDataStreamJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(CronetUploadDataStream.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                CronetUploadDataStream.Natives unused = CronetUploadDataStreamJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static CronetUploadDataStream.Natives testInstance;

    CronetUploadDataStreamJni() {
    }

    @Override // aegon.chrome.net.impl.CronetUploadDataStream.Natives
    public long attachUploadDataToRequest(CronetUploadDataStream cronetUploadDataStream, long j, long j2) {
        return GEN_JNI.m62xe897b21c(cronetUploadDataStream, j, j2);
    }

    @Override // aegon.chrome.net.impl.CronetUploadDataStream.Natives
    public long createAdapterForTesting(CronetUploadDataStream cronetUploadDataStream) {
        return GEN_JNI.m63x948d6832(cronetUploadDataStream);
    }

    @Override // aegon.chrome.net.impl.CronetUploadDataStream.Natives
    public long createUploadDataStreamForTesting(CronetUploadDataStream cronetUploadDataStream, long j, long j2) {
        return GEN_JNI.m64xfda3a116(cronetUploadDataStream, j, j2);
    }

    @Override // aegon.chrome.net.impl.CronetUploadDataStream.Natives
    public void onReadSucceeded(long j, CronetUploadDataStream cronetUploadDataStream, int i, boolean z) {
        GEN_JNI.org_chromium_net_impl_CronetUploadDataStream_onReadSucceeded(j, cronetUploadDataStream, i, z);
    }

    @Override // aegon.chrome.net.impl.CronetUploadDataStream.Natives
    public void onRewindSucceeded(long j, CronetUploadDataStream cronetUploadDataStream) {
        GEN_JNI.org_chromium_net_impl_CronetUploadDataStream_onRewindSucceeded(j, cronetUploadDataStream);
    }

    @Override // aegon.chrome.net.impl.CronetUploadDataStream.Natives
    public void destroy(long j) {
        GEN_JNI.org_chromium_net_impl_CronetUploadDataStream_destroy(j);
    }

    public static CronetUploadDataStream.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            CronetUploadDataStream.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.net.impl.CronetUploadDataStream.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new CronetUploadDataStreamJni();
    }
}
