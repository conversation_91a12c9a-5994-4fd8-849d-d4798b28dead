package android.icu.impl.number.parse;

import android.icu.number.Scale;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class MultiplierParseHandler extends ValidationMatcher {
    private final Scale multiplier;

    public MultiplierParseHandler(Scale multiplier) {
        this.multiplier = multiplier;
    }

    @Override // android.icu.impl.number.parse.NumberParseMatcher
    public void postProcess(ParsedNumber result) {
        if (result.quantity != null) {
            this.multiplier.applyReciprocalTo(result.quantity);
        }
    }

    public String toString() {
        return "<MultiplierHandler " + ((Object) this.multiplier) + ">";
    }
}
