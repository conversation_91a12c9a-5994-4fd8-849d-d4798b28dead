package aegon.chrome.base;

import java.p654io.FileInputStream;
import java.security.GeneralSecurityException;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class ByteArrayGenerator {
    public byte[] getBytes(int i) throws Throwable {
        FileInputStream fileInputStream;
        try {
            fileInputStream = new FileInputStream("/dev/urandom");
        } catch (Throwable th) {
            th = th;
            fileInputStream = null;
        }
        try {
            byte[] bArr = new byte[i];
            if (i != fileInputStream.read(bArr)) {
                throw new GeneralSecurityException("Not enough random data available");
            }
            fileInputStream.lambda$new$0();
            return bArr;
        } catch (Throwable th2) {
            th = th2;
            if (fileInputStream != null) {
                fileInputStream.lambda$new$0();
            }
            throw th;
        }
    }
}
