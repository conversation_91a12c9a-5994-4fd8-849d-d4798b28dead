package android.icu.impl.number.parse;

import android.icu.impl.StandardPlural;
import android.icu.impl.StringSegment;
import android.icu.impl.number.AffixPatternProvider;
import android.icu.impl.number.AffixUtils;
import android.icu.impl.number.PatternStringUtils;
import android.icu.number.NumberFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Objects;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class AffixMatcher implements NumberParseMatcher {
    public static final Comparator<AffixMatcher> COMPARATOR = new Comparator<AffixMatcher>() { // from class: android.icu.impl.number.parse.AffixMatcher.1
        @Override // java.util.Comparator
        public int compare(AffixMatcher lhs, AffixMatcher rhs) {
            if (AffixMatcher.length(lhs.prefix) != AffixMatcher.length(rhs.prefix)) {
                return AffixMatcher.length(lhs.prefix) > AffixMatcher.length(rhs.prefix) ? -1 : 1;
            }
            if (AffixMatcher.length(lhs.suffix) != AffixMatcher.length(rhs.suffix)) {
                return AffixMatcher.length(lhs.suffix) > AffixMatcher.length(rhs.suffix) ? -1 : 1;
            }
            if (lhs.equals(rhs)) {
                return 0;
            }
            return lhs.hashCode() > rhs.hashCode() ? -1 : 1;
        }
    };
    private final int flags;
    private final AffixPatternMatcher prefix;
    private final AffixPatternMatcher suffix;

    private static boolean isInteresting(AffixPatternProvider patternInfo, IgnorablesMatcher ignorables, int parseFlags) {
        String posPrefixString = patternInfo.getString(256);
        String posSuffixString = patternInfo.getString(0);
        String negPrefixString = null;
        String negSuffixString = null;
        if (patternInfo.hasNegativeSubpattern()) {
            negPrefixString = patternInfo.getString(768);
            negSuffixString = patternInfo.getString(512);
        }
        if ((parseFlags & 256) == 0 && AffixUtils.containsOnlySymbolsAndIgnorables(posPrefixString, ignorables.getSet()) && AffixUtils.containsOnlySymbolsAndIgnorables(posSuffixString, ignorables.getSet()) && AffixUtils.containsOnlySymbolsAndIgnorables(negPrefixString, ignorables.getSet()) && AffixUtils.containsOnlySymbolsAndIgnorables(negSuffixString, ignorables.getSet()) && !AffixUtils.containsType(posSuffixString, -2) && !AffixUtils.containsType(posSuffixString, -1) && !AffixUtils.containsType(negSuffixString, -2) && !AffixUtils.containsType(negSuffixString, -1)) {
            return false;
        }
        return true;
    }

    public static void createMatchers(AffixPatternProvider patternInfo, NumberParserImpl output, AffixTokenMatcherFactory factory, IgnorablesMatcher ignorables, int parseFlags) {
        NumberFormatter.SignDisplay signDisplay;
        AffixPatternMatcher posSuffix;
        ArrayList<AffixMatcher> matchers;
        if (!isInteresting(patternInfo, ignorables, parseFlags)) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        ArrayList<AffixMatcher> matchers2 = new ArrayList<>(6);
        boolean includeUnpaired = (parseFlags & 128) != 0;
        if ((parseFlags & 1024) != 0) {
            signDisplay = NumberFormatter.SignDisplay.ALWAYS;
        } else {
            signDisplay = NumberFormatter.SignDisplay.AUTO;
        }
        AffixPatternMatcher posPrefix = null;
        AffixPatternMatcher posSuffix2 = null;
        int signum = 1;
        while (signum >= -1) {
            PatternStringUtils.patternInfoToStringBuilder(patternInfo, true, signum, signDisplay, StandardPlural.OTHER, false, sb);
            AffixPatternMatcher prefix = AffixPatternMatcher.fromAffixPattern(sb.toString(), factory, parseFlags);
            int signum2 = signum;
            AffixPatternMatcher posSuffix3 = posSuffix2;
            AffixPatternMatcher posPrefix2 = posPrefix;
            ArrayList<AffixMatcher> matchers3 = matchers2;
            PatternStringUtils.patternInfoToStringBuilder(patternInfo, false, signum2, signDisplay, StandardPlural.OTHER, false, sb);
            AffixPatternMatcher suffix = AffixPatternMatcher.fromAffixPattern(sb.toString(), factory, parseFlags);
            if (signum2 == 1) {
                posPrefix = prefix;
                posSuffix2 = suffix;
            } else {
                if (Objects.equals(prefix, posPrefix2)) {
                    posSuffix = posSuffix3;
                    if (Objects.equals(suffix, posSuffix)) {
                        posPrefix = posPrefix2;
                        posSuffix2 = posSuffix;
                        matchers = matchers3;
                        signum = signum2 - 1;
                        matchers2 = matchers;
                    }
                } else {
                    posSuffix = posSuffix3;
                }
                posPrefix = posPrefix2;
                posSuffix2 = posSuffix;
            }
            int flags = signum2 == -1 ? 1 : 0;
            matchers = matchers3;
            matchers.add(getInstance(prefix, suffix, flags));
            if (includeUnpaired && prefix != null && suffix != null) {
                if (signum2 == 1 || !Objects.equals(prefix, posPrefix)) {
                    matchers.add(getInstance(prefix, null, flags));
                }
                if (signum2 == 1 || !Objects.equals(suffix, posSuffix2)) {
                    matchers.add(getInstance(null, suffix, flags));
                }
            }
            signum = signum2 - 1;
            matchers2 = matchers;
        }
        ArrayList<AffixMatcher> matchers4 = matchers2;
        Collections.sort(matchers4, COMPARATOR);
        output.addMatchers(matchers4);
    }

    private static final AffixMatcher getInstance(AffixPatternMatcher prefix, AffixPatternMatcher suffix, int flags) {
        return new AffixMatcher(prefix, suffix, flags);
    }

    private AffixMatcher(AffixPatternMatcher prefix, AffixPatternMatcher suffix, int flags) {
        this.prefix = prefix;
        this.suffix = suffix;
        this.flags = flags;
    }

    @Override // android.icu.impl.number.parse.NumberParseMatcher
    public boolean match(StringSegment segment, ParsedNumber result) {
        if (!result.seenNumber()) {
            if (result.prefix != null || this.prefix == null) {
                return false;
            }
            int initialOffset = segment.getOffset();
            boolean maybeMore = this.prefix.match(segment, result);
            if (initialOffset != segment.getOffset()) {
                result.prefix = this.prefix.getPattern();
            }
            return maybeMore;
        }
        if (result.suffix != null || this.suffix == null || !matched(this.prefix, result.prefix)) {
            return false;
        }
        int initialOffset2 = segment.getOffset();
        boolean maybeMore2 = this.suffix.match(segment, result);
        if (initialOffset2 != segment.getOffset()) {
            result.suffix = this.suffix.getPattern();
        }
        return maybeMore2;
    }

    @Override // android.icu.impl.number.parse.NumberParseMatcher
    public boolean smokeTest(StringSegment segment) {
        AffixPatternMatcher affixPatternMatcher;
        AffixPatternMatcher affixPatternMatcher2 = this.prefix;
        return (affixPatternMatcher2 != null && affixPatternMatcher2.smokeTest(segment)) || ((affixPatternMatcher = this.suffix) != null && affixPatternMatcher.smokeTest(segment));
    }

    @Override // android.icu.impl.number.parse.NumberParseMatcher
    public void postProcess(ParsedNumber result) {
        if (matched(this.prefix, result.prefix) && matched(this.suffix, result.suffix)) {
            if (result.prefix == null) {
                result.prefix = "";
            }
            if (result.suffix == null) {
                result.suffix = "";
            }
            result.flags |= this.flags;
            AffixPatternMatcher affixPatternMatcher = this.prefix;
            if (affixPatternMatcher != null) {
                affixPatternMatcher.postProcess(result);
            }
            AffixPatternMatcher affixPatternMatcher2 = this.suffix;
            if (affixPatternMatcher2 != null) {
                affixPatternMatcher2.postProcess(result);
            }
        }
    }

    static boolean matched(AffixPatternMatcher affix, String patternString) {
        return (affix == null && patternString == null) || (affix != null && affix.getPattern().equals(patternString));
    }

    private static int length(AffixPatternMatcher matcher) {
        if (matcher == null) {
            return 0;
        }
        return matcher.getPattern().length();
    }

    public boolean equals(Object _other) {
        if (!(_other instanceof AffixMatcher)) {
            return false;
        }
        AffixMatcher other = (AffixMatcher) _other;
        return Objects.equals(this.prefix, other.prefix) && Objects.equals(this.suffix, other.suffix) && this.flags == other.flags;
    }

    public int hashCode() {
        return (Objects.hashCode(this.prefix) ^ Objects.hashCode(this.suffix)) ^ this.flags;
    }

    public String toString() {
        boolean isNegative = (this.flags & 1) != 0;
        StringBuilder sb = new StringBuilder();
        sb.append("<AffixMatcher");
        sb.append(isNegative ? ":negative " : " ");
        sb.append((Object) this.prefix);
        sb.append("#");
        sb.append((Object) this.suffix);
        sb.append(">");
        return sb.toString();
    }
}
