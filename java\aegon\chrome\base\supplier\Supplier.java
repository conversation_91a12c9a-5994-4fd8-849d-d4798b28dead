package aegon.chrome.base.supplier;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public interface Supplier<T> {

    /* renamed from: aegon.chrome.base.supplier.Supplier$1 */
    static /* synthetic */ class C00471 {
        static final /* synthetic */ boolean $assertionsDisabled = false;
    }

    T get();

    boolean hasValue();

    /* renamed from: aegon.chrome.base.supplier.Supplier$-CC, reason: invalid class name */
    public final /* synthetic */ class CC {
        public static boolean $default$hasValue(Supplier _this) {
            Object obj = _this.get();
            if (C00471.$assertionsDisabled || obj == _this.get()) {
                return obj != null;
            }
            throw new AssertionError((Object) "Value provided by #get() must not change.");
        }
    }
}
