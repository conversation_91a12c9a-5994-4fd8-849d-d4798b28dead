package android.icu.impl.coll;

import android.icu.impl.ICUData;
import android.icu.impl.ICUResourceBundle;
import android.icu.util.ULocale;
import android.icu.util.UResourceBundle;
import java.util.MissingResourceException;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationLoader {
    private static volatile String rootRules = null;

    private CollationLoader() {
    }

    private static void loadRootRules() {
        if (rootRules != null) {
            return;
        }
        synchronized (CollationLoader.class) {
            if (rootRules == null) {
                UResourceBundle rootBundle = UResourceBundle.getBundleInstance(ICUData.ICU_COLLATION_BASE_NAME, ULocale.ROOT);
                rootRules = rootBundle.getString("UCARules");
            }
        }
    }

    public static String getRootRules() {
        loadRootRules();
        return rootRules;
    }

    private static final class ASCII {
        private ASCII() {
        }

        static String toLowerCase(String s) {
            int i = 0;
            while (i < s.length()) {
                char c2 = s.charAt(i);
                if ('A' > c2 || c2 > 'Z') {
                    i++;
                } else {
                    StringBuilder sb = new StringBuilder(s.length());
                    sb.append((CharSequence) s, 0, i);
                    sb.append((char) (c2 + ' '));
                    while (true) {
                        i++;
                        if (i < s.length()) {
                            char c3 = s.charAt(i);
                            if ('A' <= c3 && c3 <= 'Z') {
                                c3 = (char) (c3 + ' ');
                            }
                            sb.append(c3);
                        } else {
                            return sb.toString();
                        }
                    }
                }
            }
            return s;
        }
    }

    static String loadRules(ULocale locale, String collationType) throws MissingResourceException {
        UResourceBundle bundle = UResourceBundle.getBundleInstance(ICUData.ICU_COLLATION_BASE_NAME, locale);
        UResourceBundle data = ((ICUResourceBundle) bundle).getWithFallback("collations/" + ASCII.toLowerCase(collationType));
        String rules = data.getString("Sequence");
        return rules;
    }

    private static final UResourceBundle findWithFallback(UResourceBundle table, String entryName) {
        return ((ICUResourceBundle) table).findWithFallback(entryName);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:13:0x003d  */
    /* JADX WARN: Removed duplicated region for block: B:49:0x00bf  */
    /* JADX WARN: Type inference failed for: r0v1, types: [T, android.icu.util.ULocale] */
    /* JADX WARN: Type inference failed for: r0v18, types: [T, android.icu.util.ULocale] */
    /* JADX WARN: Type inference failed for: r1v1, types: [T, android.icu.util.ULocale] */
    /* JADX WARN: Type inference failed for: r7v4, types: [T, android.icu.util.ULocale, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r7v5 */
    /* JADX WARN: Type inference failed for: r7v6 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static android.icu.impl.coll.CollationTailoring loadTailoring(android.icu.util.ULocale r20, android.icu.util.Output<android.icu.util.ULocale> r21) {
        /*
            Method dump skipped, instructions count: 343
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationLoader.loadTailoring(android.icu.util.ULocale, android.icu.util.Output):android.icu.impl.coll.CollationTailoring");
    }
}
