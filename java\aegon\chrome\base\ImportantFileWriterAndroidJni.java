package aegon.chrome.base;

import aegon.chrome.base.ImportantFileWriterAndroid;
import aegon.chrome.base.natives.GEN_JNI;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class ImportantFileWriterAndroidJni implements ImportantFileWriterAndroid.Natives {
    public static final JniStaticTestMocker<ImportantFileWriterAndroid.Natives> TEST_HOOKS = new JniStaticTestMocker<ImportantFileWriterAndroid.Natives>() { // from class: aegon.chrome.base.ImportantFileWriterAndroidJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(ImportantFileWriterAndroid.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                ImportantFileWriterAndroid.Natives unused = ImportantFileWriterAndroidJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static ImportantFileWriterAndroid.Natives testInstance;

    ImportantFileWriterAndroidJni() {
    }

    @Override // aegon.chrome.base.ImportantFileWriterAndroid.Natives
    public boolean writeFileAtomically(String str, byte[] bArr) {
        return GEN_JNI.org_chromium_base_ImportantFileWriterAndroid_writeFileAtomically(str, bArr);
    }

    public static ImportantFileWriterAndroid.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            ImportantFileWriterAndroid.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.ImportantFileWriterAndroid.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new ImportantFileWriterAndroidJni();
    }
}
