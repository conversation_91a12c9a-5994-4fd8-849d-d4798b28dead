package android.icu.impl.number;

import android.icu.impl.FormattedStringBuilder;
import android.icu.impl.number.Modifier;
import java.text.Format;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class ConstantAffixModifier implements Modifier {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    public static final ConstantAffixModifier EMPTY = new ConstantAffixModifier();
    private final Format.Field field;
    private final String prefix;
    private final boolean strong;
    private final String suffix;

    public ConstantAffixModifier(String prefix, String suffix, Format.Field field, boolean strong) {
        this.prefix = prefix == null ? "" : prefix;
        this.suffix = suffix != null ? suffix : "";
        this.field = field;
        this.strong = strong;
    }

    public ConstantAffixModifier() {
        this.prefix = "";
        this.suffix = "";
        this.field = null;
        this.strong = false;
    }

    @Override // android.icu.impl.number.Modifier
    public int apply(FormattedStringBuilder output, int leftIndex, int rightIndex) {
        int length = output.insert(rightIndex, this.suffix, this.field);
        return length + output.insert(leftIndex, this.prefix, this.field);
    }

    @Override // android.icu.impl.number.Modifier
    public int getPrefixLength() {
        return this.prefix.length();
    }

    @Override // android.icu.impl.number.Modifier
    public int getCodePointCount() {
        String str = this.prefix;
        int iCodePointCount = str.codePointCount(0, str.length());
        String str2 = this.suffix;
        return iCodePointCount + str2.codePointCount(0, str2.length());
    }

    @Override // android.icu.impl.number.Modifier
    public boolean isStrong() {
        return this.strong;
    }

    @Override // android.icu.impl.number.Modifier
    public boolean containsField(Format.Field field) {
        return false;
    }

    @Override // android.icu.impl.number.Modifier
    public Modifier.Parameters getParameters() {
        return null;
    }

    @Override // android.icu.impl.number.Modifier
    public boolean semanticallyEquivalent(Modifier other) {
        if (!(other instanceof ConstantAffixModifier)) {
            return false;
        }
        ConstantAffixModifier _other = (ConstantAffixModifier) other;
        return this.prefix.equals(_other.prefix) && this.suffix.equals(_other.suffix) && this.field == _other.field && this.strong == _other.strong;
    }

    public String toString() {
        return String.format("<ConstantAffixModifier prefix:'%s' suffix:'%s'>", this.prefix, this.suffix);
    }
}
