package android.icu.impl.locale;

import android.icu.impl.ICUData;
import android.icu.impl.ICUResourceBundle;
import android.icu.impl.UResource;
import android.icu.util.BytesTrie;
import android.icu.util.ULocale;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.TreeMap;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class XLikelySubtags {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final boolean DEBUG_OUTPUT = false;
    public static final XLikelySubtags INSTANCE = new XLikelySubtags(Data.load());
    private static final String PSEUDO_ACCENTS_PREFIX = "'";
    private static final String PSEUDO_BIDI_PREFIX = "+";
    private static final String PSEUDO_CRACKED_PREFIX = ",";
    public static final int SKIP_SCRIPT = 1;
    private final int defaultLsrIndex;
    private final Map<String, String> languageAliases;
    private final LSR[] lsrs;
    private final Map<String, String> regionAliases;
    private final BytesTrie trie;
    private final long[] trieFirstLetterStates = new long[26];
    private final long trieUndState;
    private final long trieUndZzzzState;

    public static final class Data {
        public final Map<String, String> languageAliases;
        public final LSR[] lsrs;
        public final Map<String, String> regionAliases;
        public final byte[] trie;

        public Data(Map<String, String> languageAliases, Map<String, String> regionAliases, byte[] trie, LSR[] lsrs) {
            this.languageAliases = languageAliases;
            this.regionAliases = regionAliases;
            this.trie = trie;
            this.lsrs = lsrs;
        }

        private static UResource.Value getValue(UResource.Table table, String key, UResource.Value value) {
            if (!table.findValue(key, value)) {
                throw new MissingResourceException("langInfo.res missing data", "", "likely/" + key);
            }
            return value;
        }

        public static Data load() throws MissingResourceException {
            Map<String, String> languageAliases;
            Map<String, String> regionAliases;
            ICUResourceBundle langInfo = ICUResourceBundle.getBundleInstance(ICUData.ICU_BASE_NAME, "langInfo", ICUResourceBundle.ICU_DATA_CLASS_LOADER, ICUResourceBundle.OpenType.DIRECT);
            UResource.Value value = langInfo.getValueWithFallback("likely");
            UResource.Table likelyTable = value.getTable();
            if (likelyTable.findValue("languageAliases", value)) {
                String[] pairs = value.getStringArray();
                languageAliases = new HashMap<>(pairs.length / 2);
                for (int i = 0; i < pairs.length; i += 2) {
                    languageAliases.put(pairs[i], pairs[i + 1]);
                }
            } else {
                languageAliases = Collections.emptyMap();
            }
            if (likelyTable.findValue("regionAliases", value)) {
                String[] pairs2 = value.getStringArray();
                regionAliases = new HashMap<>(pairs2.length / 2);
                for (int i2 = 0; i2 < pairs2.length; i2 += 2) {
                    regionAliases.put(pairs2[i2], pairs2[i2 + 1]);
                }
            } else {
                regionAliases = Collections.emptyMap();
            }
            ByteBuffer buffer = getValue(likelyTable, "trie", value).getBinary();
            byte[] trie = new byte[buffer.remaining()];
            buffer.get(trie);
            String[] lsrSubtags = getValue(likelyTable, "lsrs", value).getStringArray();
            LSR[] lsrs = new LSR[lsrSubtags.length / 3];
            int i3 = 0;
            int j = 0;
            while (i3 < lsrSubtags.length) {
                lsrs[j] = new LSR(lsrSubtags[i3], lsrSubtags[i3 + 1], lsrSubtags[i3 + 2]);
                i3 += 3;
                j++;
            }
            return new Data(languageAliases, regionAliases, trie, lsrs);
        }

        public boolean equals(Object other) {
            if (this == other) {
                return true;
            }
            if (!getClass().equals(other.getClass())) {
                return false;
            }
            Data od = (Data) other;
            return this.languageAliases.equals(od.languageAliases) && this.regionAliases.equals(od.regionAliases) && Arrays.equals(this.trie, od.trie) && Arrays.equals(this.lsrs, od.lsrs);
        }
    }

    private XLikelySubtags(Data data) {
        this.languageAliases = data.languageAliases;
        this.regionAliases = data.regionAliases;
        this.trie = new BytesTrie(data.trie, 0);
        this.lsrs = data.lsrs;
        this.trie.next(42);
        this.trieUndState = this.trie.getState64();
        this.trie.next(42);
        this.trieUndZzzzState = this.trie.getState64();
        this.trie.next(42);
        this.defaultLsrIndex = this.trie.getValue();
        this.trie.reset();
        for (char c2 = 'a'; c2 <= 'z'; c2 = (char) (c2 + 1)) {
            BytesTrie.Result result = this.trie.next(c2);
            if (result == BytesTrie.Result.NO_VALUE) {
                this.trieFirstLetterStates[c2 - 'a'] = this.trie.getState64();
            }
            this.trie.reset();
        }
    }

    public ULocale canonicalize(ULocale locale) {
        String lang = locale.getLanguage();
        String lang2 = this.languageAliases.get(lang);
        String region = locale.getCountry();
        String region2 = this.regionAliases.get(region);
        if (lang2 != null || region2 != null) {
            return new ULocale(lang2 == null ? lang : lang2, locale.getScript(), region2 == null ? region : region2);
        }
        return locale;
    }

    private static String getCanonical(Map<String, String> aliases, String alias) {
        String canonical = aliases.get(alias);
        return canonical == null ? alias : canonical;
    }

    public LSR makeMaximizedLsrFrom(ULocale locale) {
        String name = locale.getName();
        if (name.startsWith("@x=")) {
            String tag = locale.toLanguageTag();
            return new LSR(tag, "", "");
        }
        String tag2 = locale.getLanguage();
        return makeMaximizedLsr(tag2, locale.getScript(), locale.getCountry(), locale.getVariant());
    }

    public LSR makeMaximizedLsrFrom(Locale locale) {
        String tag = locale.toLanguageTag();
        if (tag.startsWith("x-")) {
            return new LSR(tag, "", "");
        }
        return makeMaximizedLsr(locale.getLanguage(), locale.getScript(), locale.getCountry(), locale.getVariant());
    }

    /* JADX WARN: Removed duplicated region for block: B:33:0x00c5  */
    /* JADX WARN: Removed duplicated region for block: B:35:0x00c8  */
    /* JADX WARN: Removed duplicated region for block: B:50:0x012a  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private android.icu.impl.locale.LSR makeMaximizedLsr(java.lang.String r10, java.lang.String r11, java.lang.String r12, java.lang.String r13) {
        /*
            Method dump skipped, instructions count: 372
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.locale.XLikelySubtags.makeMaximizedLsr(java.lang.String, java.lang.String, java.lang.String, java.lang.String):android.icu.impl.locale.LSR");
    }

    /* JADX WARN: Removed duplicated region for block: B:28:0x0068  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private android.icu.impl.locale.LSR maximize(java.lang.String r13, java.lang.String r14, java.lang.String r15) {
        /*
            Method dump skipped, instructions count: 273
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.locale.XLikelySubtags.maximize(java.lang.String, java.lang.String, java.lang.String):android.icu.impl.locale.LSR");
    }

    private static final int trieNext(BytesTrie iter, String s, int i) {
        BytesTrie.Result result;
        if (s.isEmpty()) {
            result = iter.next(42);
        } else {
            int end = s.length() - 1;
            while (true) {
                int c2 = s.charAt(i);
                if (i < end) {
                    if (!iter.next(c2).hasNext()) {
                        return -1;
                    }
                    i++;
                } else {
                    BytesTrie.Result result2 = iter.next(c2 | 128);
                    result = result2;
                    break;
                }
            }
        }
        int i2 = C02691.$SwitchMap$android$icu$util$BytesTrie$Result[result.ordinal()];
        if (i2 == 2) {
            return 0;
        }
        if (i2 == 3) {
            return 1;
        }
        if (i2 != 4) {
            return -1;
        }
        return iter.getValue();
    }

    /* renamed from: android.icu.impl.locale.XLikelySubtags$1 */
    static /* synthetic */ class C02691 {
        static final /* synthetic */ int[] $SwitchMap$android$icu$util$BytesTrie$Result;

        static {
            int[] iArr = new int[BytesTrie.Result.values().length];
            $SwitchMap$android$icu$util$BytesTrie$Result = iArr;
            try {
                iArr[BytesTrie.Result.NO_MATCH.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$android$icu$util$BytesTrie$Result[BytesTrie.Result.NO_VALUE.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$android$icu$util$BytesTrie$Result[BytesTrie.Result.INTERMEDIATE_VALUE.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$android$icu$util$BytesTrie$Result[BytesTrie.Result.FINAL_VALUE.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    LSR minimizeSubtags(String languageIn, String scriptIn, String regionIn, ULocale.Minimize fieldToFavor) {
        LSR result = maximize(languageIn, scriptIn, regionIn);
        BytesTrie iter = new BytesTrie(this.trie);
        int value = trieNext(iter, result.language, 0);
        if (value == 0 && (value = trieNext(iter, "", 0)) == 0) {
            value = trieNext(iter, "", 0);
        }
        LSR value00 = this.lsrs[value];
        boolean favorRegionOk = false;
        if (result.script.equals(value00.script)) {
            if (result.region.equals(value00.region)) {
                return new LSR(result.language, "", "");
            }
            if (fieldToFavor == ULocale.Minimize.FAVOR_REGION) {
                return new LSR(result.language, "", result.region);
            }
            favorRegionOk = true;
        }
        LSR result2 = maximize(languageIn, scriptIn, "");
        if (result2.equals(result)) {
            return new LSR(result.language, result.script, "");
        }
        if (favorRegionOk) {
            return new LSR(result.language, "", result.region);
        }
        return result;
    }

    private Map<String, LSR> getTable() {
        Map<String, LSR> map = new TreeMap<>();
        StringBuilder sb = new StringBuilder();
        Iterator<BytesTrie.Entry> itIterator2 = this.trie.iterator2();
        while (itIterator2.hasNext()) {
            BytesTrie.Entry entry = itIterator2.mo35924next();
            sb.setLength(0);
            int length = entry.bytesLength();
            int i = 0;
            while (i < length) {
                int i2 = i + 1;
                byte b2 = entry.byteAt(i);
                if (b2 == 42) {
                    sb.append("*-");
                } else if (b2 >= 0) {
                    sb.append((char) b2);
                } else {
                    sb.append((char) (b2 & Byte.MAX_VALUE));
                    sb.append('-');
                }
                i = i2;
            }
            sb.setLength(sb.length() - 1);
            map.put(sb.toString(), this.lsrs[entry.value]);
        }
        return map;
    }

    public String toString() {
        return getTable().toString();
    }
}
