package aegon.chrome.base;

import java.util.concurrent.Callable;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class PathUtils$$Lambda$1 implements Callable {
    private static final PathUtils$$Lambda$1 instance = new PathUtils$$Lambda$1();

    private PathUtils$$Lambda$1() {
    }

    @Override // java.util.concurrent.Callable
    public final Object call() {
        return PathUtils.access$lambda$0();
    }
}
