package android.arch.lifecycle;

import android.arch.core.internal.SafeIterableMap;
import com.android.internal.util.Predicate;
import java.util.Iterator;
import java.util.Map;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes25.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes37.dex */
public class MediatorLiveData<T> extends MutableLiveData<T> {
    private SafeIterableMap<LiveData<?>, Source<?>> mSources = new SafeIterableMap<>();

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes25.dex
 */
    static class Source<V> implements Observer<V> {
        final LiveData<V> mLiveData;
        final Observer<V> mObserver;
        int mVersion = -1;

        private static void stub() {
            System.out.println(Predicate.class);
        }

        Source(LiveData<V> liveData, Observer<V> observer) {
            this.mLiveData = liveData;
            this.mObserver = observer;
        }

        void plug() {
            this.mLiveData.observeForever(this);
        }

        void unplug() {
            this.mLiveData.removeObserver(this);
        }

        @Override // android.arch.lifecycle.Observer
        public void onChanged(V v) {
            if (this.mVersion != this.mLiveData.getVersion()) {
                this.mVersion = this.mLiveData.getVersion();
                this.mObserver.onChanged(v);
            }
        }
    }

    private static void stub() {
        System.out.println(Predicate.class);
    }

    public <S> void addSource(LiveData<S> liveData, Observer<S> observer) {
        Source<?> source = new Source<>(liveData, observer);
        Source<?> sourcePutIfAbsent = this.mSources.putIfAbsent(liveData, source);
        if (sourcePutIfAbsent != null && sourcePutIfAbsent.mObserver != observer) {
            throw new IllegalArgumentException("This source was already added with the different observer");
        }
        if (sourcePutIfAbsent == null && hasActiveObservers()) {
            source.plug();
        }
    }

    public <S> void removeSource(LiveData<S> liveData) {
        Source<?> sourceRemove = this.mSources.remove(liveData);
        if (sourceRemove != null) {
            sourceRemove.unplug();
        }
    }

    @Override // android.arch.lifecycle.LiveData
    protected void onActive() {
        Iterator<Map.Entry<LiveData<?>, Source<?>>> itIterator2 = this.mSources.iterator2();
        while (itIterator2.hasNext()) {
            itIterator2.mo35924next().getValue().plug();
        }
    }

    @Override // android.arch.lifecycle.LiveData
    protected void onInactive() {
        Iterator<Map.Entry<LiveData<?>, Source<?>>> itIterator2 = this.mSources.iterator2();
        while (itIterator2.hasNext()) {
            itIterator2.mo35924next().getValue().unplug();
        }
    }
}
