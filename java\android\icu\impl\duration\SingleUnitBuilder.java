package android.icu.impl.duration;

import android.icu.impl.duration.BasicPeriodBuilderFactory;

/* compiled from: BasicPeriodBuilderFactory.java */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
class SingleUnitBuilder extends PeriodBuilderImpl {
    SingleUnitBuilder(BasicPeriodBuilderFactory.Settings settings) {
        super(settings);
    }

    public static SingleUnitBuilder get(BasicPeriodBuilderFactory.Settings settings) {
        if (settings == null) {
            return null;
        }
        return new SingleUnitBuilder(settings);
    }

    @Override // android.icu.impl.duration.PeriodBuilderImpl
    protected PeriodBuilder withSettings(BasicPeriodBuilderFactory.Settings settingsToUse) {
        return get(settingsToUse);
    }

    @Override // android.icu.impl.duration.PeriodBuilderImpl
    protected Period handleCreate(long duration, long referenceDate, boolean inPast) {
        short uset = this.settings.effectiveSet();
        for (int i = 0; i < TimeUnit.units.length; i++) {
            if (((1 << i) & uset) != 0) {
                TimeUnit unit = TimeUnit.units[i];
                long unitDuration = approximateDurationOf(unit);
                if (duration >= unitDuration) {
                    return Period.m89at((float) (duration / unitDuration), unit).inPast(inPast);
                }
            }
        }
        return null;
    }
}
