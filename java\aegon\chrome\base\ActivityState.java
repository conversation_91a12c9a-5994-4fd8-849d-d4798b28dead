package aegon.chrome.base;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@Retention(RetentionPolicy.SOURCE)
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public @interface ActivityState {
    public static final int CREATED = 1;
    public static final int DESTROYED = 6;
    public static final int PAUSED = 4;
    public static final int RESUMED = 3;
    public static final int STARTED = 2;
    public static final int STOPPED = 5;
}
