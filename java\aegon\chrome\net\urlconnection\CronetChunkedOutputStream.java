package aegon.chrome.net.urlconnection;

import aegon.chrome.net.UploadDataProvider;
import aegon.chrome.net.UploadDataSink;
import java.net.HttpRetryException;
import java.nio.ByteBuffer;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
final class CronetChunkedOutputStream extends CronetOutputStream {
    private final ByteBuffer mBuffer;
    private final CronetHttpURLConnection mConnection;
    private boolean mLastChunk;
    private final MessageLoop mMessageLoop;
    private final UploadDataProvider mUploadDataProvider = new UploadDataProviderImpl();

    @Override // aegon.chrome.net.urlconnection.CronetOutputStream
    final void checkReceivedEnoughContent() {
    }

    @Override // aegon.chrome.net.urlconnection.CronetOutputStream
    final void setConnected() {
    }

    CronetChunkedOutputStream(CronetHttpURLConnection cronetHttpURLConnection, int i, MessageLoop messageLoop) {
        if (cronetHttpURLConnection == null) {
            throw new NullPointerException();
        }
        if (i <= 0) {
            throw new IllegalArgumentException("chunkLength should be greater than 0");
        }
        this.mBuffer = ByteBuffer.allocate(i);
        this.mConnection = cronetHttpURLConnection;
        this.mMessageLoop = messageLoop;
    }

    @Override // java.p654io.OutputStream
    public final void write(int i) {
        ensureBufferHasRemaining();
        this.mBuffer.put((byte) i);
    }

    @Override // java.p654io.OutputStream
    public final void write(byte[] bArr, int i, int i2) {
        checkNotClosed();
        if (bArr.length - i < i2 || i < 0 || i2 < 0) {
            throw new IndexOutOfBoundsException();
        }
        int i3 = i2;
        while (i3 > 0) {
            int iMin = Math.min(i3, this.mBuffer.remaining());
            this.mBuffer.put(bArr, (i + i2) - i3, iMin);
            i3 -= iMin;
            ensureBufferHasRemaining();
        }
    }

    @Override // aegon.chrome.net.urlconnection.CronetOutputStream, java.p654io.OutputStream, java.p654io.Closeable, java.lang.AutoCloseable
    /* renamed from: close */
    public final void lambda$new$0() {
        super.lambda$new$0();
        if (this.mLastChunk) {
            return;
        }
        this.mLastChunk = true;
        this.mBuffer.flip();
    }

    @Override // aegon.chrome.net.urlconnection.CronetOutputStream
    final UploadDataProvider getUploadDataProvider() {
        return this.mUploadDataProvider;
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    class UploadDataProviderImpl extends UploadDataProvider {
        @Override // aegon.chrome.net.UploadDataProvider
        public long getLength() {
            return -1L;
        }

        private UploadDataProviderImpl() {
        }

        @Override // aegon.chrome.net.UploadDataProvider
        public void read(UploadDataSink uploadDataSink, ByteBuffer byteBuffer) {
            if (byteBuffer.remaining() >= CronetChunkedOutputStream.this.mBuffer.remaining()) {
                byteBuffer.put(CronetChunkedOutputStream.this.mBuffer);
                CronetChunkedOutputStream.this.mBuffer.clear();
                uploadDataSink.onReadSucceeded(CronetChunkedOutputStream.this.mLastChunk);
                if (CronetChunkedOutputStream.this.mLastChunk) {
                    return;
                }
                CronetChunkedOutputStream.this.mMessageLoop.quit();
                return;
            }
            int iLimit = CronetChunkedOutputStream.this.mBuffer.limit();
            CronetChunkedOutputStream.this.mBuffer.limit(CronetChunkedOutputStream.this.mBuffer.position() + byteBuffer.remaining());
            byteBuffer.put(CronetChunkedOutputStream.this.mBuffer);
            CronetChunkedOutputStream.this.mBuffer.limit(iLimit);
            uploadDataSink.onReadSucceeded(false);
        }

        @Override // aegon.chrome.net.UploadDataProvider
        public void rewind(UploadDataSink uploadDataSink) {
            uploadDataSink.onRewindError(new HttpRetryException("Cannot retry streamed Http body", -1));
        }
    }

    private void ensureBufferHasRemaining() {
        if (this.mBuffer.hasRemaining()) {
            return;
        }
        uploadBufferInternal();
    }

    private void uploadBufferInternal() {
        checkNotClosed();
        this.mBuffer.flip();
        this.mMessageLoop.loop();
        checkNoException();
    }
}
