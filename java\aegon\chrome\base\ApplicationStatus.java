package aegon.chrome.base;

import aegon.chrome.base.annotations.JNINamespace;
import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.view.Window;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class ApplicationStatus {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final String TOOLBAR_CALLBACK_WRAPPER_CLASS = "androidx.appcompat.app.ToolbarActionBar$ToolbarCallbackWrapper";
    private static Activity sActivity;
    private static ObserverList<ApplicationStateListener> sApplicationStateListeners;
    private static ObserverList<ActivityStateListener> sGeneralActivityStateListeners;
    private static ApplicationStateListener sNativeApplicationStateListener;
    private static ObserverList<WindowFocusChangedListener> sWindowFocusListeners;
    private static final Map<Activity, ActivityInfo> sActivityInfo = Collections.synchronizedMap(new HashMap());
    private static int sCurrentApplicationState = 0;

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    public interface ActivityStateListener {
        void onActivityStateChange(Activity activity, int i);
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    public interface ApplicationStateListener {
        void onApplicationStateChange(int i);
    }

    interface Natives {
        void onApplicationStateChange(int i);
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    public interface WindowFocusChangedListener {
        void onWindowFocusChanged(Activity activity, boolean z);
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static class ActivityInfo {
        private ObserverList<ActivityStateListener> mListeners;
        private int mStatus;

        private ActivityInfo() {
            this.mStatus = 6;
            this.mListeners = new ObserverList<>();
        }

        public int getStatus() {
            return this.mStatus;
        }

        public void setStatus(int i) {
            this.mStatus = i;
        }

        public ObserverList<ActivityStateListener> getListeners() {
            return this.mListeners;
        }
    }

    private ApplicationStatus() {
    }

    public static void registerWindowFocusChangedListener(WindowFocusChangedListener windowFocusChangedListener) {
        if (sWindowFocusListeners == null) {
            sWindowFocusListeners = new ObserverList<>();
        }
        sWindowFocusListeners.addObserver(windowFocusChangedListener);
    }

    public static void unregisterWindowFocusChangedListener(WindowFocusChangedListener windowFocusChangedListener) {
        ObserverList<WindowFocusChangedListener> observerList = sWindowFocusListeners;
        if (observerList == null) {
            return;
        }
        observerList.removeObserver(windowFocusChangedListener);
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static class WindowCallbackProxy implements InvocationHandler {
        private final Activity mActivity;
        private final Window.Callback mCallback;

        public WindowCallbackProxy(Activity activity, Window.Callback callback) {
            this.mCallback = callback;
            this.mActivity = activity;
        }

        @Override // java.lang.reflect.InvocationHandler
        public Object invoke(Object obj, Method method, Object[] objArr) throws Throwable {
            if (method.getName().equals("onWindowFocusChanged") && objArr.length == 1 && (objArr[0] instanceof Boolean)) {
                onWindowFocusChanged(((Boolean) objArr[0]).booleanValue());
                return null;
            }
            try {
                return method.invoke(this.mCallback, objArr);
            } catch (InvocationTargetException e) {
                if (e.getCause() instanceof AbstractMethodError) {
                    throw e.getCause();
                }
                throw e;
            }
        }

        public void onWindowFocusChanged(boolean z) {
            this.mCallback.onWindowFocusChanged(z);
            if (ApplicationStatus.sWindowFocusListeners != null) {
                Iterator it = ApplicationStatus.sWindowFocusListeners.iterator2();
                while (it.hasNext()) {
                    ((WindowFocusChangedListener) it.mo35924next()).onWindowFocusChanged(this.mActivity, z);
                }
            }
        }
    }

    public static boolean isInitialized() {
        boolean z;
        synchronized (sActivityInfo) {
            z = sCurrentApplicationState != 0;
        }
        return z;
    }

    public static void initialize(Application application) {
        synchronized (sActivityInfo) {
            sCurrentApplicationState = 4;
        }
        registerWindowFocusChangedListener(new WindowFocusChangedListener() { // from class: aegon.chrome.base.ApplicationStatus.1
            @Override // aegon.chrome.base.ApplicationStatus.WindowFocusChangedListener
            public void onWindowFocusChanged(Activity activity, boolean z) {
                int stateForActivity;
                if (!z || activity == ApplicationStatus.sActivity || (stateForActivity = ApplicationStatus.getStateForActivity(activity)) == 6 || stateForActivity == 5) {
                    return;
                }
                Activity unused = ApplicationStatus.sActivity = activity;
            }
        });
        application.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() { // from class: aegon.chrome.base.ApplicationStatus.2
            static final /* synthetic */ boolean $assertionsDisabled = false;

            @Override // android.app.Application.ActivityLifecycleCallbacks
            public void onActivityCreated(Activity activity, Bundle bundle) {
                ApplicationStatus.onStateChange(activity, 1);
                activity.getWindow().setCallback(ApplicationStatus.createWindowCallbackProxy(activity, activity.getWindow().getCallback()));
            }

            @Override // android.app.Application.ActivityLifecycleCallbacks
            public void onActivityDestroyed(Activity activity) {
                ApplicationStatus.onStateChange(activity, 6);
                checkCallback(activity);
            }

            @Override // android.app.Application.ActivityLifecycleCallbacks
            public void onActivityPaused(Activity activity) {
                ApplicationStatus.onStateChange(activity, 4);
                checkCallback(activity);
            }

            @Override // android.app.Application.ActivityLifecycleCallbacks
            public void onActivityResumed(Activity activity) {
                ApplicationStatus.onStateChange(activity, 3);
                checkCallback(activity);
            }

            @Override // android.app.Application.ActivityLifecycleCallbacks
            public void onActivitySaveInstanceState(Activity activity, Bundle bundle) {
                checkCallback(activity);
            }

            @Override // android.app.Application.ActivityLifecycleCallbacks
            public void onActivityStarted(Activity activity) {
                ApplicationStatus.onStateChange(activity, 2);
                checkCallback(activity);
            }

            @Override // android.app.Application.ActivityLifecycleCallbacks
            public void onActivityStopped(Activity activity) {
                ApplicationStatus.onStateChange(activity, 5);
                checkCallback(activity);
            }

            private void checkCallback(Activity activity) {
                boolean z = aegon.chrome.build.BuildConfig.ENABLE_ASSERTS;
            }
        });
    }

    static Window.Callback createWindowCallbackProxy(Activity activity, Window.Callback callback) {
        return (Window.Callback) Proxy.newProxyInstance(Window.Callback.class.getClassLoader(), new Class[]{Window.Callback.class}, new WindowCallbackProxy(activity, callback));
    }

    static boolean reachesWindowCallback(Window.Callback callback) {
        if (callback == null) {
            return false;
        }
        if (callback.getClass().getName().equals(TOOLBAR_CALLBACK_WRAPPER_CLASS)) {
            return true;
        }
        if (Proxy.isProxyClass(callback.getClass())) {
            return Proxy.getInvocationHandler(callback) instanceof WindowCallbackProxy;
        }
        for (Class<?> superclass = callback.getClass(); superclass != Object.class; superclass = superclass.getSuperclass()) {
            for (Field field : superclass.getDeclaredFields()) {
                if (field.getType().isAssignableFrom(Window.Callback.class)) {
                    boolean zIsAccessible = field.isAccessible();
                    field.setAccessible(true);
                    try {
                        Window.Callback callback2 = (Window.Callback) field.get(callback);
                        field.setAccessible(zIsAccessible);
                        if (reachesWindowCallback(callback2)) {
                            return true;
                        }
                    } catch (IllegalAccessException unused) {
                        field.setAccessible(zIsAccessible);
                    } catch (Throwable th) {
                        field.setAccessible(zIsAccessible);
                        throw th;
                    }
                }
            }
        }
        return false;
    }

    /* JADX WARN: Removed duplicated region for block: B:18:0x0033 A[Catch: all -> 0x0094, TryCatch #0 {, blocks: (B:15:0x001b, B:16:0x0025, B:18:0x0033, B:20:0x003c, B:21:0x003e, B:22:0x0044), top: B:48:0x001b }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private static void onStateChange(android.app.Activity r5, int r6) {
        /*
            if (r5 == 0) goto L97
            android.app.Activity r0 = aegon.chrome.base.ApplicationStatus.sActivity
            r1 = 1
            if (r0 == 0) goto Lf
            if (r6 == r1) goto Lf
            r0 = 3
            if (r6 == r0) goto Lf
            r0 = 2
            if (r6 != r0) goto L11
        Lf:
            aegon.chrome.base.ApplicationStatus.sActivity = r5
        L11:
            int r0 = getStateForApplication()
            java.util.Map<android.app.Activity, aegon.chrome.base.ApplicationStatus$ActivityInfo> r2 = aegon.chrome.base.ApplicationStatus.sActivityInfo
            monitor-enter(r2)
            r3 = 0
            if (r6 != r1) goto L25
            java.util.Map<android.app.Activity, aegon.chrome.base.ApplicationStatus$ActivityInfo> r1 = aegon.chrome.base.ApplicationStatus.sActivityInfo     // Catch: java.lang.Throwable -> L94
            aegon.chrome.base.ApplicationStatus$ActivityInfo r4 = new aegon.chrome.base.ApplicationStatus$ActivityInfo     // Catch: java.lang.Throwable -> L94
            r4.<init>()     // Catch: java.lang.Throwable -> L94
            r1.put(r5, r4)     // Catch: java.lang.Throwable -> L94
        L25:
            java.util.Map<android.app.Activity, aegon.chrome.base.ApplicationStatus$ActivityInfo> r1 = aegon.chrome.base.ApplicationStatus.sActivityInfo     // Catch: java.lang.Throwable -> L94
            java.lang.Object r1 = r1.get(r5)     // Catch: java.lang.Throwable -> L94
            aegon.chrome.base.ApplicationStatus$ActivityInfo r1 = (aegon.chrome.base.ApplicationStatus.ActivityInfo) r1     // Catch: java.lang.Throwable -> L94
            r1.setStatus(r6)     // Catch: java.lang.Throwable -> L94
            r4 = 6
            if (r6 != r4) goto L3e
            java.util.Map<android.app.Activity, aegon.chrome.base.ApplicationStatus$ActivityInfo> r4 = aegon.chrome.base.ApplicationStatus.sActivityInfo     // Catch: java.lang.Throwable -> L94
            r4.remove(r5)     // Catch: java.lang.Throwable -> L94
            android.app.Activity r4 = aegon.chrome.base.ApplicationStatus.sActivity     // Catch: java.lang.Throwable -> L94
            if (r5 != r4) goto L3e
            aegon.chrome.base.ApplicationStatus.sActivity = r3     // Catch: java.lang.Throwable -> L94
        L3e:
            int r3 = determineApplicationStateLocked()     // Catch: java.lang.Throwable -> L94
            aegon.chrome.base.ApplicationStatus.sCurrentApplicationState = r3     // Catch: java.lang.Throwable -> L94
            monitor-exit(r2)     // Catch: java.lang.Throwable -> L94
            aegon.chrome.base.ObserverList r1 = r1.getListeners()
            java.util.Iterator r1 = r1.iterator2()
        L4d:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L5d
            java.lang.Object r2 = r1.mo35924next()
            aegon.chrome.base.ApplicationStatus$ActivityStateListener r2 = (aegon.chrome.base.ApplicationStatus.ActivityStateListener) r2
            r2.onActivityStateChange(r5, r6)
            goto L4d
        L5d:
            aegon.chrome.base.ObserverList<aegon.chrome.base.ApplicationStatus$ActivityStateListener> r1 = aegon.chrome.base.ApplicationStatus.sGeneralActivityStateListeners
            if (r1 == 0) goto L75
            java.util.Iterator r1 = r1.iterator2()
        L65:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L75
            java.lang.Object r2 = r1.mo35924next()
            aegon.chrome.base.ApplicationStatus$ActivityStateListener r2 = (aegon.chrome.base.ApplicationStatus.ActivityStateListener) r2
            r2.onActivityStateChange(r5, r6)
            goto L65
        L75:
            int r5 = getStateForApplication()
            if (r5 == r0) goto L93
            aegon.chrome.base.ObserverList<aegon.chrome.base.ApplicationStatus$ApplicationStateListener> r6 = aegon.chrome.base.ApplicationStatus.sApplicationStateListeners
            if (r6 == 0) goto L93
            java.util.Iterator r6 = r6.iterator2()
        L83:
            boolean r0 = r6.hasNext()
            if (r0 == 0) goto L93
            java.lang.Object r0 = r6.mo35924next()
            aegon.chrome.base.ApplicationStatus$ApplicationStateListener r0 = (aegon.chrome.base.ApplicationStatus.ApplicationStateListener) r0
            r0.onApplicationStateChange(r5)
            goto L83
        L93:
            return
        L94:
            r5 = move-exception
            monitor-exit(r2)     // Catch: java.lang.Throwable -> L94
            throw r5
        L97:
            java.lang.IllegalArgumentException r5 = new java.lang.IllegalArgumentException
            java.lang.String r6 = "null activity is not supported"
            r5.<init>(r6)
            goto La0
        L9f:
            throw r5
        La0:
            goto L9f
        */
        throw new UnsupportedOperationException("Method not decompiled: aegon.chrome.base.ApplicationStatus.onStateChange(android.app.Activity, int):void");
    }

    public static void onStateChangeForTesting(Activity activity, int i) {
        onStateChange(activity, i);
    }

    public static Activity getLastTrackedFocusedActivity() {
        return sActivity;
    }

    public static List<Activity> getRunningActivities() {
        ArrayList arrayList;
        synchronized (sActivityInfo) {
            arrayList = new ArrayList(sActivityInfo.keySet());
        }
        return arrayList;
    }

    public static int getStateForActivity(Activity activity) {
        ActivityInfo activityInfo;
        if (activity == null || (activityInfo = sActivityInfo.get(activity)) == null) {
            return 6;
        }
        return activityInfo.getStatus();
    }

    public static int getStateForApplication() {
        int i;
        synchronized (sActivityInfo) {
            i = sCurrentApplicationState;
        }
        return i;
    }

    public static boolean hasVisibleActivities() {
        int stateForApplication = getStateForApplication();
        return stateForApplication == 1 || stateForApplication == 2;
    }

    public static boolean isEveryActivityDestroyed() {
        return sActivityInfo.isEmpty();
    }

    public static void registerStateListenerForAllActivities(ActivityStateListener activityStateListener) {
        if (sGeneralActivityStateListeners == null) {
            sGeneralActivityStateListeners = new ObserverList<>();
        }
        sGeneralActivityStateListeners.addObserver(activityStateListener);
    }

    public static void registerStateListenerForActivity(ActivityStateListener activityStateListener, Activity activity) {
        sActivityInfo.get(activity).getListeners().addObserver(activityStateListener);
    }

    public static void unregisterActivityStateListener(ActivityStateListener activityStateListener) {
        ObserverList<ActivityStateListener> observerList = sGeneralActivityStateListeners;
        if (observerList != null) {
            observerList.removeObserver(activityStateListener);
        }
        synchronized (sActivityInfo) {
            Iterator<ActivityInfo> it = sActivityInfo.values().iterator2();
            while (it.hasNext()) {
                it.mo35924next().getListeners().removeObserver(activityStateListener);
            }
        }
    }

    public static void registerApplicationStateListener(ApplicationStateListener applicationStateListener) {
        if (sApplicationStateListeners == null) {
            sApplicationStateListeners = new ObserverList<>();
        }
        sApplicationStateListeners.addObserver(applicationStateListener);
    }

    public static void unregisterApplicationStateListener(ApplicationStateListener applicationStateListener) {
        ObserverList<ApplicationStateListener> observerList = sApplicationStateListeners;
        if (observerList == null) {
            return;
        }
        observerList.removeObserver(applicationStateListener);
    }

    public static void destroyForJUnitTests() {
        synchronized (sActivityInfo) {
            if (sApplicationStateListeners != null) {
                sApplicationStateListeners.clear();
            }
            if (sGeneralActivityStateListeners != null) {
                sGeneralActivityStateListeners.clear();
            }
            sActivityInfo.clear();
            if (sWindowFocusListeners != null) {
                sWindowFocusListeners.clear();
            }
            sCurrentApplicationState = 0;
            sActivity = null;
            sNativeApplicationStateListener = null;
        }
    }

    public static void resetActivitiesForInstrumentationTests() {
        synchronized (sActivityInfo) {
            Iterator it = new HashSet(sActivityInfo.keySet()).iterator2();
            while (it.hasNext()) {
                onStateChangeForTesting((Activity) it.mo35924next(), 6);
            }
        }
    }

    private static void registerThreadSafeNativeApplicationStateListener() {
        ThreadUtils.runOnUiThread(new Runnable() { // from class: aegon.chrome.base.ApplicationStatus.3
            @Override // java.lang.Runnable
            public void run() {
                if (ApplicationStatus.sNativeApplicationStateListener != null) {
                    return;
                }
                ApplicationStateListener unused = ApplicationStatus.sNativeApplicationStateListener = new ApplicationStateListener() { // from class: aegon.chrome.base.ApplicationStatus.3.1
                    @Override // aegon.chrome.base.ApplicationStatus.ApplicationStateListener
                    public void onApplicationStateChange(int i) {
                        ApplicationStatusJni.get().onApplicationStateChange(i);
                    }
                };
                ApplicationStatus.registerApplicationStateListener(ApplicationStatus.sNativeApplicationStateListener);
            }
        });
    }

    private static int determineApplicationStateLocked() {
        Iterator<ActivityInfo> it = sActivityInfo.values().iterator2();
        boolean z = false;
        boolean z2 = false;
        while (it.hasNext()) {
            int status = it.mo35924next().getStatus();
            if (status != 4 && status != 5 && status != 6) {
                return 1;
            }
            if (status == 4) {
                z = true;
            } else if (status == 5) {
                z2 = true;
            }
        }
        if (z) {
            return 2;
        }
        return z2 ? 3 : 4;
    }
}
