package android.icu.impl.duration.impl;

import android.icu.lang.UCharacter;
import com.getui.gtc.extension.distribution.gbd.utils.C2034aa;
import java.p654io.IOException;
import java.p654io.Writer;
import java.util.ArrayList;
import java.util.List;
import org.apache.xml.serializer.SerializerConstants;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class XMLRecordWriter implements RecordWriter {
    private static final String INDENT = "    ";
    static final String NULL_NAME = "Null";
    private List<String> nameStack = new ArrayList();

    /* renamed from: w */
    private Writer f77w;

    public XMLRecordWriter(Writer w) {
        this.f77w = w;
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public boolean open(String title) {
        newline();
        writeString("<" + title + ">");
        this.nameStack.add(title);
        return true;
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public boolean close() {
        int ix = this.nameStack.size() - 1;
        if (ix >= 0) {
            String name = this.nameStack.remove(ix);
            newline();
            writeString("</" + name + ">");
            return true;
        }
        return false;
    }

    public void flush() {
        try {
            this.f77w.flush();
        } catch (IOException e) {
        }
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public void bool(String name, boolean value) {
        internalString(name, String.valueOf(value));
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public void boolArray(String name, boolean[] values) {
        if (values != null) {
            String[] stringValues = new String[values.length];
            for (int i = 0; i < values.length; i++) {
                stringValues[i] = String.valueOf(values[i]);
            }
            stringArray(name, stringValues);
        }
    }

    private static String ctos(char value) {
        if (value == '<') {
            return SerializerConstants.ENTITY_LT;
        }
        if (value == '&') {
            return SerializerConstants.ENTITY_AMP;
        }
        return String.valueOf(value);
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public void character(String name, char value) {
        if (value != 65535) {
            internalString(name, ctos(value));
        }
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public void characterArray(String name, char[] values) {
        if (values != null) {
            String[] stringValues = new String[values.length];
            for (int i = 0; i < values.length; i++) {
                char value = values[i];
                if (value == 65535) {
                    stringValues[i] = NULL_NAME;
                } else {
                    stringValues[i] = ctos(value);
                }
            }
            internalStringArray(name, stringValues);
        }
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public void namedIndex(String name, String[] names, int value) {
        if (value >= 0) {
            internalString(name, names[value]);
        }
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public void namedIndexArray(String name, String[] names, byte[] values) {
        if (values != null) {
            String[] stringValues = new String[values.length];
            for (int i = 0; i < values.length; i++) {
                int value = values[i];
                if (value < 0) {
                    stringValues[i] = NULL_NAME;
                } else {
                    stringValues[i] = names[value];
                }
            }
            internalStringArray(name, stringValues);
        }
    }

    public static String normalize(String str) {
        boolean special;
        if (str == null) {
            return null;
        }
        StringBuilder sb = null;
        boolean inWhitespace = false;
        for (int i = 0; i < str.length(); i++) {
            char c2 = str.charAt(i);
            if (UCharacter.isWhitespace(c2)) {
                if (sb == null && (inWhitespace || c2 != ' ')) {
                    sb = new StringBuilder(str.substring(0, i));
                }
                if (!inWhitespace) {
                    inWhitespace = true;
                    special = false;
                    c2 = ' ';
                }
            } else {
                inWhitespace = false;
                special = c2 == '<' || c2 == '&';
                if (special && sb == null) {
                    sb = new StringBuilder(str.substring(0, i));
                }
            }
            if (sb != null) {
                if (special) {
                    sb.append(c2 == '<' ? SerializerConstants.ENTITY_LT : SerializerConstants.ENTITY_AMP);
                } else {
                    sb.append(c2);
                }
            }
        }
        if (sb != null) {
            return sb.toString();
        }
        return str;
    }

    private void internalString(String name, String normalizedValue) {
        if (normalizedValue != null) {
            newline();
            writeString("<" + name + ">" + normalizedValue + "</" + name + ">");
        }
    }

    private void internalStringArray(String name, String[] normalizedValues) {
        if (normalizedValues != null) {
            push(name + "List");
            for (String value : normalizedValues) {
                if (value == null) {
                    value = NULL_NAME;
                }
                string(name, value);
            }
            pop();
        }
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public void string(String name, String value) {
        internalString(name, normalize(value));
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public void stringArray(String name, String[] values) {
        if (values != null) {
            push(name + "List");
            for (String str : values) {
                String value = normalize(str);
                if (value == null) {
                    value = NULL_NAME;
                }
                internalString(name, value);
            }
            pop();
        }
    }

    @Override // android.icu.impl.duration.impl.RecordWriter
    public void stringTable(String name, String[][] values) {
        if (values != null) {
            push(name + "Table");
            for (String[] rowValues : values) {
                if (rowValues == null) {
                    internalString(name + "List", NULL_NAME);
                } else {
                    stringArray(name, rowValues);
                }
            }
            pop();
        }
    }

    private void push(String name) {
        newline();
        writeString("<" + name + ">");
        this.nameStack.add(name);
    }

    private void pop() {
        int ix = this.nameStack.size() - 1;
        String name = this.nameStack.remove(ix);
        newline();
        writeString("</" + name + ">");
    }

    private void newline() {
        writeString(C2034aa.f6731b);
        for (int i = 0; i < this.nameStack.size(); i++) {
            writeString(INDENT);
        }
    }

    private void writeString(String str) {
        Writer writer = this.f77w;
        if (writer != null) {
            try {
                writer.write(str);
            } catch (IOException e) {
                System.err.println(e.getMessage());
                this.f77w = null;
            }
        }
    }
}
