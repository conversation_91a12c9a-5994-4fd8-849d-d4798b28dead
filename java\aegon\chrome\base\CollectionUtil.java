package aegon.chrome.base;

import android.util.Pair;
import androidx.media3.exoplayer.upstream.experimental.ExponentialWeightedAverageTimeToFirstByteEstimator;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class CollectionUtil {
    private CollectionUtil() {
    }

    @SafeVarargs
    public static <E> HashSet<E> newHashSet(E... eArr) {
        HashSet<E> hashSet = new HashSet<>(eArr.length);
        Collections.addAll(hashSet, eArr);
        return hashSet;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @SafeVarargs
    public static <K, V> HashMap<K, V> newHashMap(Pair<? extends K, ? extends V>... pairArr) {
        ExponentialWeightedAverageTimeToFirstByteEstimator.FixedSizeLinkedHashMap fixedSizeLinkedHashMap = (HashMap<K, V>) new HashMap();
        for (Pair<? extends K, ? extends V> pair : pairArr) {
            fixedSizeLinkedHashMap.put(pair.first, pair.second);
        }
        return fixedSizeLinkedHashMap;
    }

    public static boolean[] booleanListToBooleanArray(List<Boolean> list) {
        boolean[] zArr = new boolean[list.size()];
        for (int i = 0; i < list.size(); i++) {
            zArr[i] = list.get(i).booleanValue();
        }
        return zArr;
    }

    public static int[] integerListToIntArray(List<Integer> list) {
        int[] iArr = new int[list.size()];
        for (int i = 0; i < list.size(); i++) {
            iArr[i] = list.get(i).intValue();
        }
        return iArr;
    }

    public static long[] longListToLongArray(List<Long> list) {
        long[] jArr = new long[list.size()];
        for (int i = 0; i < list.size(); i++) {
            jArr[i] = list.get(i).longValue();
        }
        return jArr;
    }

    public static <T> void forEach(Collection<? extends T> collection, Callback<T> callback) {
        Iterator<? extends T> itIterator2 = collection.iterator2();
        while (itIterator2.hasNext()) {
            callback.onResult(itIterator2.mo35924next());
        }
    }

    public static <K, V> void forEach(Map<? extends K, ? extends V> map, Callback<Map.Entry<K, V>> callback) {
        Iterator<Map.Entry<? extends K, ? extends V>> itIterator2 = map.entrySet().iterator2();
        while (itIterator2.hasNext()) {
            callback.onResult(itIterator2.mo35924next());
        }
    }

    public static <T> List<T> strengthen(Collection<WeakReference<T>> collection) {
        ArrayList arrayList = new ArrayList(collection.size());
        Iterator<WeakReference<T>> itIterator2 = collection.iterator2();
        while (itIterator2.hasNext()) {
            T t = itIterator2.mo35924next().get();
            if (t == null) {
                itIterator2.remove();
            } else {
                arrayList.add(t);
            }
        }
        return arrayList;
    }
}
