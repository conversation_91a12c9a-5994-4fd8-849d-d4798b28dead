package android.icu.impl.number.parse;

import android.icu.impl.StaticUnicodeSets;
import android.icu.impl.StringSegment;
import android.icu.impl.number.parse.NumberParseMatcher;
import android.icu.text.UnicodeSet;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class IgnorablesMatcher extends SymbolMatcher implements NumberParseMatcher.Flexible {
    private static final IgnorablesMatcher DEFAULT = new IgnorablesMatcher(StaticUnicodeSets.get(StaticUnicodeSets.Key.DEFAULT_IGNORABLES));
    private static final IgnorablesMatcher STRICT = new IgnorablesMatcher(StaticUnicodeSets.get(StaticUnicodeSets.Key.STRICT_IGNORABLES));
    private static final IgnorablesMatcher JAVA_COMPATIBILITY = new IgnorablesMatcher(StaticUnicodeSets.get(StaticUnicodeSets.Key.EMPTY));

    public static IgnorablesMatcher getInstance(int parseFlags) {
        if ((65536 & parseFlags) != 0) {
            return JAVA_COMPATIBILITY;
        }
        if ((32768 & parseFlags) != 0) {
            return STRICT;
        }
        return DEFAULT;
    }

    private IgnorablesMatcher(UnicodeSet ignorables) {
        super("", ignorables);
    }

    @Override // android.icu.impl.number.parse.SymbolMatcher
    protected boolean isDisabled(ParsedNumber result) {
        return false;
    }

    @Override // android.icu.impl.number.parse.SymbolMatcher
    protected void accept(StringSegment segment, ParsedNumber result) {
    }

    public String toString() {
        return "<IgnorablesMatcher>";
    }
}
