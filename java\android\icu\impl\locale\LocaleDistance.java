package android.icu.impl.locale;

import android.icu.impl.ICUData;
import android.icu.impl.ICUResourceBundle;
import android.icu.impl.UResource;
import android.icu.util.BytesTrie;
import android.icu.util.LocaleMatcher;
import android.icu.util.ULocale;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.Set;
import java.util.TreeMap;
import org.apache.xalan.templates.Constants;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class LocaleDistance {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final int ABOVE_THRESHOLD = 100;
    private static final boolean DEBUG_OUTPUT = false;
    private static final int DISTANCE_IS_FINAL = 256;
    private static final int DISTANCE_IS_FINAL_OR_SKIP_SCRIPT = 384;
    public static final int DISTANCE_SKIP_SCRIPT = 128;
    public static final int END_OF_SUBTAG = 128;
    public static final LocaleDistance INSTANCE = new LocaleDistance(Data.load());
    public static final int IX_DEF_LANG_DISTANCE = 0;
    public static final int IX_DEF_REGION_DISTANCE = 2;
    public static final int IX_DEF_SCRIPT_DISTANCE = 1;
    public static final int IX_LIMIT = 4;
    public static final int IX_MIN_REGION_DISTANCE = 3;
    private final int defaultDemotionPerDesiredLocale;
    private final int defaultLanguageDistance;
    private final int defaultRegionDistance;
    private final int defaultScriptDistance;
    private final int minRegionDistance;
    private final Set<LSR> paradigmLSRs;
    private final String[] partitionArrays;
    private final byte[] regionToPartitionsIndex;
    private final BytesTrie trie;

    public static final class Data {
        public int[] distances;
        public Set<LSR> paradigmLSRs;
        public String[] partitionArrays;
        public byte[] regionToPartitionsIndex;
        public byte[] trie;

        public Data(byte[] trie, byte[] regionToPartitionsIndex, String[] partitionArrays, Set<LSR> paradigmLSRs, int[] distances) {
            this.trie = trie;
            this.regionToPartitionsIndex = regionToPartitionsIndex;
            this.partitionArrays = partitionArrays;
            this.paradigmLSRs = paradigmLSRs;
            this.distances = distances;
        }

        private static UResource.Value getValue(UResource.Table table, String key, UResource.Value value) {
            if (!table.findValue(key, value)) {
                throw new MissingResourceException("langInfo.res missing data", "", "match/" + key);
            }
            return value;
        }

        public static Data load() throws MissingResourceException {
            Set<LSR> paradigmLSRs;
            ICUResourceBundle langInfo = ICUResourceBundle.getBundleInstance(ICUData.ICU_BASE_NAME, "langInfo", ICUResourceBundle.ICU_DATA_CLASS_LOADER, ICUResourceBundle.OpenType.DIRECT);
            UResource.Value value = langInfo.getValueWithFallback(Constants.ATTRNAME_MATCH);
            UResource.Table matchTable = value.getTable();
            ByteBuffer buffer = getValue(matchTable, "trie", value).getBinary();
            byte[] trie = new byte[buffer.remaining()];
            buffer.get(trie);
            ByteBuffer buffer2 = getValue(matchTable, "regionToPartitions", value).getBinary();
            byte[] regionToPartitions = new byte[buffer2.remaining()];
            buffer2.get(regionToPartitions);
            if (regionToPartitions.length < 1677) {
                throw new MissingResourceException("langInfo.res binary data too short", "", "match/regionToPartitions");
            }
            String[] partitions = getValue(matchTable, "partitions", value).getStringArray();
            if (matchTable.findValue("paradigms", value)) {
                String[] paradigms = value.getStringArray();
                Set<LSR> paradigmLSRs2 = new HashSet<>(paradigms.length / 3);
                for (int i = 0; i < paradigms.length; i += 3) {
                    paradigmLSRs2.add(new LSR(paradigms[i], paradigms[i + 1], paradigms[i + 2]));
                }
                paradigmLSRs = paradigmLSRs2;
            } else {
                Set<LSR> paradigmLSRs3 = Collections.emptySet();
                paradigmLSRs = paradigmLSRs3;
            }
            int[] distances = getValue(matchTable, "distances", value).getIntVector();
            if (distances.length < 4) {
                throw new MissingResourceException("langInfo.res intvector too short", "", "match/distances");
            }
            return new Data(trie, regionToPartitions, partitions, paradigmLSRs, distances);
        }

        public boolean equals(Object other) {
            if (this == other) {
                return true;
            }
            if (!getClass().equals(other.getClass())) {
                return false;
            }
            Data od = (Data) other;
            return Arrays.equals(this.trie, od.trie) && Arrays.equals(this.regionToPartitionsIndex, od.regionToPartitionsIndex) && Arrays.equals(this.partitionArrays, od.partitionArrays) && this.paradigmLSRs.equals(od.paradigmLSRs) && Arrays.equals(this.distances, od.distances);
        }
    }

    private LocaleDistance(Data data) {
        this.trie = new BytesTrie(data.trie, 0);
        this.regionToPartitionsIndex = data.regionToPartitionsIndex;
        this.partitionArrays = data.partitionArrays;
        this.paradigmLSRs = data.paradigmLSRs;
        this.defaultLanguageDistance = data.distances[0];
        this.defaultScriptDistance = data.distances[1];
        this.defaultRegionDistance = data.distances[2];
        this.minRegionDistance = data.distances[3];
        LSR en = new LSR("en", "Latn", "US");
        LSR enGB = new LSR("en", "Latn", "GB");
        this.defaultDemotionPerDesiredLocale = getBestIndexAndDistance(en, new LSR[]{enGB}, 50, LocaleMatcher.FavorSubtag.LANGUAGE) & 255;
    }

    public int testOnlyDistance(ULocale desired, ULocale supported, int threshold, LocaleMatcher.FavorSubtag favorSubtag) {
        LSR supportedLSR = XLikelySubtags.INSTANCE.makeMaximizedLsrFrom(supported);
        LSR desiredLSR = XLikelySubtags.INSTANCE.makeMaximizedLsrFrom(desired);
        return getBestIndexAndDistance(desiredLSR, new LSR[]{supportedLSR}, threshold, favorSubtag) & 255;
    }

    /* JADX WARN: Incorrect condition in loop: B:10:0x0029 */
    /* JADX WARN: Removed duplicated region for block: B:39:0x009d  */
    /* JADX WARN: Removed duplicated region for block: B:40:0x00a1  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public int getBestIndexAndDistance(android.icu.impl.locale.LSR r25, android.icu.impl.locale.LSR[] r26, int r27, android.icu.util.LocaleMatcher.FavorSubtag r28) {
        /*
            Method dump skipped, instructions count: 269
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.locale.LocaleDistance.getBestIndexAndDistance(android.icu.impl.locale.LSR, android.icu.impl.locale.LSR[], int, android.icu.util.LocaleMatcher$FavorSubtag):int");
    }

    private static final int getDesSuppScriptDistance(BytesTrie iter, long startState, String desired, String supported) {
        int distance;
        int distance2 = trieNext(iter, desired, false);
        if (distance2 >= 0) {
            distance2 = trieNext(iter, supported, true);
        }
        if (distance2 < 0) {
            BytesTrie.Result result = iter.resetToState64(startState).next(42);
            if (desired.equals(supported)) {
                distance = 0;
            } else {
                distance = iter.getValue();
            }
            if (result == BytesTrie.Result.FINAL_VALUE) {
                return distance | 256;
            }
            return distance;
        }
        return distance2;
    }

    private static final int getRegionPartitionsDistance(BytesTrie iter, long startState, String desiredPartitions, String supportedPartitions, int threshold) {
        int d2;
        int desLength = desiredPartitions.length();
        int suppLength = supportedPartitions.length();
        if (desLength == 1 && suppLength == 1) {
            BytesTrie.Result result = iter.next(desiredPartitions.charAt(0) | 128);
            if (result.hasNext()) {
                BytesTrie.Result result2 = iter.next(supportedPartitions.charAt(0) | 128);
                if (result2.hasValue()) {
                    return iter.getValue();
                }
            }
            return getFallbackRegionDistance(iter, startState);
        }
        int regionDistance = 0;
        boolean star = false;
        int di = 0;
        while (true) {
            int di2 = di + 1;
            BytesTrie.Result result3 = iter.next(desiredPartitions.charAt(di) | 128);
            if (result3.hasNext()) {
                long desState = suppLength > 1 ? iter.getState64() : 0L;
                int d3 = 0;
                while (true) {
                    int si = d3 + 1;
                    BytesTrie.Result result4 = iter.next(supportedPartitions.charAt(d3) | 128);
                    if (result4.hasValue()) {
                        d2 = iter.getValue();
                    } else if (star) {
                        d2 = 0;
                    } else {
                        d2 = getFallbackRegionDistance(iter, startState);
                        star = true;
                    }
                    if (d2 >= threshold) {
                        return d2;
                    }
                    if (regionDistance < d2) {
                        regionDistance = d2;
                    }
                    if (si >= suppLength) {
                        break;
                    }
                    iter.resetToState64(desState);
                    d3 = si;
                }
            } else if (!star) {
                int d4 = getFallbackRegionDistance(iter, startState);
                if (d4 >= threshold) {
                    return d4;
                }
                if (regionDistance < d4) {
                    regionDistance = d4;
                }
                star = true;
            }
            if (di2 < desLength) {
                iter.resetToState64(startState);
                di = di2;
            } else {
                return regionDistance;
            }
        }
    }

    private static final int getFallbackRegionDistance(BytesTrie iter, long startState) {
        iter.resetToState64(startState).next(42);
        int distance = iter.getValue();
        return distance;
    }

    private static final int trieNext(BytesTrie iter, String s, boolean wantValue) {
        if (s.isEmpty()) {
            return -1;
        }
        int i = 0;
        int end = s.length() - 1;
        while (true) {
            int c2 = s.charAt(i);
            if (i < end) {
                if (!iter.next(c2).hasNext()) {
                    return -1;
                }
                i++;
            } else {
                BytesTrie.Result result = iter.next(c2 | 128);
                if (wantValue) {
                    if (result.hasValue()) {
                        int value = iter.getValue();
                        if (result == BytesTrie.Result.FINAL_VALUE) {
                            return value | 256;
                        }
                        return value;
                    }
                } else if (result.hasNext()) {
                    return 0;
                }
                return -1;
            }
        }
    }

    public String toString() {
        return testOnlyGetDistanceTable().toString();
    }

    private String partitionsForRegion(LSR lsr) {
        int pIndex = this.regionToPartitionsIndex[lsr.regionIndex];
        return this.partitionArrays[pIndex];
    }

    public boolean isParadigmLSR(LSR lsr) {
        return this.paradigmLSRs.contains(lsr);
    }

    public int getDefaultScriptDistance() {
        return this.defaultScriptDistance;
    }

    int getDefaultRegionDistance() {
        return this.defaultRegionDistance;
    }

    public int getDefaultDemotionPerDesiredLocale() {
        return this.defaultDemotionPerDesiredLocale;
    }

    public Map<String, Integer> testOnlyGetDistanceTable() {
        Map<String, Integer> map = new TreeMap<>();
        StringBuilder sb = new StringBuilder();
        Iterator<BytesTrie.Entry> itIterator2 = this.trie.iterator2();
        while (itIterator2.hasNext()) {
            BytesTrie.Entry entry = itIterator2.mo35924next();
            sb.setLength(0);
            int length = entry.bytesLength();
            for (int i = 0; i < length; i++) {
                byte b2 = entry.byteAt(i);
                if (b2 == 42) {
                    sb.append("*-*-");
                } else if (b2 >= 0) {
                    sb.append((char) b2);
                } else {
                    sb.append((char) (b2 & Byte.MAX_VALUE));
                    sb.append('-');
                }
            }
            sb.setLength(sb.length() - 1);
            map.put(sb.toString(), Integer.valueOf(entry.value));
        }
        return map;
    }

    public void testOnlyPrintDistanceTable() {
        for (Map.Entry<String, Integer> mapping : testOnlyGetDistanceTable().entrySet()) {
            String suffix = "";
            int value = mapping.getValue().intValue();
            if ((value & 128) != 0) {
                value &= -129;
                suffix = " skip script";
            }
            System.out.println(mapping.getKey() + '=' + value + suffix);
        }
    }
}
