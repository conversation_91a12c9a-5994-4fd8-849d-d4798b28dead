package aegon.chrome.base;

import aegon.chrome.base.annotations.JNINamespace;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public abstract class PathService {
    public static final int DIR_MODULE = 3;

    interface Natives {
        void override(int i, String str);
    }

    private PathService() {
    }

    public static void override(int i, String str) {
        PathServiceJni.get().override(i, str);
    }
}
