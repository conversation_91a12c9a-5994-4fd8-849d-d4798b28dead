package com.example.yqbz_test;

import android.app.Activity;
import android.os.Bundle;
import android.widget.TextView;

/**
 * 元气桌面壁纸广告屏蔽模块主界面
 * 这个Activity主要用于显示模块信息，实际的广告屏蔽功能由AdBlockerModule实现
 */
public class MainActivity extends Activity {
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 创建简单的文本视图显示模块信息
        TextView textView = new TextView(this);
        textView.setText("元气桌面壁纸广告屏蔽模块\n\n" +
                "目标应用: com.cmcm.cfwallpaper\n" +
                "功能: 屏蔽应用内所有广告\n\n" +
                "支持的广告类型:\n" +
                "• 快手广告SDK\n" +
                "• 腾讯广告SDK\n" +
                "• 字节跳动广告SDK\n" +
                "• 开屏广告\n" +
                "• Banner广告\n" +
                "• 插屏广告\n" +
                "• 激励视频广告\n" +
                "• 原生广告\n\n" +
                "使用方法:\n" +
                "1. 安装此模块\n" +
                "2. 在Xposed框架中激活模块\n" +
                "3. 重启设备\n" +
                "4. 打开元气桌面壁纸应用即可享受无广告体验");
        
        textView.setPadding(50, 50, 50, 50);
        textView.setTextSize(16);
        
        setContentView(textView);
    }
}
