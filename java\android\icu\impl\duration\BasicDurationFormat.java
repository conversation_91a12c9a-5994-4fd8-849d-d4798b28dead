package android.icu.impl.duration;

import android.icu.text.DurationFormat;
import android.icu.util.ULocale;
import java.text.FieldPosition;
import java.util.Date;
import javax.xml.datatype.Duration;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class BasicDurationFormat extends DurationFormat {
    private static final long serialVersionUID = -3146984141909457700L;
    transient DurationFormatter formatter;
    transient PeriodFormatter pformatter;
    transient PeriodFormatterService pfs;

    public static BasicDurationFormat getInstance(ULocale locale) {
        return new BasicDurationFormat(locale);
    }

    @Override // android.icu.text.DurationFormat, java.text.Format
    public StringBuffer format(Object object, StringBuffer toAppend, FieldPosition pos) {
        if (object instanceof Long) {
            String res = formatDurationFromNow(((Long) object).longValue());
            toAppend.append(res);
            return toAppend;
        }
        if (object instanceof Date) {
            String res2 = formatDurationFromNowTo((Date) object);
            toAppend.append(res2);
            return toAppend;
        }
        if (object instanceof Duration) {
            String res3 = formatDuration(object);
            toAppend.append(res3);
            return toAppend;
        }
        throw new IllegalArgumentException("Cannot format given Object as a Duration");
    }

    public BasicDurationFormat() {
        this.pfs = null;
        BasicPeriodFormatterService basicPeriodFormatterService = BasicPeriodFormatterService.getInstance();
        this.pfs = basicPeriodFormatterService;
        this.formatter = basicPeriodFormatterService.newDurationFormatterFactory().getFormatter();
        this.pformatter = this.pfs.newPeriodFormatterFactory().setDisplayPastFuture(false).getFormatter();
    }

    public BasicDurationFormat(ULocale locale) {
        super(locale);
        this.pfs = null;
        BasicPeriodFormatterService basicPeriodFormatterService = BasicPeriodFormatterService.getInstance();
        this.pfs = basicPeriodFormatterService;
        this.formatter = basicPeriodFormatterService.newDurationFormatterFactory().setLocale(locale.getName()).getFormatter();
        this.pformatter = this.pfs.newPeriodFormatterFactory().setDisplayPastFuture(false).setLocale(locale.getName()).getFormatter();
    }

    @Override // android.icu.text.DurationFormat
    public String formatDurationFrom(long duration, long referenceDate) {
        return this.formatter.formatDurationFrom(duration, referenceDate);
    }

    @Override // android.icu.text.DurationFormat
    public String formatDurationFromNow(long duration) {
        return this.formatter.formatDurationFromNow(duration);
    }

    @Override // android.icu.text.DurationFormat
    public String formatDurationFromNowTo(Date targetDate) {
        return this.formatter.formatDurationFromNowTo(targetDate);
    }

    /* JADX WARN: Removed duplicated region for block: B:23:0x00b2  */
    /* JADX WARN: Removed duplicated region for block: B:24:0x00b9  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x00c1  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x00c7  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.String formatDuration(java.lang.Object r24) {
        /*
            Method dump skipped, instructions count: 247
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.duration.BasicDurationFormat.formatDuration(java.lang.Object):java.lang.String");
    }
}
