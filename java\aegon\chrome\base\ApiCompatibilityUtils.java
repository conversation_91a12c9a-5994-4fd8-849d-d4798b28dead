package aegon.chrome.base;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.ActivityOptions;
import android.app.Application;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.ImageDecoder;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.graphics.drawable.TransitionDrawable;
import android.hardware.display.DisplayManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.StrictMode;
import android.os.UserManager;
import android.provider.MediaStore;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.Display;
import android.view.View;
import android.view.Window;
import android.view.inputmethod.InputMethodSubtype;
import android.view.textclassifier.TextClassifier;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.core.p048c.C1099a;
import com.getui.gtc.extension.distribution.gbd.p156g.p157a.C1965e;
import com.getui.gtc.extension.distribution.gws.p198c.C2211c;
import dalvik.bytecode.Opcodes;
import java.p654io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class ApiCompatibilityUtils {
    static final /* synthetic */ boolean $assertionsDisabled = false;

    public static int compareBoolean(boolean z, boolean z2) {
        if (z == z2) {
            return 0;
        }
        return z ? 1 : -1;
    }

    public static int compareLong(long j, long j2) {
        if (j < j2) {
            return -1;
        }
        return j == j2 ? 0 : 1;
    }

    private static boolean isPasswordInputType(int i) {
        int i2 = i & Opcodes.OP_IPUT_OBJECT_JUMBO;
        return i2 == 129 || i2 == 225 || i2 == 18;
    }

    private ApiCompatibilityUtils() {
    }

    static class ApisQ {
        private ApisQ() {
        }

        static boolean isRunningInUserTestHarness() {
            return ActivityManager.isRunningInUserTestHarness();
        }

        static List<Integer> getTargetableDisplayIds(Activity activity) {
            DisplayManager displayManager;
            ArrayList arrayList = new ArrayList();
            if (activity == null || (displayManager = (DisplayManager) activity.getSystemService("display")) == null) {
                return arrayList;
            }
            Display[] displays = displayManager.getDisplays();
            ActivityManager activityManager = (ActivityManager) activity.getSystemService(C2211c.f7744n);
            for (Display display : displays) {
                if (display.getState() == 2 && activityManager.isActivityStartAllowedOnDisplay(activity, display.getDisplayId(), new Intent(activity, activity.getClass()))) {
                    arrayList.add(Integer.valueOf(display.getDisplayId()));
                }
            }
            return arrayList;
        }
    }

    static class ApisP {
        private ApisP() {
        }

        static String getProcessName() {
            return Application.getProcessName();
        }

        static Bitmap getBitmapByUri(ContentResolver contentResolver, Uri uri) {
            return ImageDecoder.decodeBitmap(ImageDecoder.createSource(contentResolver, uri));
        }
    }

    static class ApisO {
        private ApisO() {
        }

        static void initNotificationSettingsIntent(Intent intent, String str) {
            intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
            intent.putExtra("android.provider.extra.APP_PACKAGE", str);
        }

        static void disableSmartSelectionTextClassifier(TextView textView) {
            textView.setTextClassifier(TextClassifier.NO_OP);
        }

        static Bundle createLaunchDisplayIdActivityOptions(int i) {
            ActivityOptions activityOptionsMakeBasic = ActivityOptions.makeBasic();
            activityOptionsMakeBasic.setLaunchDisplayId(i);
            return activityOptionsMakeBasic.toBundle();
        }
    }

    static class ApisN {
        private ApisN() {
        }

        static String toHtml(Spanned spanned, int i) {
            return Html.toHtml(spanned, i);
        }

        static boolean isDemoUser() {
            return ((UserManager) ContextUtils.getApplicationContext().getSystemService("user")).isDemoUser();
        }

        static String getLocale(InputMethodSubtype inputMethodSubtype) {
            return inputMethodSubtype.getLanguageTag();
        }

        static boolean isInMultiWindowMode(Activity activity) {
            return activity.isInMultiWindowMode();
        }
    }

    static class ApisM {
        private ApisM() {
        }

        public static void setStatusBarIconColor(View view, boolean z) {
            int systemUiVisibility = view.getSystemUiVisibility();
            view.setSystemUiVisibility(z ? systemUiVisibility | 8192 : systemUiVisibility & (-8193));
        }
    }

    static class ApisLmr1 {
        private ApisLmr1() {
        }

        static void setAccessibilityTraversalBefore(View view, int i) {
            view.setAccessibilityTraversalBefore(i);
        }
    }

    public static <T> T requireNonNull(T t) {
        if (t != null) {
            return t;
        }
        throw new NullPointerException();
    }

    public static <T> T requireNonNull(T t, String str) {
        if (t != null) {
            return t;
        }
        throw new NullPointerException(str);
    }

    public static byte[] getBytesUtf8(String str) {
        try {
            return str.getBytes(C1965e.f6503z);
        } catch (UnsupportedEncodingException e) {
            throw new IllegalStateException(e);
        }
    }

    public static String toHtml(Spanned spanned, int i) {
        if (Build.VERSION.SDK_INT >= 24) {
            return ApisN.toHtml(spanned, i);
        }
        return Html.toHtml(spanned);
    }

    public static void finishAndRemoveTask(Activity activity) {
        if (Build.VERSION.SDK_INT > 21) {
            activity.finishAndRemoveTask();
        } else {
            new FinishAndRemoveTaskWithRetry(activity).run();
        }
    }

    public static Intent getNotificationSettingsIntent() {
        Intent intent = new Intent();
        String packageName = ContextUtils.getApplicationContext().getPackageName();
        if (Build.VERSION.SDK_INT >= 26) {
            ApisO.initNotificationSettingsIntent(intent, packageName);
        } else {
            intent.setAction("android.settings.ACTION_APP_NOTIFICATION_SETTINGS");
            intent.putExtra("app_package", packageName);
            intent.putExtra("app_uid", ContextUtils.getApplicationContext().getApplicationInfo().uid);
        }
        return intent;
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static class FinishAndRemoveTaskWithRetry implements Runnable {
        private static final long MAX_TRY_COUNT = 3;
        private static final long RETRY_DELAY_MS = 500;
        private final Activity mActivity;
        private int mTryCount;

        FinishAndRemoveTaskWithRetry(Activity activity) {
            this.mActivity = activity;
        }

        @Override // java.lang.Runnable
        public void run() {
            this.mActivity.finishAndRemoveTask();
            this.mTryCount++;
            if (this.mActivity.isFinishing()) {
                return;
            }
            if (this.mTryCount < 3) {
                ThreadUtils.postOnUiThreadDelayed(this, 500L);
            } else {
                this.mActivity.finish();
            }
        }
    }

    public static void setStatusBarColor(Window window, int i) {
        if (Build.VERSION.SDK_INT < 26 && i == -16777216 && window.getNavigationBarColor() == -16777216) {
            window.clearFlags(Integer.MIN_VALUE);
        } else {
            window.addFlags(Integer.MIN_VALUE);
        }
        window.setStatusBarColor(i);
    }

    public static void setStatusBarIconColor(View view, boolean z) {
        if (Build.VERSION.SDK_INT >= 23) {
            ApisM.setStatusBarIconColor(view, z);
        }
    }

    public static Drawable getDrawable(Resources resources, int i) {
        return getDrawableForDensity(resources, i, 0);
    }

    public static void setImageTintList(ImageView imageView, ColorStateList colorStateList) {
        if (Build.VERSION.SDK_INT == 21 && colorStateList != null && imageView.getImageTintMode() == null) {
            imageView.setImageTintMode(PorterDuff.Mode.SRC_IN);
        }
        C1099a.setImageTintList(imageView, colorStateList);
        if (Build.VERSION.SDK_INT == 21 && colorStateList == null) {
            imageView.refreshDrawableState();
        }
    }

    public static Drawable getDrawableForDensity(Resources resources, int i, int i2) {
        StrictMode.ThreadPolicy threadPolicyAllowThreadDiskReads = StrictMode.allowThreadDiskReads();
        try {
            if (i2 == 0) {
                return resources.getDrawable(i, null);
            }
            return resources.getDrawableForDensity(i, i2, null);
        } finally {
            StrictMode.setThreadPolicy(threadPolicyAllowThreadDiskReads);
        }
    }

    public static int getColor(Resources resources, int i) {
        return resources.getColor(i);
    }

    public static void setTextAppearance(TextView textView, int i) {
        textView.setTextAppearance(textView.getContext(), i);
    }

    public static boolean isDemoUser() {
        return Build.VERSION.SDK_INT >= 25 && ApisN.isDemoUser();
    }

    public static int checkPermission(Context context, String str, int i, int i2) {
        try {
            return context.checkPermission(str, i, i2);
        } catch (RuntimeException unused) {
            return -1;
        }
    }

    public static String getLocale(InputMethodSubtype inputMethodSubtype) {
        if (Build.VERSION.SDK_INT >= 24) {
            return ApisN.getLocale(inputMethodSubtype);
        }
        return inputMethodSubtype.getLocale();
    }

    public static boolean isInMultiWindowMode(Activity activity) {
        if (Build.VERSION.SDK_INT >= 24) {
            return ApisN.isInMultiWindowMode(activity);
        }
        return false;
    }

    public static List<Integer> getTargetableDisplayIds(Activity activity) {
        if (Build.VERSION.SDK_INT >= 29) {
            return ApisQ.getTargetableDisplayIds(activity);
        }
        return new ArrayList();
    }

    public static void disableSmartSelectionTextClassifier(TextView textView) {
        if (Build.VERSION.SDK_INT >= 26) {
            ApisO.disableSmartSelectionTextClassifier(textView);
        }
    }

    public static Bundle createLaunchDisplayIdActivityOptions(int i) {
        if (Build.VERSION.SDK_INT >= 26) {
            return ApisO.createLaunchDisplayIdActivityOptions(i);
        }
        return null;
    }

    public static void setAccessibilityTraversalBefore(View view, int i) {
        if (Build.VERSION.SDK_INT >= 22) {
            ApisLmr1.setAccessibilityTraversalBefore(view, i);
        }
    }

    public static void setPasswordEditTextContentDescription(EditText editText) {
        if (Build.VERSION.SDK_INT < 24 && isPasswordInputType(editText.getInputType()) && !TextUtils.isEmpty(editText.getHint())) {
            editText.setContentDescription(editText.getHint());
        }
    }

    static String getProcessName() {
        if (Build.VERSION.SDK_INT >= 28) {
            return ApisP.getProcessName();
        }
        try {
            return (String) Class.forName("android.app.ActivityThread").getMethod("currentProcessName", new Class[0]).invoke(null, new Object[0]);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static boolean isRunningInUserTestHarness() {
        if (Build.VERSION.SDK_INT >= 29) {
            return ApisQ.isRunningInUserTestHarness();
        }
        return false;
    }

    public static Bitmap getBitmapByUri(ContentResolver contentResolver, Uri uri) {
        if (Build.VERSION.SDK_INT >= 28) {
            return ApisP.getBitmapByUri(contentResolver, uri);
        }
        return MediaStore.Images.Media.getBitmap(contentResolver, uri);
    }

    /* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
    static class LayerDrawableCompat extends LayerDrawable {
        private boolean mMutated;

        LayerDrawableCompat(Drawable[] drawableArr) {
            super(drawableArr);
        }

        @Override // android.graphics.drawable.LayerDrawable, android.graphics.drawable.Drawable
        public Drawable mutate() {
            if (this.mMutated) {
                return this;
            }
            Rect[] rectArrAccess$000 = ApiCompatibilityUtils.access$000(this);
            Drawable drawableMutate = super.mutate();
            if (drawableMutate != this) {
                return drawableMutate;
            }
            ApiCompatibilityUtils.access$100(this, rectArrAccess$000);
            this.mMutated = true;
            return this;
        }
    }

    /* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
    static class TransitionDrawableCompat extends TransitionDrawable {
        private boolean mMutated;

        TransitionDrawableCompat(Drawable[] drawableArr) {
            super(drawableArr);
        }

        @Override // android.graphics.drawable.LayerDrawable, android.graphics.drawable.Drawable
        public Drawable mutate() {
            if (this.mMutated) {
                return this;
            }
            Rect[] rectArrAccess$000 = ApiCompatibilityUtils.access$000(this);
            Drawable drawableMutate = super.mutate();
            if (drawableMutate != this) {
                return drawableMutate;
            }
            ApiCompatibilityUtils.access$100(this, rectArrAccess$000);
            this.mMutated = true;
            return this;
        }
    }
}
