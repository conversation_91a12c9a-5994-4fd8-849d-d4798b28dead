package android.icu.impl.locale;

import android.icu.util.ICUException;
import android.icu.util.ICUUncheckedIOException;
import java.net.URL;
import java.nio.charset.Charset;
import java.p654io.BufferedReader;
import java.p654io.File;
import java.p654io.InputStream;
import java.p654io.InputStreamReader;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class XCldrStub {

    public interface Predicate<T> {
        boolean test(T t);
    }

    public static class Multimap<K, V> {
        private final Map<K, Set<V>> map;
        private final Class<Set<V>> setClass;

        private Multimap(Map<K, Set<V>> map, Class<?> cls) {
            Class<HashSet> cls2;
            this.map = map;
            if (cls != null) {
                cls2 = (Class<Set<V>>) cls;
            } else {
                cls2 = HashSet.class;
            }
            this.setClass = cls2;
        }

        @SafeVarargs
        public final Multimap<K, V> putAll(K key, V... values) {
            if (values.length != 0) {
                createSetIfMissing(key).addAll(Arrays.asList(values));
            }
            return this;
        }

        /* JADX WARN: Multi-variable type inference failed */
        public void putAll(K key, Collection<V> collection) {
            if (!collection.isEmpty()) {
                createSetIfMissing(key).addAll(collection);
            }
        }

        public void putAll(Collection<K> keys, V value) {
            for (K key : keys) {
                put(key, value);
            }
        }

        public void putAll(Multimap<K, V> source) {
            for (Map.Entry<K, Set<V>> entry : source.map.entrySet()) {
                putAll((Multimap<K, V>) entry.getKey(), entry.getValue());
            }
        }

        public void put(K key, V value) {
            createSetIfMissing(key).add(value);
        }

        private Set<V> createSetIfMissing(K key) {
            Set<V> old = this.map.get(key);
            if (old == null) {
                Map<K, Set<V>> map = this.map;
                Set<V> old2 = getInstance();
                map.put(key, old2);
                return old2;
            }
            return old;
        }

        private Set<V> getInstance() {
            try {
                return this.setClass.newInstance();
            } catch (Exception e) {
                throw new ICUException(e);
            }
        }

        public Set<V> get(K key) {
            Set<V> result = this.map.get(key);
            return result;
        }

        public Set<K> keySet() {
            return this.map.keySet();
        }

        public Map<K, Set<V>> asMap() {
            return this.map;
        }

        public Set<V> values() {
            Collection<Set<V>> values = this.map.values();
            if (values.size() == 0) {
                return Collections.emptySet();
            }
            Set<V> result = getInstance();
            for (Set<V> valueSet : values) {
                result.addAll(valueSet);
            }
            return result;
        }

        public int size() {
            return this.map.size();
        }

        public Iterable<Map.Entry<K, V>> entries() {
            return new MultimapIterator(this.map);
        }

        public boolean equals(Object obj) {
            return this == obj || (obj != null && obj.getClass() == getClass() && this.map.equals(((Multimap) obj).map));
        }

        public int hashCode() {
            return this.map.hashCode();
        }
    }

    public static class Multimaps {
        public static <K, V, R extends Multimap<K, V>> R invertFrom(Multimap<V, K> source, R target) {
            for (Map.Entry<V, Set<K>> entry : source.asMap().entrySet()) {
                target.putAll(entry.getValue(), entry.getKey());
            }
            return target;
        }

        public static <K, V, R extends Multimap<K, V>> R invertFrom(Map<V, K> source, R target) {
            for (Map.Entry<V, K> entry : source.entrySet()) {
                target.put(entry.getValue(), entry.getKey());
            }
            return target;
        }

        public static <K, V> Map<K, V> forMap(Map<K, V> map) {
            return map;
        }
    }

    private static class MultimapIterator<K, V> implements Iterator<Map.Entry<K, V>>, Iterable<Map.Entry<K, V>> {
        private final ReusableEntry<K, V> entry;
        private final Iterator<Map.Entry<K, Set<V>>> it1;
        private Iterator<V> it2;

        private MultimapIterator(Map<K, Set<V>> map) {
            this.it2 = null;
            this.entry = new ReusableEntry<>();
            this.it1 = map.entrySet().iterator2();
        }

        @Override // java.util.Iterator
        public boolean hasNext() {
            Iterator<V> it;
            return this.it1.hasNext() || ((it = this.it2) != null && it.hasNext());
        }

        @Override // java.util.Iterator
        /* renamed from: next */
        public Map.Entry<K, V> mo35924next() {
            Iterator<V> it = this.it2;
            if (it != null && it.hasNext()) {
                this.entry.value = this.it2.mo35924next();
            } else {
                Map.Entry<K, Set<V>> e = this.it1.mo35924next();
                this.entry.key = e.getKey();
                this.it2 = e.getValue().iterator2();
            }
            return this.entry;
        }

        @Override // java.lang.Iterable
        /* renamed from: iterator */
        public Iterator<Map.Entry<K, V>> iterator2() {
            return this;
        }

        @Override // java.util.Iterator
        public void remove() {
            throw new UnsupportedOperationException();
        }
    }

    private static class ReusableEntry<K, V> implements Map.Entry<K, V> {
        K key;
        V value;

        private ReusableEntry() {
        }

        @Override // java.util.Map.Entry
        public K getKey() {
            return this.key;
        }

        @Override // java.util.Map.Entry
        public V getValue() {
            return this.value;
        }

        @Override // java.util.Map.Entry
        public V setValue(V value) {
            throw new UnsupportedOperationException();
        }
    }

    public static class HashMultimap<K, V> extends Multimap<K, V> {
        private HashMultimap() {
            super(new HashMap(), HashSet.class);
        }

        public static <K, V> HashMultimap<K, V> create() {
            return new HashMultimap<>();
        }
    }

    public static class TreeMultimap<K, V> extends Multimap<K, V> {
        private TreeMultimap() {
            super(new TreeMap(), TreeSet.class);
        }

        public static <K, V> TreeMultimap<K, V> create() {
            return new TreeMultimap<>();
        }
    }

    public static class LinkedHashMultimap<K, V> extends Multimap<K, V> {
        private LinkedHashMultimap() {
            super(new LinkedHashMap(), LinkedHashSet.class);
        }

        public static <K, V> LinkedHashMultimap<K, V> create() {
            return new LinkedHashMultimap<>();
        }
    }

    public static <T> String join(T[] source, String separator) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < source.length; i++) {
            if (i != 0) {
                result.append(separator);
            }
            result.append((Object) source[i]);
        }
        return result.toString();
    }

    public static <T> String join(Iterable<T> source, String separator) {
        StringBuilder result = new StringBuilder();
        boolean first = true;
        for (T item : source) {
            if (first) {
                first = false;
            } else {
                result.append(separator);
            }
            result.append(item.toString());
        }
        return result.toString();
    }

    public static class CollectionUtilities {
        public static <T, U extends Iterable<T>> String join(U source, String separator) {
            return XCldrStub.join(source, separator);
        }
    }

    public static class Joiner {
        private final String separator;

        private Joiner(String separator) {
            this.separator = separator;
        }

        /* renamed from: on */
        public static final Joiner m91on(String separator) {
            return new Joiner(separator);
        }

        public <T> String join(T[] source) {
            return XCldrStub.join(source, this.separator);
        }

        public <T> String join(Iterable<T> source) {
            return XCldrStub.join(source, this.separator);
        }
    }

    public static class Splitter {
        Pattern pattern;
        boolean trimResults;

        public Splitter(char c2) {
            this(Pattern.compile("\\Q" + c2 + "\\E"));
        }

        public Splitter(Pattern p) {
            this.trimResults = false;
            this.pattern = p;
        }

        /* renamed from: on */
        public static Splitter m92on(char c2) {
            return new Splitter(c2);
        }

        /* renamed from: on */
        public static Splitter m93on(Pattern p) {
            return new Splitter(p);
        }

        public List<String> splitToList(String input) {
            String[] items = this.pattern.split(input);
            if (this.trimResults) {
                for (int i = 0; i < items.length; i++) {
                    items[i] = items[i].trim();
                }
            }
            return Arrays.asList(items);
        }

        public Splitter trimResults() {
            this.trimResults = true;
            return this;
        }

        public Iterable<String> split(String input) {
            return splitToList(input);
        }
    }

    public static class ImmutableSet {
        public static <T> Set<T> copyOf(Set<T> values) {
            return Collections.unmodifiableSet(new LinkedHashSet(values));
        }
    }

    public static class ImmutableMap {
        public static <K, V> Map<K, V> copyOf(Map<K, V> values) {
            return Collections.unmodifiableMap(new LinkedHashMap(values));
        }
    }

    public static class ImmutableMultimap {
        public static <K, V> Multimap<K, V> copyOf(Multimap<K, V> multimap) {
            Set setUnmodifiableSet;
            LinkedHashMap linkedHashMap = new LinkedHashMap();
            for (Map.Entry<K, Set<V>> entry : multimap.asMap().entrySet()) {
                Set<V> value = entry.getValue();
                K key = entry.getKey();
                if (value.size() == 1) {
                    setUnmodifiableSet = Collections.singleton(value.iterator2().mo35924next());
                } else {
                    setUnmodifiableSet = Collections.unmodifiableSet(new LinkedHashSet(value));
                }
                linkedHashMap.put(key, setUnmodifiableSet);
            }
            return new Multimap<>(Collections.unmodifiableMap(linkedHashMap), null);
        }
    }

    public static class FileUtilities {
        public static final Charset UTF8 = Charset.forName("utf-8");

        public static BufferedReader openFile(Class<?> class1, String file) {
            return openFile(class1, file, UTF8);
        }

        public static BufferedReader openFile(Class<?> class1, String file, Charset charset) {
            try {
                InputStream resourceAsStream = class1.getResourceAsStream(file);
                if (charset == null) {
                    charset = UTF8;
                }
                InputStreamReader reader = new InputStreamReader(resourceAsStream, charset);
                BufferedReader bufferedReader = new BufferedReader(reader, 65536);
                return bufferedReader;
            } catch (Exception e) {
                String className = class1 == null ? null : class1.getCanonicalName();
                try {
                    String relativeFileName = getRelativeFileName(class1, "../util/");
                    String canonicalName = new File(relativeFileName).getCanonicalPath();
                    throw new ICUUncheckedIOException("Couldn't open file " + file + "; in path " + canonicalName + "; relative to class: " + className, e);
                } catch (Exception e2) {
                    throw new ICUUncheckedIOException("Couldn't open file: " + file + "; relative to class: " + className, e);
                }
            }
        }

        public static String getRelativeFileName(Class<?> class1, String filename) {
            URL resource = class1 == null ? FileUtilities.class.getResource(filename) : class1.getResource(filename);
            String resourceString = resource.toString();
            if (resourceString.startsWith("file:")) {
                return resourceString.substring(5);
            }
            if (resourceString.startsWith("jar:file:")) {
                return resourceString.substring(9);
            }
            throw new ICUUncheckedIOException("File not found: " + resourceString);
        }
    }

    public static class RegexUtilities {
        public static int findMismatch(Matcher m, CharSequence s) {
            int i = 1;
            while (i < s.length()) {
                boolean matches = m.reset(s.subSequence(0, i)).matches();
                if (!matches && !m.hitEnd()) {
                    break;
                }
                i++;
            }
            return i - 1;
        }

        public static String showMismatch(Matcher m, CharSequence s) {
            int failPoint = findMismatch(m, s);
            String show = ((Object) s.subSequence(0, failPoint)) + "☹" + ((Object) s.subSequence(failPoint, s.length()));
            return show;
        }
    }
}
