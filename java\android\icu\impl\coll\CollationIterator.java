package android.icu.impl.coll;

import android.icu.impl.Trie2_32;
import android.icu.util.BytesTrie;
import android.icu.util.CharsTrie;
import android.icu.util.ICUException;
import android.net.wifi.hotspot2.pps.UpdateParameter;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public abstract class CollationIterator {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    protected static final long NO_CP_AND_CE32 = -4294967104L;
    private CEBuffer ceBuffer;
    private int cesIndex;
    protected final CollationData data;
    private boolean isNumeric;
    private int numCpFwd;
    private SkippedState skipped;
    protected final Trie2_32 trie;

    protected abstract void backwardNumCodePoints(int i);

    protected abstract void forwardNumCodePoints(int i);

    public abstract int getOffset();

    public abstract int nextCodePoint();

    public abstract int previousCodePoint();

    public abstract void resetToOffset(int i);

    private static final class CEBuffer {
        private static final int INITIAL_CAPACITY = 40;
        int length = 0;
        private long[] buffer = new long[40];

        CEBuffer() {
        }

        void append(long ce) {
            if (this.length >= 40) {
                ensureAppendCapacity(1);
            }
            long[] jArr = this.buffer;
            int i = this.length;
            this.length = i + 1;
            jArr[i] = ce;
        }

        void appendUnsafe(long ce) {
            long[] jArr = this.buffer;
            int i = this.length;
            this.length = i + 1;
            jArr[i] = ce;
        }

        void ensureAppendCapacity(int appCap) {
            int i;
            int capacity = this.buffer.length;
            if (this.length + appCap <= capacity) {
                return;
            }
            do {
                if (capacity < 1000) {
                    capacity *= 4;
                } else {
                    capacity *= 2;
                }
                i = this.length;
            } while (capacity < i + appCap);
            long[] newBuffer = new long[capacity];
            System.arraycopy((Object) this.buffer, 0, (Object) newBuffer, 0, i);
            this.buffer = newBuffer;
        }

        void incLength() {
            if (this.length >= 40) {
                ensureAppendCapacity(1);
            }
            this.length++;
        }

        long set(int i, long ce) {
            this.buffer[i] = ce;
            return ce;
        }

        long get(int i) {
            return this.buffer[i];
        }

        long[] getCEs() {
            return this.buffer;
        }
    }

    private static final class SkippedState {
        static final /* synthetic */ boolean $assertionsDisabled = false;
        private int pos;
        private int skipLengthAtMatch;
        private final StringBuilder oldBuffer = new StringBuilder();
        private final StringBuilder newBuffer = new StringBuilder();
        private CharsTrie.State state = new CharsTrie.State();

        SkippedState() {
        }

        void clear() {
            this.oldBuffer.setLength(0);
            this.pos = 0;
        }

        boolean isEmpty() {
            return this.oldBuffer.length() == 0;
        }

        boolean hasNext() {
            return this.pos < this.oldBuffer.length();
        }

        int next() {
            int c2 = this.oldBuffer.codePointAt(this.pos);
            this.pos += Character.charCount(c2);
            return c2;
        }

        void incBeyond() {
            this.pos++;
        }

        int backwardNumCodePoints(int n) {
            int length = this.oldBuffer.length();
            int i = this.pos;
            int beyond = i - length;
            if (beyond > 0) {
                if (beyond >= n) {
                    this.pos = i - n;
                    return n;
                }
                this.pos = this.oldBuffer.offsetByCodePoints(length, beyond - n);
                return beyond;
            }
            this.pos = this.oldBuffer.offsetByCodePoints(i, -n);
            return 0;
        }

        void setFirstSkipped(int c2) {
            this.skipLengthAtMatch = 0;
            this.newBuffer.setLength(0);
            this.newBuffer.appendCodePoint(c2);
        }

        void skip(int c2) {
            this.newBuffer.appendCodePoint(c2);
        }

        void recordMatch() {
            this.skipLengthAtMatch = this.newBuffer.length();
        }

        void replaceMatch() {
            int oldLength = this.oldBuffer.length();
            if (this.pos > oldLength) {
                this.pos = oldLength;
            }
            this.oldBuffer.delete(0, this.pos).insert(0, (CharSequence) this.newBuffer, 0, this.skipLengthAtMatch);
            this.pos = 0;
        }

        void saveTrieState(CharsTrie trie) {
            trie.saveState(this.state);
        }

        void resetToTrieState(CharsTrie trie) {
            trie.resetToState(this.state);
        }
    }

    public CollationIterator(CollationData d2) {
        this.trie = d2.trie;
        this.data = d2;
        this.numCpFwd = -1;
        this.isNumeric = false;
        this.ceBuffer = null;
    }

    public CollationIterator(CollationData d2, boolean numeric) {
        this.trie = d2.trie;
        this.data = d2;
        this.numCpFwd = -1;
        this.isNumeric = numeric;
        this.ceBuffer = new CEBuffer();
    }

    public boolean equals(Object other) {
        if (other == null || !getClass().equals(other.getClass())) {
            return false;
        }
        CollationIterator o = (CollationIterator) other;
        if (this.ceBuffer.length != o.ceBuffer.length || this.cesIndex != o.cesIndex || this.numCpFwd != o.numCpFwd || this.isNumeric != o.isNumeric) {
            return false;
        }
        for (int i = 0; i < this.ceBuffer.length; i++) {
            if (this.ceBuffer.get(i) != o.ceBuffer.get(i)) {
                return false;
            }
        }
        return true;
    }

    public int hashCode() {
        return 0;
    }

    public final long nextCE() {
        CollationData d2;
        if (this.cesIndex < this.ceBuffer.length) {
            CEBuffer cEBuffer = this.ceBuffer;
            int i = this.cesIndex;
            this.cesIndex = i + 1;
            return cEBuffer.get(i);
        }
        this.ceBuffer.incLength();
        long cAndCE32 = handleNextCE32();
        int c2 = (int) (cAndCE32 >> 32);
        int ce32 = (int) cAndCE32;
        int t = ce32 & 255;
        if (t < 192) {
            CEBuffer cEBuffer2 = this.ceBuffer;
            int i2 = this.cesIndex;
            this.cesIndex = i2 + 1;
            return cEBuffer2.set(i2, ((ce32 & 65280) << 16) | (((-65536) & ce32) << 32) | (t << 8));
        }
        if (t == 192) {
            if (c2 < 0) {
                CEBuffer cEBuffer3 = this.ceBuffer;
                int i3 = this.cesIndex;
                this.cesIndex = i3 + 1;
                return cEBuffer3.set(i3, Collation.NO_CE);
            }
            d2 = this.data.base;
            ce32 = d2.getCE32(c2);
            t = ce32 & 255;
            if (t < 192) {
                CEBuffer cEBuffer4 = this.ceBuffer;
                int i4 = this.cesIndex;
                this.cesIndex = i4 + 1;
                return cEBuffer4.set(i4, ((ce32 & 65280) << 16) | (((-65536) & ce32) << 32) | (t << 8));
            }
        } else {
            d2 = this.data;
        }
        if (t == 193) {
            CEBuffer cEBuffer5 = this.ceBuffer;
            int i5 = this.cesIndex;
            this.cesIndex = i5 + 1;
            return cEBuffer5.set(i5, ((ce32 - t) << 32) | 83887360);
        }
        return nextCEFromCE32(d2, c2, ce32);
    }

    public final int fetchCEs() {
        while (nextCE() != Collation.NO_CE) {
            this.cesIndex = this.ceBuffer.length;
        }
        return this.ceBuffer.length;
    }

    final void setCurrentCE(long ce) {
        this.ceBuffer.set(this.cesIndex - 1, ce);
    }

    public final long previousCE(UVector32 offsets) {
        CollationData d2;
        if (this.ceBuffer.length > 0) {
            CEBuffer cEBuffer = this.ceBuffer;
            int i = cEBuffer.length - 1;
            cEBuffer.length = i;
            return cEBuffer.get(i);
        }
        offsets.removeAllElements();
        int limitOffset = getOffset();
        int c2 = previousCodePoint();
        if (c2 < 0) {
            return Collation.NO_CE;
        }
        if (this.data.isUnsafeBackward(c2, this.isNumeric)) {
            return previousCEUnsafe(c2, offsets);
        }
        int ce32 = this.data.getCE32(c2);
        if (ce32 == 192) {
            d2 = this.data.base;
            ce32 = d2.getCE32(c2);
        } else {
            d2 = this.data;
        }
        if (Collation.isSimpleOrLongCE32(ce32)) {
            return Collation.ceFromCE32(ce32);
        }
        appendCEsFromCE32(d2, c2, ce32, false);
        if (this.ceBuffer.length > 1) {
            offsets.addElement(getOffset());
            while (offsets.size() <= this.ceBuffer.length) {
                offsets.addElement(limitOffset);
            }
        }
        CEBuffer cEBuffer2 = this.ceBuffer;
        int i2 = cEBuffer2.length - 1;
        cEBuffer2.length = i2;
        return cEBuffer2.get(i2);
    }

    public final int getCEsLength() {
        return this.ceBuffer.length;
    }

    public final long getCE(int i) {
        return this.ceBuffer.get(i);
    }

    public final long[] getCEs() {
        return this.ceBuffer.getCEs();
    }

    final void clearCEs() {
        this.ceBuffer.length = 0;
        this.cesIndex = 0;
    }

    public final void clearCEsIfNoneRemaining() {
        if (this.cesIndex == this.ceBuffer.length) {
            clearCEs();
        }
    }

    protected final void reset() {
        this.ceBuffer.length = 0;
        this.cesIndex = 0;
        SkippedState skippedState = this.skipped;
        if (skippedState != null) {
            skippedState.clear();
        }
    }

    protected final void reset(boolean numeric) {
        if (this.ceBuffer == null) {
            this.ceBuffer = new CEBuffer();
        }
        reset();
        this.isNumeric = numeric;
    }

    protected long handleNextCE32() {
        int c2 = nextCodePoint();
        return c2 < 0 ? NO_CP_AND_CE32 : makeCodePointAndCE32Pair(c2, this.data.getCE32(c2));
    }

    protected long makeCodePointAndCE32Pair(int c2, int ce32) {
        return (c2 << 32) | (ce32 & UpdateParameter.UPDATE_CHECK_INTERVAL_NEVER);
    }

    protected char handleGetTrailSurrogate() {
        return (char) 0;
    }

    protected boolean forbidSurrogateCodePoints() {
        return false;
    }

    protected int getDataCE32(int c2) {
        return this.data.getCE32(c2);
    }

    protected int getCE32FromBuilderData(int ce32) {
        throw new ICUException("internal program error: should be unreachable");
    }

    /* JADX WARN: Removed duplicated region for block: B:138:0x0141 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:159:0x0000 A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    protected final void appendCEsFromCE32(android.icu.impl.coll.CollationData r12, int r13, int r14, boolean r15) {
        /*
            Method dump skipped, instructions count: 530
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationIterator.appendCEsFromCE32(android.icu.impl.coll.CollationData, int, int, boolean):void");
    }

    private static final boolean isSurrogate(int c2) {
        return (c2 & (-2048)) == 55296;
    }

    protected static final boolean isLeadSurrogate(int c2) {
        return (c2 & (-1024)) == 55296;
    }

    protected static final boolean isTrailSurrogate(int c2) {
        return (c2 & (-1024)) == 56320;
    }

    private final long nextCEFromCE32(CollationData d2, int c2, int ce32) {
        this.ceBuffer.length--;
        appendCEsFromCE32(d2, c2, ce32, true);
        CEBuffer cEBuffer = this.ceBuffer;
        int i = this.cesIndex;
        this.cesIndex = i + 1;
        return cEBuffer.get(i);
    }

    private final int getCE32FromPrefix(CollationData d2, int ce32) {
        BytesTrie.Result match;
        int index = Collation.indexFromCE32(ce32);
        int ce322 = d2.getCE32FromContexts(index);
        int lookBehind = 0;
        CharsTrie prefixes = new CharsTrie(d2.contexts, index + 2);
        do {
            int c2 = previousCodePoint();
            if (c2 < 0) {
                break;
            }
            lookBehind++;
            match = prefixes.nextForCodePoint(c2);
            if (match.hasValue()) {
                ce322 = prefixes.getValue();
            }
        } while (match.hasNext());
        forwardNumCodePoints(lookBehind);
        return ce322;
    }

    private final int nextSkippedCodePoint() {
        SkippedState skippedState = this.skipped;
        if (skippedState != null && skippedState.hasNext()) {
            return this.skipped.next();
        }
        if (this.numCpFwd == 0) {
            return -1;
        }
        int c2 = nextCodePoint();
        SkippedState skippedState2 = this.skipped;
        if (skippedState2 != null && !skippedState2.isEmpty() && c2 >= 0) {
            this.skipped.incBeyond();
        }
        int i = this.numCpFwd;
        if (i > 0 && c2 >= 0) {
            this.numCpFwd = i - 1;
        }
        return c2;
    }

    private final void backwardNumSkipped(int n) {
        SkippedState skippedState = this.skipped;
        if (skippedState != null && !skippedState.isEmpty()) {
            n = this.skipped.backwardNumCodePoints(n);
        }
        backwardNumCodePoints(n);
        int i = this.numCpFwd;
        if (i >= 0) {
            this.numCpFwd = i + n;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:21:0x0052, code lost:
    
        return r3;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private final int nextCE32FromContraction(android.icu.impl.coll.CollationData r17, int r18, java.lang.CharSequence r19, int r20, int r21, int r22) {
        /*
            r16 = this;
            r6 = r16
            r7 = r18
            r0 = 1
            r1 = 1
            android.icu.util.CharsTrie r2 = new android.icu.util.CharsTrie
            r8 = r19
            r9 = r20
            r2.<init>(r8, r9)
            r10 = r2
            android.icu.impl.coll.CollationIterator$SkippedState r2 = r6.skipped
            if (r2 == 0) goto L1f
            boolean r2 = r2.isEmpty()
            if (r2 != 0) goto L1f
            android.icu.impl.coll.CollationIterator$SkippedState r2 = r6.skipped
            r2.saveTrieState(r10)
        L1f:
            r2 = r22
            android.icu.util.BytesTrie$Result r3 = r10.firstForCodePoint(r2)
            r11 = r21
            r12 = r3
        L28:
            boolean r3 = r12.hasValue()
            if (r3 == 0) goto L53
            int r3 = r10.getValue()
            boolean r4 = r12.hasNext()
            if (r4 == 0) goto L52
            int r4 = r16.nextSkippedCodePoint()
            r2 = r4
            if (r4 >= 0) goto L40
            goto L52
        L40:
            android.icu.impl.coll.CollationIterator$SkippedState r4 = r6.skipped
            if (r4 == 0) goto L4f
            boolean r4 = r4.isEmpty()
            if (r4 != 0) goto L4f
            android.icu.impl.coll.CollationIterator$SkippedState r4 = r6.skipped
            r4.saveTrieState(r10)
        L4f:
            r1 = 1
            r11 = r3
            goto L62
        L52:
            return r3
        L53:
            android.icu.util.BytesTrie$Result r3 = android.icu.util.BytesTrie.Result.NO_MATCH
            if (r12 == r3) goto L69
            int r3 = r16.nextSkippedCodePoint()
            r4 = r3
            if (r3 >= 0) goto L5f
            goto L69
        L5f:
            r2 = r4
            int r1 = r1 + 1
        L62:
            int r0 = r0 + 1
            android.icu.util.BytesTrie$Result r12 = r10.nextForCodePoint(r2)
            goto L28
        L69:
            r3 = r7 & 1024(0x400, float:1.435E-42)
            if (r3 == 0) goto La2
            r3 = r7 & 256(0x100, float:3.59E-43)
            if (r3 == 0) goto L73
            if (r1 >= r0) goto La2
        L73:
            r3 = 1
            if (r1 <= r3) goto L85
            r6.backwardNumSkipped(r1)
            int r2 = r16.nextSkippedCodePoint()
            int r3 = r1 + (-1)
            int r0 = r0 - r3
            r1 = 1
            r13 = r0
            r14 = r1
            r15 = r2
            goto L88
        L85:
            r13 = r0
            r14 = r1
            r15 = r2
        L88:
            r5 = r17
            int r0 = r5.getFCD16(r15)
            r1 = 255(0xff, float:3.57E-43)
            if (r0 <= r1) goto L9f
            r0 = r16
            r1 = r17
            r2 = r10
            r3 = r11
            r4 = r13
            r5 = r15
            int r0 = r0.nextCE32FromDiscontiguousContraction(r1, r2, r3, r4, r5)
            return r0
        L9f:
            r0 = r13
            r1 = r14
            r2 = r15
        La2:
            r6.backwardNumSkipped(r1)
            return r11
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationIterator.nextCE32FromContraction(android.icu.impl.coll.CollationData, int, java.lang.CharSequence, int, int, int):int");
    }

    /* JADX WARN: Removed duplicated region for block: B:35:0x008d  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private final int nextCE32FromDiscontiguousContraction(android.icu.impl.coll.CollationData r9, android.icu.util.CharsTrie r10, int r11, int r12, int r13) {
        /*
            Method dump skipped, instructions count: 242
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationIterator.nextCE32FromDiscontiguousContraction(android.icu.impl.coll.CollationData, android.icu.util.CharsTrie, int, int, int):int");
    }

    private final long previousCEUnsafe(int c2, UVector32 offsets) {
        int c3;
        int numBackward = 1;
        do {
            c3 = previousCodePoint();
            if (c3 < 0) {
                break;
            }
            numBackward++;
        } while (this.data.isUnsafeBackward(c3, this.isNumeric));
        this.numCpFwd = numBackward;
        this.cesIndex = 0;
        int offset = getOffset();
        while (true) {
            int i = this.numCpFwd;
            if (i > 0) {
                this.numCpFwd = i - 1;
                nextCE();
                this.cesIndex = this.ceBuffer.length;
                offsets.addElement(offset);
                offset = getOffset();
                while (offsets.size() < this.ceBuffer.length) {
                    offsets.addElement(offset);
                }
            } else {
                offsets.addElement(offset);
                this.numCpFwd = -1;
                backwardNumCodePoints(numBackward);
                this.cesIndex = 0;
                CEBuffer cEBuffer = this.ceBuffer;
                int i2 = cEBuffer.length - 1;
                cEBuffer.length = i2;
                return cEBuffer.get(i2);
            }
        }
    }

    private final void appendNumericCEs(int ce32, boolean forward) {
        int c2;
        StringBuilder digits = new StringBuilder();
        if (forward) {
            while (true) {
                char digit = Collation.digitFromCE32(ce32);
                digits.append(digit);
                if (this.numCpFwd == 0 || (c2 = nextCodePoint()) < 0) {
                    break;
                }
                ce32 = this.data.getCE32(c2);
                if (ce32 == 192) {
                    ce32 = this.data.base.getCE32(c2);
                }
                if (!Collation.hasCE32Tag(ce32, 10)) {
                    backwardNumCodePoints(1);
                    break;
                } else {
                    int i = this.numCpFwd;
                    if (i > 0) {
                        this.numCpFwd = i - 1;
                    }
                }
            }
        } else {
            while (true) {
                char digit2 = Collation.digitFromCE32(ce32);
                digits.append(digit2);
                int c3 = previousCodePoint();
                if (c3 < 0) {
                    break;
                }
                ce32 = this.data.getCE32(c3);
                if (ce32 == 192) {
                    ce32 = this.data.base.getCE32(c3);
                }
                if (!Collation.hasCE32Tag(ce32, 10)) {
                    forwardNumCodePoints(1);
                    break;
                }
            }
            digits.reverse();
        }
        int pos = 0;
        while (true) {
            if (pos >= digits.length() - 1 || digits.charAt(pos) != 0) {
                int segmentLength = digits.length() - pos;
                if (segmentLength > 254) {
                    segmentLength = 254;
                }
                appendNumericSegmentCEs(digits.subSequence(pos, pos + segmentLength));
                pos += segmentLength;
                if (pos >= digits.length()) {
                    return;
                }
            } else {
                pos++;
            }
        }
    }

    private final void appendNumericSegmentCEs(CharSequence digits) {
        int pair;
        int pos;
        int length = digits.length();
        long numericPrimary = this.data.numericPrimary;
        if (length <= 7) {
            int value = digits.charAt(0);
            for (int i = 1; i < length; i++) {
                value = (value * 10) + digits.charAt(i);
            }
            if (value < 74) {
                long primary = ((2 + value) << 16) | numericPrimary;
                this.ceBuffer.append(Collation.makeCE(primary));
                return;
            }
            int value2 = value - 74;
            int firstByte = 2 + 74;
            if (value2 < 40 * 254) {
                long primary2 = (((value2 / 254) + firstByte) << 16) | numericPrimary | (((value2 % 254) + 2) << 8);
                this.ceBuffer.append(Collation.makeCE(primary2));
                return;
            }
            int value3 = value2 - (40 * 254);
            int firstByte2 = firstByte + 40;
            if (value3 < 16 * 254 * 254) {
                long primary3 = ((value3 % 254) + 2) | numericPrimary;
                int value4 = value3 / 254;
                this.ceBuffer.append(Collation.makeCE(primary3 | (((value4 % 254) + 2) << 8) | ((((value4 / 254) % 254) + firstByte2) << 16)));
                return;
            }
        }
        int numPairs = (length + 1) / 2;
        long primary4 = ((numPairs + 128) << 16) | numericPrimary;
        while (digits.charAt(length - 1) == 0 && digits.charAt(length - 2) == 0) {
            length -= 2;
        }
        if ((length & 1) != 0) {
            pair = digits.charAt(0);
            pos = 1;
        } else {
            int pair2 = digits.charAt(0);
            pair = (pair2 * 10) + digits.charAt(1);
            pos = 2;
        }
        int pair3 = (pair * 2) + 11;
        int shift = 8;
        while (pos < length) {
            if (shift == 0) {
                this.ceBuffer.append(Collation.makeCE(primary4 | pair3));
                primary4 = numericPrimary;
                shift = 16;
            } else {
                primary4 |= pair3 << shift;
                shift -= 8;
            }
            pair3 = (((digits.charAt(pos) * '\n') + digits.charAt(pos + 1)) * 2) + 11;
            pos += 2;
        }
        this.ceBuffer.append(Collation.makeCE(primary4 | ((pair3 - 1) << shift)));
    }
}
