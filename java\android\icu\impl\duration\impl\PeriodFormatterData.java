package android.icu.impl.duration.impl;

import android.icu.impl.duration.TimeUnit;
import android.icu.impl.duration.impl.DataRecord;
import android.icu.impl.duration.impl.Utils;
import java.util.Arrays;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class PeriodFormatterData {
    private static final int FORM_DUAL = 2;
    private static final int FORM_HALF_SPELLED = 6;
    private static final int FORM_PAUCAL = 3;
    private static final int FORM_PLURAL = 0;
    private static final int FORM_SINGULAR = 1;
    private static final int FORM_SINGULAR_NO_OMIT = 5;
    private static final int FORM_SINGULAR_SPELLED = 4;
    public static boolean trace = false;

    /* renamed from: dr */
    final DataRecord f74dr;
    String localeName;

    public PeriodFormatterData(String localeName, DataRecord dr) {
        this.f74dr = dr;
        this.localeName = localeName;
        if (localeName == null) {
            throw new NullPointerException("localename is null");
        }
        if (dr == null) {
            throw new NullPointerException("data record is null");
        }
    }

    public int pluralization() {
        return this.f74dr.f66pl;
    }

    public boolean allowZero() {
        return this.f74dr.allowZero;
    }

    public boolean weeksAloneOnly() {
        return this.f74dr.weeksAloneOnly;
    }

    public int useMilliseconds() {
        return this.f74dr.useMilliseconds;
    }

    public boolean appendPrefix(int tl, int td, StringBuffer sb) {
        String prefix;
        if (this.f74dr.scopeData != null) {
            int ix = (tl * 3) + td;
            DataRecord.ScopeData sd = this.f74dr.scopeData[ix];
            if (sd != null && (prefix = sd.prefix) != null) {
                sb.append(prefix);
                return sd.requiresDigitPrefix;
            }
            return false;
        }
        return false;
    }

    public void appendSuffix(int tl, int td, StringBuffer sb) {
        String suffix;
        if (this.f74dr.scopeData != null) {
            int ix = (tl * 3) + td;
            DataRecord.ScopeData sd = this.f74dr.scopeData[ix];
            if (sd != null && (suffix = sd.suffix) != null) {
                if (trace) {
                    System.out.println("appendSuffix '" + suffix + "'");
                }
                sb.append(suffix);
            }
        }
    }

    public boolean appendUnit(TimeUnit unit, int count, int cv, int uv, boolean useCountSep, boolean useDigitPrefix, boolean multiple, boolean last, boolean wasSkipped, StringBuffer sb) {
        boolean willRequireSkipMarker;
        int count2;
        int cv2;
        String name;
        String name2;
        int form;
        byte b2;
        String[] names;
        int px = unit.ordinal();
        if (this.f74dr.requiresSkipMarker != null && this.f74dr.requiresSkipMarker[px] && this.f74dr.skippedUnitMarker != null) {
            if (!wasSkipped && last) {
                sb.append(this.f74dr.skippedUnitMarker);
            }
            willRequireSkipMarker = true;
        } else {
            willRequireSkipMarker = false;
        }
        if (uv != 0) {
            boolean useMedium = uv == 1;
            DataRecord dataRecord = this.f74dr;
            String[] names2 = useMedium ? dataRecord.mediumNames : dataRecord.shortNames;
            if (names2 == null || names2[px] == null) {
                DataRecord dataRecord2 = this.f74dr;
                names = useMedium ? dataRecord2.shortNames : dataRecord2.mediumNames;
            } else {
                names = names2;
            }
            if (names != null && names[px] != null) {
                appendCount(unit, false, false, count, cv, useCountSep, names[px], last, sb);
                return false;
            }
        }
        if (cv == 2 && this.f74dr.halfSupport != null && ((b2 = this.f74dr.halfSupport[px]) == 1 || (b2 == 2 && count <= 1000))) {
            count2 = (count / 500) * 500;
            cv2 = 3;
        } else {
            count2 = count;
            cv2 = cv;
        }
        int form2 = computeForm(unit, count2, cv2, multiple && last);
        if (form2 == 4) {
            if (this.f74dr.singularNames == null) {
                form2 = 1;
                name = this.f74dr.pluralNames[px][1];
            } else {
                name = this.f74dr.singularNames[px];
            }
        } else if (form2 == 5) {
            name = this.f74dr.pluralNames[px][1];
        } else if (form2 == 6) {
            name = this.f74dr.halfNames[px];
        } else {
            try {
                name = this.f74dr.pluralNames[px][form2];
            } catch (NullPointerException e) {
                System.out.println("Null Pointer in PeriodFormatterData[" + this.localeName + "].au px: " + px + " form: " + form2 + " pn: " + Arrays.toString(this.f74dr.pluralNames));
                throw e;
            }
        }
        if (name != null) {
            name2 = name;
            form = form2;
        } else {
            String name3 = this.f74dr.pluralNames[px][0];
            name2 = name3;
            form = 0;
        }
        boolean omitCount = form == 4 || form == 6 || (this.f74dr.omitSingularCount && form == 1) || (this.f74dr.omitDualCount && form == 2);
        int suffixIndex = appendCount(unit, omitCount, useDigitPrefix, count2, cv2, useCountSep, name2, last, sb);
        if (last && suffixIndex >= 0) {
            String suffix = null;
            if (this.f74dr.rqdSuffixes != null && suffixIndex < this.f74dr.rqdSuffixes.length) {
                suffix = this.f74dr.rqdSuffixes[suffixIndex];
            }
            if (suffix == null && this.f74dr.optSuffixes != null && suffixIndex < this.f74dr.optSuffixes.length) {
                suffix = this.f74dr.optSuffixes[suffixIndex];
            }
            if (suffix != null) {
                sb.append(suffix);
            }
        }
        return willRequireSkipMarker;
    }

    /* JADX WARN: Removed duplicated region for block: B:113:0x013d  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public int appendCount(android.icu.impl.duration.TimeUnit r15, boolean r16, boolean r17, int r18, int r19, boolean r20, java.lang.String r21, boolean r22, java.lang.StringBuffer r23) {
        /*
            Method dump skipped, instructions count: 375
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.duration.impl.PeriodFormatterData.appendCount(android.icu.impl.duration.TimeUnit, boolean, boolean, int, int, boolean, java.lang.String, boolean, java.lang.StringBuffer):int");
    }

    public void appendCountValue(int count, int integralDigits, int decimalDigits, StringBuffer sb) {
        int ival = count / 1000;
        if (decimalDigits == 0) {
            appendInteger(ival, integralDigits, 10, sb);
            return;
        }
        if (this.f74dr.requiresDigitSeparator && sb.length() > 0) {
            sb.append(' ');
        }
        appendDigits(ival, integralDigits, 10, sb);
        int dval = count % 1000;
        if (decimalDigits == 1) {
            dval /= 100;
        } else if (decimalDigits == 2) {
            dval /= 10;
        }
        sb.append(this.f74dr.decimalSep);
        appendDigits(dval, decimalDigits, decimalDigits, sb);
        if (this.f74dr.requiresDigitSeparator) {
            sb.append(' ');
        }
    }

    public void appendInteger(int num, int mindigits, int maxdigits, StringBuffer sb) {
        String name;
        if (this.f74dr.numberNames != null && num < this.f74dr.numberNames.length && (name = this.f74dr.numberNames[num]) != null) {
            sb.append(name);
            return;
        }
        if (this.f74dr.requiresDigitSeparator && sb.length() > 0) {
            sb.append(' ');
        }
        byte b2 = this.f74dr.numberSystem;
        if (b2 == 0) {
            appendDigits(num, mindigits, maxdigits, sb);
        } else if (b2 == 1) {
            sb.append(Utils.chineseNumber(num, Utils.ChineseDigits.TRADITIONAL));
        } else if (b2 == 2) {
            sb.append(Utils.chineseNumber(num, Utils.ChineseDigits.SIMPLIFIED));
        } else if (b2 == 3) {
            sb.append(Utils.chineseNumber(num, Utils.ChineseDigits.KOREAN));
        }
        if (this.f74dr.requiresDigitSeparator) {
            sb.append(' ');
        }
    }

    public void appendDigits(long num, int mindigits, int maxdigits, StringBuffer sb) {
        char[] buf = new char[maxdigits];
        int ix = maxdigits;
        while (ix > 0 && num > 0) {
            ix--;
            buf[ix] = (char) (this.f74dr.zero + (num % 10));
            num /= 10;
        }
        int e = maxdigits - mindigits;
        while (ix > e) {
            ix--;
            buf[ix] = this.f74dr.zero;
        }
        int e2 = maxdigits - ix;
        sb.append(buf, ix, e2);
    }

    public void appendSkippedUnit(StringBuffer sb) {
        if (this.f74dr.skippedUnitMarker != null) {
            sb.append(this.f74dr.skippedUnitMarker);
        }
    }

    public boolean appendUnitSeparator(TimeUnit timeUnit, boolean z, boolean z2, boolean z3, StringBuffer stringBuffer) {
        if ((z && this.f74dr.unitSep != null) || this.f74dr.shortUnitSep != null) {
            if (z && this.f74dr.unitSep != null) {
                int i = (z2 ? 2 : 0) + (z3 ? 1 : 0);
                stringBuffer.append(this.f74dr.unitSep[i]);
                return this.f74dr.unitSepRequiresDP != null && this.f74dr.unitSepRequiresDP[i];
            }
            stringBuffer.append(this.f74dr.shortUnitSep);
        }
        return false;
    }

    private int computeForm(TimeUnit unit, int count, int cv, boolean lastOfMultiple) {
        if (trace) {
            System.err.println("pfd.cf unit: " + ((Object) unit) + " count: " + count + " cv: " + cv + " dr.pl: " + ((int) this.f74dr.f66pl));
            Thread.dumpStack();
        }
        if (this.f74dr.f66pl == 0) {
            return 0;
        }
        int val = count / 1000;
        if (cv != 0 && cv != 1) {
            if (cv == 2) {
                byte b2 = this.f74dr.fractionHandling;
                if (b2 == 0) {
                    return 0;
                }
                if (b2 == 1 || b2 == 2) {
                    int v = count / 500;
                    if (v == 1) {
                        if (this.f74dr.halfNames == null || this.f74dr.halfNames[unit.ordinal()] == null) {
                            return 5;
                        }
                        return 6;
                    }
                    if ((v & 1) == 1) {
                        if (this.f74dr.f66pl == 5 && v > 21) {
                            return 5;
                        }
                        if (v == 3 && this.f74dr.f66pl == 1 && this.f74dr.fractionHandling != 2) {
                            return 0;
                        }
                    }
                } else if (b2 == 3) {
                    int v2 = count / 500;
                    if (v2 == 1 || v2 == 3) {
                        return 3;
                    }
                } else {
                    throw new IllegalStateException();
                }
            } else {
                byte b3 = this.f74dr.decimalHandling;
                if (b3 == 1) {
                    return 5;
                }
                if (b3 == 2) {
                    if (count < 1000) {
                        return 5;
                    }
                } else if (b3 == 3 && this.f74dr.f66pl == 3) {
                    return 3;
                }
                return 0;
            }
        }
        if (trace && count == 0) {
            System.err.println("EZeroHandling = " + ((int) this.f74dr.zeroHandling));
        }
        if (count == 0 && this.f74dr.zeroHandling == 1) {
            return 4;
        }
        byte b4 = this.f74dr.f66pl;
        if (b4 == 0) {
            return 0;
        }
        if (b4 == 1) {
            if (val != 1) {
                return 0;
            }
            return 4;
        }
        if (b4 == 2) {
            if (val == 2) {
                return 2;
            }
            if (val != 1) {
                return 0;
            }
            return 1;
        }
        if (b4 != 3) {
            if (b4 != 4) {
                if (b4 == 5) {
                    if (val == 2) {
                        return 2;
                    }
                    if (val == 1) {
                        return 1;
                    }
                    if (val <= 10) {
                        return 0;
                    }
                    return 5;
                }
                System.err.println("dr.pl is " + ((int) this.f74dr.f66pl));
                throw new IllegalStateException();
            }
            if (val == 2) {
                return 2;
            }
            if (val == 1) {
                if (lastOfMultiple) {
                    return 4;
                }
                return 1;
            }
            if (unit != TimeUnit.YEAR || val <= 11) {
                return 0;
            }
            return 5;
        }
        int v3 = val % 100;
        if (v3 > 20) {
            v3 %= 10;
        }
        if (v3 == 1) {
            return 1;
        }
        if (v3 <= 1 || v3 >= 5) {
            return 0;
        }
        return 3;
    }
}
