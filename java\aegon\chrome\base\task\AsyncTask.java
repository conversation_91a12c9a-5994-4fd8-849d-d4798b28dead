package aegon.chrome.base.task;

import aegon.chrome.base.Log;
import aegon.chrome.base.ThreadUtils;
import aegon.chrome.base.TraceEvent;
import android.os.Binder;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.concurrent.Callable;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.FutureTask;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public abstract class AsyncTask<Result> {
    private static final String TAG = "AsyncTask";
    public static final Executor THREAD_POOL_EXECUTOR = new Executor() { // from class: aegon.chrome.base.task.-$$Lambda$AsyncTask$E_ZYZ6xf2RIcAHzkBnzdg6PotaM
        @Override // java.util.concurrent.Executor
        public final void execute(Runnable runnable) {
            PostTask.postTask(TaskTraits.BEST_EFFORT_MAY_BLOCK, runnable);
        }
    };
    public static final Executor SERIAL_EXECUTOR = new SerialExecutor();
    private static final StealRunnableHandler STEAL_RUNNABLE_HANDLER = new StealRunnableHandler();
    private volatile int mStatus = 0;
    private final AtomicBoolean mCancelled = new AtomicBoolean();
    private final AtomicBoolean mTaskInvoked = new AtomicBoolean();
    private final Callable<Result> mWorker = new Callable<Result>() { // from class: aegon.chrome.base.task.AsyncTask.1
        @Override // java.util.concurrent.Callable
        public Result call() {
            AsyncTask.this.mTaskInvoked.set(true);
            Result result = null;
            try {
                result = (Result) AsyncTask.this.doInBackground();
                Binder.flushPendingCommands();
                return result;
            } finally {
            }
        }
    };
    private final AsyncTask<Result>.NamedFutureTask mFuture = new NamedFutureTask(this.mWorker);

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    @Retention(RetentionPolicy.SOURCE)
    public @interface Status {
        public static final int FINISHED = 2;
        public static final int PENDING = 0;
        public static final int RUNNING = 1;
    }

    protected abstract Result doInBackground();

    protected void onCancelled() {
    }

    protected abstract void onPostExecute(Result result);

    protected void onPreExecute() {
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static class StealRunnableHandler implements RejectedExecutionHandler {
        private StealRunnableHandler() {
        }

        @Override // java.util.concurrent.RejectedExecutionHandler
        public void rejectedExecution(Runnable runnable, ThreadPoolExecutor threadPoolExecutor) {
            AsyncTask.THREAD_POOL_EXECUTOR.execute(runnable);
        }
    }

    public static void takeOverAndroidThreadPool() {
        ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) android.os.AsyncTask.THREAD_POOL_EXECUTOR;
        threadPoolExecutor.setRejectedExecutionHandler(STEAL_RUNNABLE_HANDLER);
        threadPoolExecutor.shutdown();
    }

    private void postResultIfNotInvoked(Result result) {
        if (this.mTaskInvoked.get()) {
            return;
        }
        postResult(result);
    }

    private void postResult(final Result result) {
        if (this instanceof BackgroundOnlyAsyncTask) {
            this.mStatus = 2;
        } else {
            ThreadUtils.postOnUiThread(new Runnable() { // from class: aegon.chrome.base.task.-$$Lambda$AsyncTask$FUyji_hDvCrBWjHWZ6PfvRsijsg
                @Override // java.lang.Runnable
                public final void run() {
                    this.f$0.lambda$postResult$1$AsyncTask(result);
                }
            });
        }
    }

    public final int getStatus() {
        return this.mStatus;
    }

    protected void onCancelled(Result result) {
        onCancelled();
    }

    public final boolean isCancelled() {
        return this.mCancelled.get();
    }

    public final boolean cancel(boolean z) {
        this.mCancelled.set(true);
        return this.mFuture.cancel(z);
    }

    public final Result get() {
        String str;
        if (getStatus() != 2 && ThreadUtils.runningOnUiThread()) {
            StackTraceElement[] stackTrace = new Exception().getStackTrace();
            if (stackTrace.length > 1) {
                str = stackTrace[1].getClassName() + '.' + stackTrace[1].getMethodName() + '.';
            } else {
                str = "";
            }
            TraceEvent traceEventScoped = TraceEvent.scoped(str + "AsyncTask.get");
            try {
                Result result = this.mFuture.get();
                if (traceEventScoped == null) {
                    return result;
                }
                traceEventScoped.close();
                return result;
            } catch (Throwable th) {
                if (traceEventScoped != null) {
                    try {
                        traceEventScoped.close();
                    } catch (Throwable unused) {
                    }
                }
                throw th;
            }
        }
        return this.mFuture.get();
    }

    public final Result get(long j, TimeUnit timeUnit) {
        String str;
        if (getStatus() != 2 && ThreadUtils.runningOnUiThread()) {
            StackTraceElement[] stackTrace = new Exception().getStackTrace();
            if (stackTrace.length > 1) {
                str = stackTrace[1].getClassName() + '.' + stackTrace[1].getMethodName() + '.';
            } else {
                str = "";
            }
            TraceEvent traceEventScoped = TraceEvent.scoped(str + "AsyncTask.get");
            try {
                Result result = this.mFuture.get(j, timeUnit);
                if (traceEventScoped == null) {
                    return result;
                }
                traceEventScoped.close();
                return result;
            } catch (Throwable th) {
                if (traceEventScoped != null) {
                    try {
                        traceEventScoped.close();
                    } catch (Throwable unused) {
                    }
                }
                throw th;
            }
        }
        return this.mFuture.get(j, timeUnit);
    }

    private void executionPreamble() {
        if (this.mStatus != 0) {
            int i = this.mStatus;
            if (i == 1) {
                throw new IllegalStateException("Cannot execute task: the task is already running.");
            }
            if (i == 2) {
                throw new IllegalStateException("Cannot execute task: the task has already been executed (a task can be executed only once)");
            }
        }
        this.mStatus = 1;
        onPreExecute();
    }

    public final AsyncTask<Result> executeOnExecutor(Executor executor) {
        executionPreamble();
        executor.execute(this.mFuture);
        return this;
    }

    public final AsyncTask<Result> executeOnTaskRunner(TaskRunner taskRunner) {
        executionPreamble();
        taskRunner.postTask(this.mFuture);
        return this;
    }

    public final AsyncTask<Result> executeWithTaskTraits(TaskTraits taskTraits) {
        executionPreamble();
        PostTask.postTask(taskTraits, this.mFuture);
        return this;
    }

    /* renamed from: finish, reason: merged with bridge method [inline-methods] */
    private void lambda$postResult$1$AsyncTask(Result result) {
        if (isCancelled()) {
            onCancelled(result);
        } else {
            onPostExecute(result);
        }
        this.mStatus = 2;
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    class NamedFutureTask extends FutureTask<Result> {
        NamedFutureTask(Callable<Result> callable) {
            super(callable);
        }

        Class getBlamedClass() {
            return AsyncTask.this.getClass();
        }

        @Override // java.util.concurrent.FutureTask, java.util.concurrent.RunnableFuture, java.lang.Runnable
        public void run() {
            TraceEvent traceEventScoped = TraceEvent.scoped("AsyncTask.run: " + ((Object) AsyncTask.this.mFuture.getBlamedClass()));
            try {
                super.run();
                if (traceEventScoped != null) {
                    traceEventScoped.close();
                }
            } catch (Throwable th) {
                if (traceEventScoped != null) {
                    try {
                        traceEventScoped.close();
                    } catch (Throwable unused) {
                    }
                }
                throw th;
            }
        }

        @Override // java.util.concurrent.FutureTask
        protected void done() {
            try {
                AsyncTask.this.postResultIfNotInvoked(get());
            } catch (InterruptedException e) {
                Log.m46w(AsyncTask.TAG, e.toString(), new Object[0]);
            } catch (CancellationException unused) {
                AsyncTask.this.postResultIfNotInvoked(null);
            } catch (ExecutionException e2) {
                throw new RuntimeException("An error occurred while executing doInBackground()", e2.getCause());
            }
        }
    }
}
