package aegon.chrome.base;

import aegon.chrome.base.Features;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class FeaturesJni implements Features.Natives {
    public static final JniStaticTestMocker<Features.Natives> TEST_HOOKS = new JniStaticTestMocker<Features.Natives>() { // from class: aegon.chrome.base.FeaturesJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(Features.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                Features.Natives unused = FeaturesJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static Features.Natives testInstance;

    FeaturesJni() {
    }

    @Override // aegon.chrome.base.Features.Natives
    public boolean isEnabled(long j) {
        return GEN_JNI.org_chromium_base_Features_isEnabled(j);
    }

    @Override // aegon.chrome.base.Features.Natives
    public boolean getFieldTrialParamByFeatureAsBoolean(long j, String str, boolean z) {
        return GEN_JNI.org_chromium_base_Features_getFieldTrialParamByFeatureAsBoolean(j, str, z);
    }

    public static Features.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            Features.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.Features.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new FeaturesJni();
    }
}
