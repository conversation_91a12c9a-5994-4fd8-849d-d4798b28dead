package aegon.chrome.base.compat;

import android.content.Context;
import android.graphics.Rect;
import android.hardware.input.InputManager;
import android.net.Uri;
import android.os.storage.StorageManager;
import android.view.Display;
import android.view.InputEvent;
import android.view.VerifiedInputEvent;
import android.view.WindowManager;
import java.p654io.File;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class ApiHelperForR {
    private ApiHelperForR() {
    }

    public static Display getDisplay(Context context) {
        return context.getDisplay();
    }

    public static File getVolumeDir(StorageManager storageManager, Uri uri) {
        return storageManager.getStorageVolume(uri).getDirectory();
    }

    public static VerifiedInputEvent verifyInputEvent(InputManager inputManager, InputEvent inputEvent) {
        return inputManager.verifyInputEvent(inputEvent);
    }

    public static Rect getMaximumWindowMetricsBounds(WindowManager windowManager) {
        return windowManager.getMaximumWindowMetrics().getBounds();
    }
}
