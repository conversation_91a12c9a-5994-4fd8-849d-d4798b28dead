package android.app;

import android.annotation.SystemApi;
import android.content.Context;
import android.os.Binder;
import android.os.RemoteException;
import android.p012os.IPullAtomCallback;
import android.p012os.IPullAtomResultReceiver;
import android.p012os.IStatsManagerService;
import android.p012os.StatsFrameworkInitializer;
import android.util.AndroidException;
import android.util.Log;
import android.util.StatsEvent;
import android.util.StatsEventParcel;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

@SystemApi
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes86.dex */
public final class StatsManager {
    public static final String ACTION_STATSD_STARTED = "android.app.action.STATSD_STARTED";
    private static final boolean DEBUG = false;
    public static final long DEFAULT_COOL_DOWN_MILLIS = 1000;
    public static final long DEFAULT_TIMEOUT_MILLIS = 2000;
    public static final String EXTRA_STATS_ACTIVE_CONFIG_KEYS = "android.app.extra.STATS_ACTIVE_CONFIG_KEYS";
    public static final String EXTRA_STATS_BROADCAST_SUBSCRIBER_COOKIES = "android.app.extra.STATS_BROADCAST_SUBSCRIBER_COOKIES";
    public static final String EXTRA_STATS_CONFIG_KEY = "android.app.extra.STATS_CONFIG_KEY";
    public static final String EXTRA_STATS_CONFIG_UID = "android.app.extra.STATS_CONFIG_UID";
    public static final String EXTRA_STATS_DIMENSIONS_VALUE = "android.app.extra.STATS_DIMENSIONS_VALUE";
    public static final String EXTRA_STATS_SUBSCRIPTION_ID = "android.app.extra.STATS_SUBSCRIPTION_ID";
    public static final String EXTRA_STATS_SUBSCRIPTION_RULE_ID = "android.app.extra.STATS_SUBSCRIPTION_RULE_ID";
    public static final int PULL_SKIP = 1;
    public static final int PULL_SUCCESS = 0;
    private static final String TAG = "StatsManager";
    private static final Object sLock = new Object();
    private final Context mContext;
    private IStatsManagerService mStatsManagerService;

    public interface StatsPullAtomCallback {
        int onPullAtom(int i, List<StatsEvent> list);
    }

    public StatsManager(Context context) {
        this.mContext = context;
    }

    public void addConfig(long configKey, byte[] config) throws StatsUnavailableException {
        synchronized (sLock) {
            try {
                try {
                    try {
                        IStatsManagerService service = getIStatsManagerServiceLocked();
                        service.addConfiguration(configKey, config, this.mContext.getOpPackageName());
                    } catch (RemoteException e) {
                        Log.e(TAG, "Failed to connect to statsmanager when adding configuration");
                        throw new StatsUnavailableException("could not connect", e);
                    } catch (SecurityException e2) {
                        throw new StatsUnavailableException(e2.getMessage(), e2);
                    }
                } catch (IllegalStateException e3) {
                    Log.e(TAG, "Failed to addConfig in statsmanager");
                    throw new StatsUnavailableException(e3.getMessage(), e3);
                }
            } catch (Throwable th) {
                throw th;
            }
        }
    }

    @Deprecated
    public boolean addConfiguration(long configKey, byte[] config) {
        try {
            addConfig(configKey, config);
            return true;
        } catch (StatsUnavailableException | IllegalArgumentException e) {
            return false;
        }
    }

    public void removeConfig(long configKey) throws StatsUnavailableException {
        synchronized (sLock) {
            try {
                try {
                    try {
                        IStatsManagerService service = getIStatsManagerServiceLocked();
                        service.removeConfiguration(configKey, this.mContext.getOpPackageName());
                    } catch (RemoteException e) {
                        Log.e(TAG, "Failed to connect to statsmanager when removing configuration");
                        throw new StatsUnavailableException("could not connect", e);
                    } catch (SecurityException e2) {
                        throw new StatsUnavailableException(e2.getMessage(), e2);
                    }
                } catch (IllegalStateException e3) {
                    Log.e(TAG, "Failed to removeConfig in statsmanager");
                    throw new StatsUnavailableException(e3.getMessage(), e3);
                }
            } catch (Throwable th) {
                throw th;
            }
        }
    }

    @Deprecated
    public boolean removeConfiguration(long configKey) {
        try {
            removeConfig(configKey);
            return true;
        } catch (StatsUnavailableException e) {
            return false;
        }
    }

    public void setBroadcastSubscriber(PendingIntent pendingIntent, long configKey, long subscriberId) throws StatsUnavailableException {
        synchronized (sLock) {
            try {
                try {
                    IStatsManagerService service = getIStatsManagerServiceLocked();
                    if (pendingIntent != null) {
                        service.setBroadcastSubscriber(configKey, subscriberId, pendingIntent, this.mContext.getOpPackageName());
                    } else {
                        service.unsetBroadcastSubscriber(configKey, subscriberId, this.mContext.getOpPackageName());
                    }
                } catch (RemoteException e) {
                    Log.e(TAG, "Failed to connect to statsmanager when adding broadcast subscriber", e);
                    throw new StatsUnavailableException("could not connect", e);
                } catch (SecurityException e2) {
                    throw new StatsUnavailableException(e2.getMessage(), e2);
                }
            } catch (Throwable th) {
                throw th;
            }
        }
    }

    @Deprecated
    public boolean setBroadcastSubscriber(long configKey, long subscriberId, PendingIntent pendingIntent) {
        try {
            setBroadcastSubscriber(pendingIntent, configKey, subscriberId);
            return true;
        } catch (StatsUnavailableException e) {
            return false;
        }
    }

    public void setFetchReportsOperation(PendingIntent pendingIntent, long configKey) throws StatsUnavailableException {
        synchronized (sLock) {
            try {
                try {
                    IStatsManagerService service = getIStatsManagerServiceLocked();
                    if (pendingIntent == null) {
                        service.removeDataFetchOperation(configKey, this.mContext.getOpPackageName());
                    } else {
                        service.setDataFetchOperation(configKey, pendingIntent, this.mContext.getOpPackageName());
                    }
                } catch (RemoteException e) {
                    Log.e(TAG, "Failed to connect to statsmanager when registering data listener.");
                    throw new StatsUnavailableException("could not connect", e);
                } catch (SecurityException e2) {
                    throw new StatsUnavailableException(e2.getMessage(), e2);
                }
            } catch (Throwable th) {
                throw th;
            }
        }
    }

    public long[] setActiveConfigsChangedOperation(PendingIntent pendingIntent) throws StatsUnavailableException {
        synchronized (sLock) {
            try {
                try {
                    IStatsManagerService service = getIStatsManagerServiceLocked();
                    if (pendingIntent == null) {
                        service.removeActiveConfigsChangedOperation(this.mContext.getOpPackageName());
                        return new long[0];
                    }
                    return service.setActiveConfigsChangedOperation(pendingIntent, this.mContext.getOpPackageName());
                } catch (RemoteException e) {
                    Log.e(TAG, "Failed to connect to statsmanager when registering active configs listener.");
                    throw new StatsUnavailableException("could not connect", e);
                } catch (SecurityException e2) {
                    throw new StatsUnavailableException(e2.getMessage(), e2);
                }
            } catch (Throwable th) {
                throw th;
            }
        }
    }

    @Deprecated
    public boolean setDataFetchOperation(long configKey, PendingIntent pendingIntent) {
        try {
            setFetchReportsOperation(pendingIntent, configKey);
            return true;
        } catch (StatsUnavailableException e) {
            return false;
        }
    }

    public byte[] getReports(long configKey) throws StatsUnavailableException {
        byte[] data;
        synchronized (sLock) {
            try {
                try {
                    try {
                        IStatsManagerService service = getIStatsManagerServiceLocked();
                        data = service.getData(configKey, this.mContext.getOpPackageName());
                    } catch (RemoteException e) {
                        Log.e(TAG, "Failed to connect to statsmanager when getting data");
                        throw new StatsUnavailableException("could not connect", e);
                    } catch (SecurityException e2) {
                        throw new StatsUnavailableException(e2.getMessage(), e2);
                    }
                } catch (IllegalStateException e3) {
                    Log.e(TAG, "Failed to getReports in statsmanager");
                    throw new StatsUnavailableException(e3.getMessage(), e3);
                }
            } catch (Throwable th) {
                throw th;
            }
        }
        return data;
    }

    @Deprecated
    public byte[] getData(long configKey) {
        try {
            return getReports(configKey);
        } catch (StatsUnavailableException e) {
            return null;
        }
    }

    public byte[] getStatsMetadata() throws StatsUnavailableException {
        byte[] metadata;
        synchronized (sLock) {
            try {
                try {
                    try {
                        IStatsManagerService service = getIStatsManagerServiceLocked();
                        metadata = service.getMetadata(this.mContext.getOpPackageName());
                    } catch (RemoteException e) {
                        Log.e(TAG, "Failed to connect to statsmanager when getting metadata");
                        throw new StatsUnavailableException("could not connect", e);
                    } catch (SecurityException e2) {
                        throw new StatsUnavailableException(e2.getMessage(), e2);
                    }
                } catch (IllegalStateException e3) {
                    Log.e(TAG, "Failed to getStatsMetadata in statsmanager");
                    throw new StatsUnavailableException(e3.getMessage(), e3);
                }
            } catch (Throwable th) {
                throw th;
            }
        }
        return metadata;
    }

    @Deprecated
    public byte[] getMetadata() {
        try {
            return getStatsMetadata();
        } catch (StatsUnavailableException e) {
            return null;
        }
    }

    public long[] getRegisteredExperimentIds() throws StatsUnavailableException {
        long[] registeredExperimentIds;
        synchronized (sLock) {
            try {
                try {
                    IStatsManagerService service = getIStatsManagerServiceLocked();
                    registeredExperimentIds = service.getRegisteredExperimentIds();
                } catch (RemoteException e) {
                    throw new StatsUnavailableException("could not connect", e);
                } catch (IllegalStateException e2) {
                    Log.e(TAG, "Failed to getRegisteredExperimentIds in statsmanager");
                    throw new StatsUnavailableException(e2.getMessage(), e2);
                }
            } catch (Throwable th) {
                throw th;
            }
        }
        return registeredExperimentIds;
    }

    public void setPullAtomCallback(int atomTag, PullAtomMetadata metadata, Executor executor, StatsPullAtomCallback callback) {
        int[] additiveFields;
        long coolDownMillis = metadata == null ? 1000L : metadata.mCoolDownMillis;
        long timeoutMillis = metadata == null ? 2000L : metadata.mTimeoutMillis;
        if (metadata == null) {
            additiveFields = new int[0];
        } else {
            additiveFields = metadata.mAdditiveFields;
        }
        if (additiveFields == null) {
            additiveFields = new int[0];
        }
        synchronized (sLock) {
            try {
                try {
                    IStatsManagerService service = getIStatsManagerServiceLocked();
                    PullAtomCallbackInternal rec = new PullAtomCallbackInternal(atomTag, callback, executor);
                    service.registerPullAtomCallback(atomTag, coolDownMillis, timeoutMillis, additiveFields, rec);
                } catch (RemoteException e) {
                    throw new RuntimeException("Unable to register pull callback", e);
                }
            } catch (Throwable th) {
                throw th;
            }
        }
    }

    public void clearPullAtomCallback(int atomTag) {
        synchronized (sLock) {
            try {
                try {
                    IStatsManagerService service = getIStatsManagerServiceLocked();
                    service.unregisterPullAtomCallback(atomTag);
                } catch (RemoteException e) {
                    throw new RuntimeException("Unable to unregister pull atom callback");
                }
            } catch (Throwable th) {
                throw th;
            }
        }
    }

    private static class PullAtomCallbackInternal extends IPullAtomCallback.Stub {
        public final int mAtomId;
        public final StatsPullAtomCallback mCallback;
        public final Executor mExecutor;

        PullAtomCallbackInternal(int atomId, StatsPullAtomCallback callback, Executor executor) {
            this.mAtomId = atomId;
            this.mCallback = callback;
            this.mExecutor = executor;
        }

        @Override // android.p012os.IPullAtomCallback
        public void onPullAtom(final int atomTag, final IPullAtomResultReceiver resultReceiver) {
            long token = Binder.clearCallingIdentity();
            try {
                this.mExecutor.execute(new Runnable() { // from class: android.app.-$$Lambda$StatsManager$PullAtomCallbackInternal$w6Gp-6oZ4SkNmMx3n0IbFHBMKZs
                    @Override // java.lang.Runnable
                    public final void run() {
                        this.f$0.lambda$onPullAtom$0$StatsManager$PullAtomCallbackInternal(atomTag, resultReceiver);
                    }
                });
            } finally {
                Binder.restoreCallingIdentity(token);
            }
        }

        public /* synthetic */ void lambda$onPullAtom$0$StatsManager$PullAtomCallbackInternal(int atomTag, IPullAtomResultReceiver resultReceiver) {
            List<StatsEvent> data = new ArrayList<>();
            int successInt = this.mCallback.onPullAtom(atomTag, data);
            boolean success = successInt == 0;
            StatsEventParcel[] parcels = new StatsEventParcel[data.size()];
            for (int i = 0; i < data.size(); i++) {
                parcels[i] = new StatsEventParcel();
                parcels[i].buffer = data.get(i).getBytes();
            }
            try {
                resultReceiver.pullFinished(atomTag, success, parcels);
            } catch (RemoteException e) {
                Log.w(StatsManager.TAG, "StatsPullResultReceiver failed for tag " + this.mAtomId + " due to TransactionTooLarge. Calling pullFinish with no data");
                StatsEventParcel[] emptyData = new StatsEventParcel[0];
                try {
                    resultReceiver.pullFinished(atomTag, false, emptyData);
                } catch (RemoteException e2) {
                    Log.w(StatsManager.TAG, "StatsPullResultReceiver failed for tag " + this.mAtomId + " with empty payload");
                }
            }
        }
    }

    public static class PullAtomMetadata {
        private final int[] mAdditiveFields;
        private final long mCoolDownMillis;
        private final long mTimeoutMillis;

        private PullAtomMetadata(long coolDownMillis, long timeoutMillis, int[] additiveFields) {
            this.mCoolDownMillis = coolDownMillis;
            this.mTimeoutMillis = timeoutMillis;
            this.mAdditiveFields = additiveFields;
        }

        public static class Builder {
            private long mCoolDownMillis = 1000;
            private long mTimeoutMillis = 2000;
            private int[] mAdditiveFields = null;

            public Builder setCoolDownMillis(long coolDownMillis) {
                this.mCoolDownMillis = coolDownMillis;
                return this;
            }

            public Builder setTimeoutMillis(long timeoutMillis) {
                this.mTimeoutMillis = timeoutMillis;
                return this;
            }

            public Builder setAdditiveFields(int[] additiveFields) {
                this.mAdditiveFields = additiveFields;
                return this;
            }

            public PullAtomMetadata build() {
                return new PullAtomMetadata(this.mCoolDownMillis, this.mTimeoutMillis, this.mAdditiveFields);
            }
        }

        public long getCoolDownMillis() {
            return this.mCoolDownMillis;
        }

        public long getTimeoutMillis() {
            return this.mTimeoutMillis;
        }

        public int[] getAdditiveFields() {
            return this.mAdditiveFields;
        }
    }

    private IStatsManagerService getIStatsManagerServiceLocked() {
        IStatsManagerService iStatsManagerService = this.mStatsManagerService;
        if (iStatsManagerService != null) {
            return iStatsManagerService;
        }
        IStatsManagerService iStatsManagerServiceAsInterface = IStatsManagerService.Stub.asInterface(StatsFrameworkInitializer.getStatsServiceManager().getStatsManagerServiceRegisterer().get());
        this.mStatsManagerService = iStatsManagerServiceAsInterface;
        return iStatsManagerServiceAsInterface;
    }

    public static class StatsUnavailableException extends AndroidException {
        public StatsUnavailableException(String reason) {
            super("Failed to connect to statsd: " + reason);
        }

        public StatsUnavailableException(String reason, Throwable e) {
            super("Failed to connect to statsd: " + reason, e);
        }
    }
}
