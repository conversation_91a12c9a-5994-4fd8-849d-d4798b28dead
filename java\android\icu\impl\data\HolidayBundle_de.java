package android.icu.impl.data;

import java.util.ListResourceBundle;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class HolidayBundle_de extends ListResourceBundle {
    private static final Object[][] fContents = {new Object[]{"All Saints' Day", "Allerheiligen"}, new Object[]{"All Souls' Day", "Allerseelen"}, new Object[]{"Armistice Day", "Waffenstillstandstag"}, new Object[]{"Ascension", "Christi Himmelfahrt"}, new Object[]{"Ash Wednesday", "Aschermittwoch"}, new Object[]{"Assumption", "Mariä Himmelfahrt"}, new Object[]{"Boxing Day", "2. Weihnachtstag"}, new Object[]{"Carnival", "Karneval"}, new Object[]{"Christmas", "Weihnachtstag"}, new Object[]{"Civic Holiday", "Bürgerfeiertag"}, new Object[]{"Constitution Day", "Verfassungstag"}, new Object[]{"Corpus Christi", "Fronleichnam"}, new Object[]{"Day of Prayer and Repentance", "Buß- und Bettag"}, new Object[]{"Easter Monday", "Ostermonntag"}, new Object[]{"Easter Sunday", "Ostersonntag"}, new Object[]{"Epiphany", "Heilige 3 Könige"}, new Object[]{"Father's Day", "Vatertag"}, new Object[]{"Flag Day", "Jahrestag der Nationalflagge"}, new Object[]{"German Unity Day", "Tag der deutschen Einheit"}, new Object[]{"Good Friday", "Karfreitag"}, new Object[]{"Halloween", "Abend vor Allerheiligen"}, new Object[]{"Immaculate Conception", "Mariä Empfängnis"}, new Object[]{"Independence Day", "Unabhängigkeitstag"}, new Object[]{"Labor Day", "Tag der Arbeit"}, new Object[]{"Liberation Day", "Befreiungstag"}, new Object[]{"Mardi Gras", "Faschingsdienstag"}, new Object[]{"Maundy Thursday", "Gründonnerstag"}, new Object[]{"May Day", "Maifeiertag"}, new Object[]{"Memorial Day", "Tag des Gedenkens"}, new Object[]{"Mother's Day", "Muttertag"}, new Object[]{"National Holiday", "Nationalfeiertag"}, new Object[]{"New Year's Day", "Neujahr"}, new Object[]{"New Year's Eve", "Silvesterabend"}, new Object[]{"Palm Sunday", "Palmsonntag"}, new Object[]{"Pentecost", "Pfingsten"}, new Object[]{"Presidents' Day", "Präsidentstag"}, new Object[]{"Remembrance Day", "Volkstrauertag"}, new Object[]{"Revolution Day", "Jahrestag der Revolution"}, new Object[]{"Rose Monday", "Rosenmontag"}, new Object[]{"St. Stephen's Day", "Stephanitag"}, new Object[]{"Shrove Tuesday", "Faschingsdienstag"}, new Object[]{"Spring Holiday", "Tag des Frühlings"}, new Object[]{"Summer Bank Holiday", "Bankfeiertag"}, new Object[]{"Thanksgiving", "Dankfest"}, new Object[]{"Unity Day", "Einheitstag"}, new Object[]{"Veterans' Day", "Veteranstag"}, new Object[]{"Victory Day", "Tag der Befreiung"}, new Object[]{"Washington's Birthday", "Washingtons Geburtstag"}, new Object[]{"Whit Monday", "Pfingstmontag"}, new Object[]{"Whit Sunday", "Pfingstsonntag"}};

    @Override // java.util.ListResourceBundle
    public synchronized Object[][] getContents() {
        return fContents;
    }
}
