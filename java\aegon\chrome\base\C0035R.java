package aegon.chrome.base;

import p002a.p003a.p004a.p005a.AbstractC0001a;

/* renamed from: aegon.chrome.base.R */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class C0035R {

    /* renamed from: aegon.chrome.base.R$anim */
    public static final class anim extends AbstractC0001a.a {
    }

    /* renamed from: aegon.chrome.base.R$animator */
    public static final class animator extends AbstractC0001a.b {
    }

    /* renamed from: aegon.chrome.base.R$array */
    public static final class array extends AbstractC0001a.c {
    }

    /* renamed from: aegon.chrome.base.R$attr */
    public static final class attr extends AbstractC0001a.d {
    }

    /* renamed from: aegon.chrome.base.R$bool */
    public static final class bool extends AbstractC0001a.e {
    }

    /* renamed from: aegon.chrome.base.R$color */
    public static final class color extends AbstractC0001a.f {
    }

    /* renamed from: aegon.chrome.base.R$dimen */
    public static final class dimen extends AbstractC0001a.g {
    }

    /* renamed from: aegon.chrome.base.R$drawable */
    public static final class drawable extends AbstractC0001a.h {
    }

    /* renamed from: aegon.chrome.base.R$font */
    public static final class font extends AbstractC0001a.i {
    }

    /* renamed from: aegon.chrome.base.R$fraction */
    public static final class fraction extends AbstractC0001a.j {
    }

    /* renamed from: aegon.chrome.base.R$id */
    public static final class id extends AbstractC0001a.k {
    }

    /* renamed from: aegon.chrome.base.R$integer */
    public static final class integer extends AbstractC0001a.l {
    }

    /* renamed from: aegon.chrome.base.R$interpolator */
    public static final class interpolator extends AbstractC0001a.m {
    }

    /* renamed from: aegon.chrome.base.R$layout */
    public static final class layout extends AbstractC0001a.n {
    }

    /* renamed from: aegon.chrome.base.R$macro */
    public static final class macro extends AbstractC0001a.o {
    }

    /* renamed from: aegon.chrome.base.R$menu */
    public static final class menu extends AbstractC0001a.p {
    }

    /* renamed from: aegon.chrome.base.R$mipmap */
    public static final class mipmap extends AbstractC0001a.q {
    }

    /* renamed from: aegon.chrome.base.R$plurals */
    public static final class plurals extends AbstractC0001a.r {
    }

    /* renamed from: aegon.chrome.base.R$raw */
    public static final class raw extends AbstractC0001a.s {
    }

    /* renamed from: aegon.chrome.base.R$string */
    public static final class string extends AbstractC0001a.t {
    }

    /* renamed from: aegon.chrome.base.R$style */
    public static final class style extends AbstractC0001a.u {
    }

    /* renamed from: aegon.chrome.base.R$styleable */
    public static final class styleable extends AbstractC0001a.v {
    }

    /* renamed from: aegon.chrome.base.R$transition */
    public static final class transition extends AbstractC0001a.w {
    }

    /* renamed from: aegon.chrome.base.R$xml */
    public static final class xml extends AbstractC0001a.x {
    }

    public static void onResourcesLoaded(int i) {
    }
}
