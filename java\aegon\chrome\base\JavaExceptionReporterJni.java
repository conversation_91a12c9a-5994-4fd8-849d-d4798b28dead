package aegon.chrome.base;

import aegon.chrome.base.JavaExceptionReporter;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class JavaExceptionReporterJni implements JavaExceptionReporter.Natives {
    public static final JniStaticTestMocker<JavaExceptionReporter.Natives> TEST_HOOKS = new JniStaticTestMocker<JavaExceptionReporter.Natives>() { // from class: aegon.chrome.base.JavaExceptionReporterJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(JavaExceptionReporter.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                JavaExceptionReporter.Natives unused = JavaExceptionReporterJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static JavaExceptionReporter.Natives testInstance;

    JavaExceptionReporterJni() {
    }

    @Override // aegon.chrome.base.JavaExceptionReporter.Natives
    public void reportJavaException(boolean z, Throwable th) {
        GEN_JNI.org_chromium_base_JavaExceptionReporter_reportJavaException(z, th);
    }

    @Override // aegon.chrome.base.JavaExceptionReporter.Natives
    public void reportJavaStackTrace(String str) {
        GEN_JNI.org_chromium_base_JavaExceptionReporter_reportJavaStackTrace(str);
    }

    public static JavaExceptionReporter.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            JavaExceptionReporter.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.JavaExceptionReporter.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new JavaExceptionReporterJni();
    }
}
