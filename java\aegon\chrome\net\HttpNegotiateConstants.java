package aegon.chrome.net;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class HttpNegotiateConstants {
    public static final int ERR_ABORTED = 2;
    public static final int ERR_INVALID_AUTH_CREDENTIALS = 5;
    public static final int ERR_INVALID_RESPONSE = 4;
    public static final int ERR_MALFORMED_IDENTITY = 9;
    public static final int ERR_MISSING_AUTH_CREDENTIALS = 7;
    public static final int ERR_UNDOCUMENTED_SECURITY_LIBRARY_STATUS = 8;
    public static final int ERR_UNEXPECTED = 1;
    public static final int ERR_UNEXPECTED_SECURITY_LIBRARY_STATUS = 3;
    public static final int ERR_UNSUPPORTED_AUTH_SCHEME = 6;
    public static final String KEY_CAN_DELEGATE = "canDelegate";
    public static final String KEY_INCOMING_AUTH_TOKEN = "incomingAuthToken";
    public static final String KEY_SPNEGO_CONTEXT = "spnegoContext";
    public static final String KEY_SPNEGO_RESULT = "spnegoResult";

    /* renamed from: OK */
    public static final int f33OK = 0;
    public static final String SPNEGO_FEATURE = "SPNEGO";
    public static final String SPNEGO_TOKEN_TYPE_BASE = "SPNEGO:HOSTBASED:";
}
