@echo off
echo ========================================
echo 元气桌面壁纸广告屏蔽模块构建脚本
echo ========================================
echo.

echo 正在清理项目...
call gradlew clean

echo.
echo 正在构建Debug版本...
call gradlew assembleDebug

echo.
echo 构建完成！
echo APK文件位置: app\build\outputs\apk\debug\app-debug.apk
echo.

echo 是否要安装到设备？(y/n)
set /p choice=
if /i "%choice%"=="y" (
    echo 正在安装APK...
    adb install -r app\build\outputs\apk\debug\app-debug.apk
    echo 安装完成！
    echo.
    echo 请在Xposed框架中激活模块并重启设备。
) else (
    echo 跳过安装步骤。
)

echo.
echo 按任意键退出...
pause >nul
