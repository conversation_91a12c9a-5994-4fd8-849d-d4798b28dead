package android.icu.impl.coll;

import android.icu.text.UCharacterIterator;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class IterCollationIterator extends CollationIterator {
    protected UCharacterIterator iter;

    public IterCollationIterator(CollationData d2, boolean numeric, UCharacterIterator ui) {
        super(d2, numeric);
        this.iter = ui;
    }

    @Override // android.icu.impl.coll.CollationIterator
    public void resetToOffset(int newOffset) {
        reset();
        this.iter.setIndex(newOffset);
    }

    @Override // android.icu.impl.coll.CollationIterator
    public int getOffset() {
        return this.iter.getIndex();
    }

    @Override // android.icu.impl.coll.CollationIterator
    public int nextCodePoint() {
        return this.iter.nextCodePoint();
    }

    @Override // android.icu.impl.coll.CollationIterator
    public int previousCodePoint() {
        return this.iter.previousCodePoint();
    }

    @Override // android.icu.impl.coll.CollationIterator
    protected long handleNextCE32() {
        int c2 = this.iter.next();
        if (c2 < 0) {
            return -4294967104L;
        }
        return makeCodePointAndCE32Pair(c2, this.trie.getFromU16SingleLead((char) c2));
    }

    @Override // android.icu.impl.coll.CollationIterator
    protected char handleGetTrailSurrogate() {
        int trail = this.iter.next();
        if (!isTrailSurrogate(trail) && trail >= 0) {
            this.iter.previous();
        }
        return (char) trail;
    }

    @Override // android.icu.impl.coll.CollationIterator
    protected void forwardNumCodePoints(int num) {
        this.iter.moveCodePointIndex(num);
    }

    @Override // android.icu.impl.coll.CollationIterator
    protected void backwardNumCodePoints(int num) {
        this.iter.moveCodePointIndex(-num);
    }
}
