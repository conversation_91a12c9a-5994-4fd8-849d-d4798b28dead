package android.icu.impl.duration.impl;

import java.util.Locale;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class Utils {
    public static final Locale localeFromString(String s) {
        String language = s;
        String region = "";
        String variant = "";
        int x = language.indexOf("_");
        if (x != -1) {
            region = language.substring(x + 1);
            language = language.substring(0, x);
        }
        int x2 = region.indexOf("_");
        if (x2 != -1) {
            variant = region.substring(x2 + 1);
            region = region.substring(0, x2);
        }
        return new Locale(language, region, variant);
    }

    public static String chineseNumber(long n, ChineseDigits zh) {
        long n2 = n;
        if (n2 < 0) {
            n2 = -n2;
        }
        if (n2 <= 10) {
            if (n2 == 2) {
                return String.valueOf(zh.liang);
            }
            return String.valueOf(zh.digits[(int) n2]);
        }
        char[] buf = new char[40];
        char[] digits = String.valueOf(n2).toCharArray();
        boolean inZero = true;
        boolean forcedZero = false;
        int x = buf.length;
        int i = digits.length;
        int u = -1;
        int l = -1;
        while (true) {
            i--;
            if (i < 0) {
                break;
            }
            if (u == -1) {
                if (l != -1) {
                    x--;
                    buf[x] = zh.levels[l];
                    inZero = true;
                    forcedZero = false;
                }
                u++;
            } else {
                x--;
                int u2 = u + 1;
                buf[x] = zh.units[u];
                if (u2 != 3) {
                    u = u2;
                } else {
                    u = -1;
                    l++;
                }
            }
            int d2 = digits[i] - '0';
            if (d2 == 0) {
                if (x < buf.length - 1 && u != 0) {
                    buf[x] = '*';
                }
                if (inZero || forcedZero) {
                    x--;
                    buf[x] = '*';
                } else {
                    x--;
                    buf[x] = zh.digits[0];
                    inZero = true;
                    forcedZero = u == 1;
                }
            } else {
                inZero = false;
                x--;
                buf[x] = zh.digits[d2];
            }
        }
        if (n2 > 1000000) {
            boolean last = true;
            int i2 = buf.length - 3;
            while (buf[i2] != '0') {
                i2 -= 8;
                last = !last;
                if (i2 <= x) {
                    break;
                }
            }
            int i3 = buf.length - 7;
            do {
                if (buf[i3] == zh.digits[0] && !last) {
                    buf[i3] = '*';
                }
                i3 -= 8;
                last = !last;
            } while (i3 > x);
            if (n2 >= 100000000) {
                int i4 = buf.length - 8;
                do {
                    int i5 = 1;
                    int j = i4 - 1;
                    int e = Math.max(x - 1, i4 - 8);
                    while (true) {
                        if (j <= e) {
                            break;
                        }
                        if (buf[j] != '*') {
                            i5 = 0;
                            break;
                        }
                        j--;
                    }
                    if (i5 != 0) {
                        if (buf[i4 + 1] != '*' && buf[i4 + 1] != zh.digits[0]) {
                            buf[i4] = zh.digits[0];
                        } else {
                            buf[i4] = '*';
                        }
                    }
                    i4 -= 8;
                } while (i4 > x);
            }
        }
        for (int i6 = x; i6 < buf.length; i6++) {
            if (buf[i6] == zh.digits[2] && ((i6 >= buf.length - 1 || buf[i6 + 1] != zh.units[0]) && (i6 <= x || (buf[i6 - 1] != zh.units[0] && buf[i6 - 1] != zh.digits[0] && buf[i6 - 1] != '*')))) {
                buf[i6] = zh.liang;
            }
        }
        if (buf[x] == zh.digits[1] && (zh.f75ko || buf[x + 1] == zh.units[0])) {
            x++;
        }
        int w = x;
        for (int r = x; r < buf.length; r++) {
            if (buf[r] != '*') {
                buf[w] = buf[r];
                w++;
            }
        }
        return new String(buf, x, w - x);
    }

    public static class ChineseDigits {
        final char[] digits;

        /* renamed from: ko */
        final boolean f75ko;
        final char[] levels;
        final char liang;
        final char[] units;
        public static final ChineseDigits DEBUG = new ChineseDigits("0123456789s", "sbq", "WYZ", 'L', false);
        public static final ChineseDigits TRADITIONAL = new ChineseDigits("零一二三四五六七八九十", "十百千", "萬億兆", 20841, false);
        public static final ChineseDigits SIMPLIFIED = new ChineseDigits("零一二三四五六七八九十", "十百千", "万亿兆", 20004, false);
        public static final ChineseDigits KOREAN = new ChineseDigits("영일이삼사오육칠팔구십", "십백천", "만억?", 51060, true);

        ChineseDigits(String digits, String units, String levels, char liang, boolean ko) {
            this.digits = digits.toCharArray();
            this.units = units.toCharArray();
            this.levels = levels.toCharArray();
            this.liang = liang;
            this.f75ko = ko;
        }
    }
}
