package aegon.chrome.base;

import aegon.chrome.base.annotations.MainDex;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class FieldTrialList {

    interface Natives {
        boolean createFieldTrial(String str, String str2);

        String findFullName(String str);

        String getVariationParameter(String str, String str2);

        void logActiveTrials();

        boolean trialExists(String str);
    }

    private FieldTrialList() {
    }

    public static String findFullName(String str) {
        return FieldTrialListJni.get().findFullName(str);
    }

    public static boolean trialExists(String str) {
        return FieldTrialListJni.get().trialExists(str);
    }

    public static String getVariationParameter(String str, String str2) {
        return FieldTrialListJni.get().getVariationParameter(str, str2);
    }

    public static void logActiveTrials() {
        FieldTrialListJni.get().logActiveTrials();
    }

    public static boolean createFieldTrial(String str, String str2) {
        return FieldTrialListJni.get().createFieldTrial(str, str2);
    }
}
