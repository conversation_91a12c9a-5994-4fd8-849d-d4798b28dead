package aegon.chrome.base;

import aegon.chrome.base.FieldTrialList;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class FieldTrialListJni implements FieldTrialList.Natives {
    public static final JniStaticTestMocker<FieldTrialList.Natives> TEST_HOOKS = new JniStaticTestMocker<FieldTrialList.Natives>() { // from class: aegon.chrome.base.FieldTrialListJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(FieldTrialList.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                FieldTrialList.Natives unused = FieldTrialListJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static FieldTrialList.Natives testInstance;

    FieldTrialListJni() {
    }

    @Override // aegon.chrome.base.FieldTrialList.Natives
    public String findFullName(String str) {
        return GEN_JNI.org_chromium_base_FieldTrialList_findFullName(str);
    }

    @Override // aegon.chrome.base.FieldTrialList.Natives
    public boolean trialExists(String str) {
        return GEN_JNI.org_chromium_base_FieldTrialList_trialExists(str);
    }

    @Override // aegon.chrome.base.FieldTrialList.Natives
    public String getVariationParameter(String str, String str2) {
        return GEN_JNI.org_chromium_base_FieldTrialList_getVariationParameter(str, str2);
    }

    @Override // aegon.chrome.base.FieldTrialList.Natives
    public void logActiveTrials() {
        GEN_JNI.org_chromium_base_FieldTrialList_logActiveTrials();
    }

    @Override // aegon.chrome.base.FieldTrialList.Natives
    public boolean createFieldTrial(String str, String str2) {
        return GEN_JNI.org_chromium_base_FieldTrialList_createFieldTrial(str, str2);
    }

    public static FieldTrialList.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            FieldTrialList.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.FieldTrialList.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new FieldTrialListJni();
    }
}
