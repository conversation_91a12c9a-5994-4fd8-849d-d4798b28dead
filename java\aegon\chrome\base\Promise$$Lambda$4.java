package aegon.chrome.base;

import aegon.chrome.base.Promise;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class Promise$$Lambda$4 implements Callback {
    private final Promise.AsyncFunction arg$1;
    private final Promise arg$2;

    private Promise$$Lambda$4(Promise.AsyncFunction asyncFunction, Promise promise) {
        this.arg$1 = asyncFunction;
        this.arg$2 = promise;
    }

    public static Callback lambdaFactory$(Promise.AsyncFunction asyncFunction, Promise promise) {
        return new Promise$$Lambda$4(asyncFunction, promise);
    }

    @Override // aegon.chrome.base.Callback
    public final void onResult(Object obj) {
        Promise.lambda$then$2(this.arg$1, this.arg$2, obj);
    }
}
