package aegon.chrome.base.metrics;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.metrics.NativeUmaRecorder;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class NativeUmaRecorderJni implements NativeUmaRecorder.Natives {
    public static final JniStaticTestMocker<NativeUmaRecorder.Natives> TEST_HOOKS = new JniStaticTestMocker<NativeUmaRecorder.Natives>() { // from class: aegon.chrome.base.metrics.NativeUmaRecorderJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(NativeUmaRecorder.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                NativeUmaRecorder.Natives unused = NativeUmaRecorderJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static NativeUmaRecorder.Natives testInstance;

    NativeUmaRecorderJni() {
    }

    @Override // aegon.chrome.base.metrics.NativeUmaRecorder.Natives
    public long recordBooleanHistogram(String str, long j, boolean z) {
        return GEN_JNI.m48xb68b8438(str, j, z);
    }

    @Override // aegon.chrome.base.metrics.NativeUmaRecorder.Natives
    public long recordExponentialHistogram(String str, long j, int i, int i2, int i3, int i4) {
        return GEN_JNI.m49x315909bb(str, j, i, i2, i3, i4);
    }

    @Override // aegon.chrome.base.metrics.NativeUmaRecorder.Natives
    public long recordLinearHistogram(String str, long j, int i, int i2, int i3, int i4) {
        return GEN_JNI.m50x61f57623(str, j, i, i2, i3, i4);
    }

    @Override // aegon.chrome.base.metrics.NativeUmaRecorder.Natives
    public long recordSparseHistogram(String str, long j, int i) {
        return GEN_JNI.m51x5bbdc588(str, j, i);
    }

    @Override // aegon.chrome.base.metrics.NativeUmaRecorder.Natives
    public void recordUserAction(String str, long j) {
        GEN_JNI.org_chromium_base_metrics_NativeUmaRecorder_recordUserAction(str, j);
    }

    public static NativeUmaRecorder.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            NativeUmaRecorder.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.metrics.NativeUmaRecorder.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new NativeUmaRecorderJni();
    }
}
