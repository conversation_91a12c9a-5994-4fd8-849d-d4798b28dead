package android.icu.impl.number;

import android.icu.impl.StandardPlural;
import android.icu.impl.Utility;
import android.icu.text.PluralRules;
import android.icu.text.UFieldPosition;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.text.FieldPosition;
import org.apache.xpath.XPath;
import sun.misc.DoubleConsts;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public abstract class DecimalQuantity_AbstractBCD implements DecimalQuantity {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    protected static final int INFINITY_FLAG = 2;
    protected static final int NAN_FLAG = 4;
    protected static final int NEGATIVE_FLAG = 1;
    private static final int SECTION_LOWER_EDGE = -1;
    private static final int SECTION_UPPER_EDGE = -2;
    protected byte flags;
    protected boolean isApproximate;
    protected int origDelta;
    protected double origDouble;
    protected int precision;
    protected int scale;
    private static final double[] DOUBLE_MULTIPLIERS = {1.0d, 10.0d, 100.0d, 1000.0d, 10000.0d, 100000.0d, 1000000.0d, 1.0E7d, 1.0E8d, 1.0E9d, 1.0E10d, 1.0E11d, 1.0E12d, 1.0E13d, 1.0E14d, 1.0E15d, 1.0E16d, 1.0E17d, 1.0E18d, 1.0E19d, 1.0E20d, 1.0E21d};
    static final byte[] INT64_BCD = {9, 2, 2, 3, 3, 7, 2, 0, 3, 6, 8, 5, 4, 7, 7, 5, 8, 0, 8};
    protected int lReqPos = 0;
    protected int rReqPos = 0;

    @Deprecated
    public boolean explicitExactDouble = false;

    protected abstract BigDecimal bcdToBigDecimal();

    protected abstract void compact();

    protected abstract void copyBcdFrom(DecimalQuantity decimalQuantity);

    protected abstract byte getDigitPos(int i);

    protected abstract void popFromLeft(int i);

    protected abstract void readBigIntegerToBcd(BigInteger bigInteger);

    protected abstract void readIntToBcd(int i);

    protected abstract void readLongToBcd(long j);

    protected abstract void setBcdToZero();

    protected abstract void setDigitPos(int i, byte b2);

    protected abstract void shiftLeft(int i);

    protected abstract void shiftRight(int i);

    @Override // android.icu.impl.number.DecimalQuantity
    public void copyFrom(DecimalQuantity _other) {
        copyBcdFrom(_other);
        DecimalQuantity_AbstractBCD other = (DecimalQuantity_AbstractBCD) _other;
        this.lReqPos = other.lReqPos;
        this.rReqPos = other.rReqPos;
        this.scale = other.scale;
        this.precision = other.precision;
        this.flags = other.flags;
        this.origDouble = other.origDouble;
        this.origDelta = other.origDelta;
        this.isApproximate = other.isApproximate;
    }

    public DecimalQuantity_AbstractBCD clear() {
        this.lReqPos = 0;
        this.rReqPos = 0;
        this.flags = (byte) 0;
        setBcdToZero();
        return this;
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void setMinInteger(int minInt) {
        if (minInt < this.lReqPos) {
            minInt = this.lReqPos;
        }
        this.lReqPos = minInt;
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void setMinFraction(int minFrac) {
        this.rReqPos = -minFrac;
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void applyMaxInteger(int maxInt) throws ArithmeticException {
        if (this.precision == 0) {
            return;
        }
        if (maxInt <= this.scale) {
            setBcdToZero();
            return;
        }
        int magnitude = getMagnitude();
        if (maxInt <= magnitude) {
            popFromLeft((magnitude - maxInt) + 1);
            compact();
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public long getPositionFingerprint() {
        long fingerprint = 0 ^ (this.lReqPos << 16);
        return fingerprint ^ (this.rReqPos << 32);
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void roundToIncrement(BigDecimal roundingIncrement, MathContext mathContext) {
        BigDecimal temp = toBigDecimal().divide(roundingIncrement, 0, mathContext.getRoundingMode()).multiply(roundingIncrement).round(mathContext);
        if (temp.signum() == 0) {
            setBcdToZero();
        } else {
            setToBigDecimal(temp);
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void multiplyBy(BigDecimal multiplicand) {
        if (isZeroish()) {
            return;
        }
        BigDecimal temp = toBigDecimal();
        setToBigDecimal(temp.multiply(multiplicand));
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void negate() {
        this.flags = (byte) (this.flags ^ 1);
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public int getMagnitude() throws ArithmeticException {
        if (this.precision == 0) {
            throw new ArithmeticException("Magnitude is not well-defined for zero");
        }
        return (this.scale + r0) - 1;
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void adjustMagnitude(int delta) {
        if (this.precision != 0) {
            this.scale = Utility.addExact(this.scale, delta);
            this.origDelta = Utility.addExact(this.origDelta, delta);
            Utility.addExact(this.scale, this.precision);
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public StandardPlural getStandardPlural(PluralRules rules) {
        if (rules == null) {
            return StandardPlural.OTHER;
        }
        String ruleString = rules.select(this);
        return StandardPlural.orOtherFromString(ruleString);
    }

    @Override // android.icu.text.PluralRules.IFixedDecimal
    public double getPluralOperand(PluralRules.Operand operand) {
        int i = C02701.$SwitchMap$android$icu$text$PluralRules$Operand[operand.ordinal()];
        if (i == 1) {
            return isNegative() ? -toLong(true) : toLong(true);
        }
        if (i == 2) {
            return toFractionLong(true);
        }
        if (i == 3) {
            return toFractionLong(false);
        }
        if (i == 4) {
            return fractionCount();
        }
        if (i == 5) {
            return fractionCountWithoutTrailingZeros();
        }
        return Math.abs(toDouble());
    }

    /* renamed from: android.icu.impl.number.DecimalQuantity_AbstractBCD$1 */
    static /* synthetic */ class C02701 {
        static final /* synthetic */ int[] $SwitchMap$android$icu$text$PluralRules$Operand;

        static {
            int[] iArr = new int[PluralRules.Operand.values().length];
            $SwitchMap$android$icu$text$PluralRules$Operand = iArr;
            try {
                iArr[PluralRules.Operand.i.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$android$icu$text$PluralRules$Operand[PluralRules.Operand.f.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$android$icu$text$PluralRules$Operand[PluralRules.Operand.t.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$android$icu$text$PluralRules$Operand[PluralRules.Operand.v.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                $SwitchMap$android$icu$text$PluralRules$Operand[PluralRules.Operand.w.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void populateUFieldPosition(FieldPosition fp) {
        if (fp instanceof UFieldPosition) {
            ((UFieldPosition) fp).setFractionDigits((int) getPluralOperand(PluralRules.Operand.v), (long) getPluralOperand(PluralRules.Operand.f));
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public int getUpperDisplayMagnitude() {
        int magnitude = this.scale + this.precision;
        int result = this.lReqPos;
        if (result <= magnitude) {
            result = magnitude;
        }
        return result - 1;
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public int getLowerDisplayMagnitude() {
        int magnitude = this.scale;
        int result = this.rReqPos;
        return result < magnitude ? result : magnitude;
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public byte getDigit(int magnitude) {
        return getDigitPos(magnitude - this.scale);
    }

    private int fractionCount() {
        return -getLowerDisplayMagnitude();
    }

    private int fractionCountWithoutTrailingZeros() {
        return Math.max(-this.scale, 0);
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public boolean isNegative() {
        return (this.flags & 1) != 0;
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public int signum() {
        if (isNegative()) {
            return -1;
        }
        return (!isZeroish() || isInfinite()) ? 1 : 0;
    }

    @Override // android.icu.impl.number.DecimalQuantity, android.icu.text.PluralRules.IFixedDecimal
    public boolean isInfinite() {
        return (this.flags & 2) != 0;
    }

    @Override // android.icu.impl.number.DecimalQuantity, android.icu.text.PluralRules.IFixedDecimal
    public boolean isNaN() {
        return (this.flags & 4) != 0;
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public boolean isZeroish() {
        return this.precision == 0;
    }

    public void setToInt(int n) {
        setBcdToZero();
        this.flags = (byte) 0;
        if (n < 0) {
            this.flags = (byte) (0 | 1);
            n = -n;
        }
        if (n != 0) {
            _setToInt(n);
            compact();
        }
    }

    private void _setToInt(int n) {
        if (n == Integer.MIN_VALUE) {
            readLongToBcd(-n);
        } else {
            readIntToBcd(n);
        }
    }

    public void setToLong(long n) {
        setBcdToZero();
        this.flags = (byte) 0;
        if (n < 0) {
            this.flags = (byte) (0 | 1);
            n = -n;
        }
        if (n != 0) {
            _setToLong(n);
            compact();
        }
    }

    private void _setToLong(long n) {
        if (n == Long.MIN_VALUE) {
            readBigIntegerToBcd(BigInteger.valueOf(n).negate());
        } else if (n <= 2147483647L) {
            readIntToBcd((int) n);
        } else {
            readLongToBcd(n);
        }
    }

    public void setToBigInteger(BigInteger n) {
        setBcdToZero();
        this.flags = (byte) 0;
        if (n.signum() == -1) {
            this.flags = (byte) (this.flags | 1);
            n = n.negate();
        }
        if (n.signum() != 0) {
            _setToBigInteger(n);
            compact();
        }
    }

    private void _setToBigInteger(BigInteger n) {
        if (n.bitLength() < 32) {
            readIntToBcd(n.intValue());
        } else if (n.bitLength() < 64) {
            readLongToBcd(n.longValue());
        } else {
            readBigIntegerToBcd(n);
        }
    }

    public void setToDouble(double n) {
        setBcdToZero();
        this.flags = (byte) 0;
        if (Double.doubleToRawLongBits(n) < 0) {
            this.flags = (byte) (this.flags | 1);
            n = -n;
        }
        if (Double.isNaN(n)) {
            this.flags = (byte) (this.flags | 4);
            return;
        }
        if (Double.isInfinite(n)) {
            this.flags = (byte) (this.flags | 2);
        } else if (n != XPath.MATCH_SCORE_QNAME) {
            _setToDoubleFast(n);
            compact();
        }
    }

    private void _setToDoubleFast(double n) {
        double n2;
        this.isApproximate = true;
        this.origDouble = n;
        this.origDelta = 0;
        long ieeeBits = Double.doubleToLongBits(n);
        int exponent = ((int) ((DoubleConsts.EXP_BIT_MASK & ieeeBits) >> 52)) - 1023;
        if (exponent <= 52 && ((long) n) == n) {
            _setToLong((long) n);
            return;
        }
        int fracLength = (int) ((52 - exponent) / 3.32192809489d);
        if (fracLength >= 0) {
            int i = fracLength;
            while (i >= 22) {
                n *= 1.0E22d;
                i -= 22;
            }
            n2 = n * DOUBLE_MULTIPLIERS[i];
        } else {
            int i2 = fracLength;
            while (i2 <= -22) {
                n /= 1.0E22d;
                i2 += 22;
            }
            n2 = n / DOUBLE_MULTIPLIERS[-i2];
        }
        long result = Math.round(n2);
        if (result != 0) {
            _setToLong(result);
            this.scale -= fracLength;
        }
    }

    private void convertToAccurateDouble() {
        double n = this.origDouble;
        int delta = this.origDelta;
        setBcdToZero();
        String dstr = Double.toString(n);
        if (dstr.indexOf(69) != -1) {
            int expPos = dstr.indexOf(69);
            _setToLong(Long.parseLong(dstr.charAt(0) + dstr.substring(2, expPos)));
            this.scale = this.scale + (Integer.parseInt(dstr.substring(expPos + 1)) - (expPos + (-1))) + 1;
        } else if (dstr.charAt(0) == '0') {
            _setToLong(Long.parseLong(dstr.substring(2)));
            this.scale += 2 - dstr.length();
        } else if (dstr.charAt(dstr.length() - 1) == '0') {
            _setToLong(Long.parseLong(dstr.substring(0, dstr.length() - 2)));
        } else {
            int decimalPos = dstr.indexOf(46);
            _setToLong(Long.parseLong(dstr.substring(0, decimalPos) + dstr.substring(decimalPos + 1)));
            this.scale = this.scale + (decimalPos - dstr.length()) + 1;
        }
        this.scale += delta;
        compact();
        this.explicitExactDouble = true;
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void setToBigDecimal(BigDecimal n) {
        setBcdToZero();
        this.flags = (byte) 0;
        if (n.signum() == -1) {
            this.flags = (byte) (this.flags | 1);
            n = n.negate();
        }
        if (n.signum() != 0) {
            _setToBigDecimal(n);
            compact();
        }
    }

    private void _setToBigDecimal(BigDecimal n) {
        int fracLength = n.scale();
        BigInteger bi = n.scaleByPowerOfTen(fracLength).toBigInteger();
        _setToBigInteger(bi);
        this.scale -= fracLength;
    }

    public long toLong(boolean truncateIfOverflow) {
        long result = 0;
        int upperMagnitude = (this.scale + this.precision) - 1;
        if (truncateIfOverflow) {
            upperMagnitude = Math.min(upperMagnitude, 17);
        }
        for (int magnitude = upperMagnitude; magnitude >= 0; magnitude--) {
            result = (10 * result) + getDigitPos(magnitude - this.scale);
        }
        if (isNegative()) {
            return -result;
        }
        return result;
    }

    public long toFractionLong(boolean includeTrailingZeros) {
        long result = 0;
        int lowerMagnitude = this.scale;
        if (includeTrailingZeros) {
            lowerMagnitude = Math.min(lowerMagnitude, this.rReqPos);
        }
        for (int magnitude = -1; magnitude >= lowerMagnitude && result <= 1.0E17d; magnitude--) {
            result = (10 * result) + getDigitPos(magnitude - this.scale);
        }
        if (!includeTrailingZeros) {
            while (result > 0 && result % 10 == 0) {
                result /= 10;
            }
        }
        return result;
    }

    public boolean fitsInLong() throws ArithmeticException {
        if (isInfinite() || isNaN()) {
            return false;
        }
        if (isZeroish()) {
            return true;
        }
        if (this.scale < 0) {
            return false;
        }
        int magnitude = getMagnitude();
        if (magnitude < 18) {
            return true;
        }
        if (magnitude > 18) {
            return false;
        }
        for (int p = 0; p < this.precision; p++) {
            byte digit = getDigit(18 - p);
            byte[] bArr = INT64_BCD;
            if (digit < bArr[p]) {
                return true;
            }
            if (digit > bArr[p]) {
                return false;
            }
        }
        return isNegative();
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public double toDouble() {
        if (isNaN()) {
            return Double.NaN;
        }
        if (isInfinite()) {
            return isNegative() ? Double.NEGATIVE_INFINITY : Double.POSITIVE_INFINITY;
        }
        StringBuilder sb = new StringBuilder();
        toScientificString(sb);
        return Double.valueOf(sb.toString()).doubleValue();
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public BigDecimal toBigDecimal() {
        if (this.isApproximate) {
            convertToAccurateDouble();
        }
        return bcdToBigDecimal();
    }

    private static int safeSubtract(int a2, int b2) {
        int diff = a2 - b2;
        if (b2 < 0 && diff < a2) {
            return Integer.MAX_VALUE;
        }
        if (b2 > 0 && diff > a2) {
            return Integer.MIN_VALUE;
        }
        return diff;
    }

    public void truncate() {
        int i = this.scale;
        if (i < 0) {
            shiftRight(-i);
            this.scale = 0;
            compact();
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void roundToNickel(int magnitude, MathContext mathContext) {
        roundToMagnitude(magnitude, mathContext, true);
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void roundToMagnitude(int magnitude, MathContext mathContext) {
        roundToMagnitude(magnitude, mathContext, false);
    }

    private void roundToMagnitude(int magnitude, MathContext mathContext, boolean nickel) {
        int section;
        int section2;
        int p;
        boolean isEven;
        int position = safeSubtract(magnitude, this.scale);
        int _mcPrecision = mathContext.getPrecision();
        if (_mcPrecision > 0) {
            int i = this.precision;
            if (i - _mcPrecision > position) {
                position = i - _mcPrecision;
            }
        }
        byte trailingDigit = getDigitPos(position);
        if ((position > 0 || this.isApproximate || (nickel && trailingDigit != 0 && trailingDigit != 5)) && this.precision != 0) {
            byte leadingDigit = getDigitPos(safeSubtract(position, 1));
            if (!this.isApproximate) {
                if (nickel && trailingDigit != 2 && trailingDigit != 7) {
                    if (trailingDigit < 2) {
                        p = 1;
                    } else if (trailingDigit >= 5 && trailingDigit < 7) {
                        p = 1;
                    } else {
                        p = 3;
                    }
                } else if (leadingDigit < 5) {
                    p = 1;
                } else if (leadingDigit > 5) {
                    p = 3;
                } else {
                    p = 2;
                    int p2 = safeSubtract(position, 2);
                    while (true) {
                        if (p2 < 0) {
                            break;
                        }
                        if (getDigitPos(p2) == 0) {
                            p2--;
                        } else {
                            p = 3;
                            break;
                        }
                    }
                }
            } else {
                int p3 = safeSubtract(position, 2);
                int minP = Math.max(0, this.precision - 14);
                if (leadingDigit == 0 && (!nickel || trailingDigit == 0 || trailingDigit == 5)) {
                    section = -1;
                    while (true) {
                        if (p3 < minP) {
                            break;
                        }
                        if (getDigitPos(p3) == 0) {
                            p3--;
                        } else {
                            section = 1;
                            break;
                        }
                    }
                } else if (leadingDigit == 4 && (!nickel || trailingDigit == 2 || trailingDigit == 7)) {
                    section = 2;
                    while (true) {
                        if (p3 < minP) {
                            break;
                        }
                        if (getDigitPos(p3) == 9) {
                            p3--;
                        } else {
                            section = 1;
                            break;
                        }
                    }
                } else if (leadingDigit == 5 && (!nickel || trailingDigit == 2 || trailingDigit == 7)) {
                    section = 2;
                    while (true) {
                        if (p3 < minP) {
                            break;
                        }
                        if (getDigitPos(p3) == 0) {
                            p3--;
                        } else {
                            section = 3;
                            break;
                        }
                    }
                } else if (leadingDigit == 9 && (!nickel || trailingDigit == 4 || trailingDigit == 9)) {
                    section = -2;
                    while (true) {
                        if (p3 < minP) {
                            break;
                        }
                        if (getDigitPos(p3) == 9) {
                            p3--;
                        } else {
                            section = 3;
                            break;
                        }
                    }
                } else if (nickel && trailingDigit != 2 && trailingDigit != 7) {
                    if (trailingDigit < 2) {
                        section = 1;
                    } else if (trailingDigit >= 5 && trailingDigit < 7) {
                        section = 1;
                    } else {
                        section = 3;
                    }
                } else if (leadingDigit < 5) {
                    section = 1;
                } else {
                    section = 3;
                }
                boolean roundsAtMidpoint = RoundingUtils.roundsAtMidpoint(mathContext.getRoundingMode().ordinal());
                if (safeSubtract(position, 1) < this.precision - 14 || ((roundsAtMidpoint && section == 2) || (!roundsAtMidpoint && section < 0))) {
                    convertToAccurateDouble();
                    roundToMagnitude(magnitude, mathContext, nickel);
                    return;
                }
                this.isApproximate = false;
                this.origDouble = XPath.MATCH_SCORE_QNAME;
                this.origDelta = 0;
                if (position <= 0 && (!nickel || trailingDigit == 0 || trailingDigit == 5)) {
                    return;
                }
                if (section != -1) {
                    section2 = section;
                } else {
                    section2 = 1;
                }
                if (section2 != -2) {
                    p = section2;
                } else {
                    p = 3;
                }
            }
            if (nickel) {
                isEven = trailingDigit < 2 || trailingDigit > 7 || (trailingDigit == 2 && p != 3) || (trailingDigit == 7 && p == 3);
            } else {
                isEven = trailingDigit % 2 == 0;
            }
            boolean roundDown = RoundingUtils.getRoundingDirection(isEven, isNegative(), p, mathContext.getRoundingMode().ordinal(), this);
            if (position >= this.precision) {
                setBcdToZero();
                this.scale = magnitude;
            } else {
                shiftRight(position);
            }
            if (nickel) {
                if (trailingDigit < 5 && roundDown) {
                    setDigitPos(0, (byte) 0);
                    compact();
                    return;
                } else if (trailingDigit >= 5 && !roundDown) {
                    setDigitPos(0, (byte) 9);
                    trailingDigit = 9;
                } else {
                    setDigitPos(0, (byte) 5);
                    return;
                }
            }
            if (!roundDown) {
                if (trailingDigit == 9) {
                    int bubblePos = 0;
                    while (getDigitPos(bubblePos) == 9) {
                        bubblePos++;
                    }
                    shiftRight(bubblePos);
                }
                byte digit0 = getDigitPos(0);
                setDigitPos(0, (byte) (digit0 + 1));
                this.precision++;
            }
            compact();
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public void roundToInfinity() {
        if (this.isApproximate) {
            convertToAccurateDouble();
        }
    }

    @Deprecated
    public void appendDigit(byte value, int leadingZeros, boolean appendAsInteger) {
        if (value == 0) {
            if (appendAsInteger && this.precision != 0) {
                this.scale += leadingZeros + 1;
                return;
            }
            return;
        }
        int i = this.scale;
        if (i > 0) {
            leadingZeros += i;
            if (appendAsInteger) {
                this.scale = 0;
            }
        }
        shiftLeft(leadingZeros + 1);
        setDigitPos(0, value);
        if (appendAsInteger) {
            this.scale += leadingZeros + 1;
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public String toPlainString() {
        StringBuilder sb = new StringBuilder();
        if (isNegative()) {
            sb.append('-');
        }
        if (this.precision == 0 || getMagnitude() < 0) {
            sb.append('0');
        }
        for (int m = getUpperDisplayMagnitude(); m >= getLowerDisplayMagnitude(); m--) {
            sb.append((char) (getDigit(m) + 48));
            if (m == 0) {
                sb.append('.');
            }
        }
        return sb.toString();
    }

    public String toScientificString() {
        StringBuilder sb = new StringBuilder();
        toScientificString(sb);
        return sb.toString();
    }

    public void toScientificString(StringBuilder result) {
        if (isNegative()) {
            result.append('-');
        }
        int i = this.precision;
        if (i == 0) {
            result.append("0E+0");
            return;
        }
        int upperPos = i - 1;
        result.append((char) (getDigitPos(upperPos) + 48));
        int p = upperPos - 1;
        if (p >= 0) {
            result.append('.');
            while (p >= 0) {
                result.append((char) (getDigitPos(p) + 48));
                p--;
            }
        }
        result.append('E');
        int _scale = this.scale + upperPos;
        if (_scale == Integer.MIN_VALUE) {
            result.append("-2147483648");
            return;
        }
        if (_scale < 0) {
            _scale *= -1;
            result.append('-');
        } else {
            result.append('+');
        }
        if (_scale == 0) {
            result.append('0');
        }
        int insertIndex = result.length();
        while (_scale > 0) {
            int quot = _scale / 10;
            int rem = _scale % 10;
            result.insert(insertIndex, (char) (rem + 48));
            _scale = quot;
        }
    }

    public boolean equals(Object other) {
        boolean basicEquals;
        if (this == other) {
            return true;
        }
        if (other == null || !(other instanceof DecimalQuantity_AbstractBCD)) {
            return false;
        }
        DecimalQuantity_AbstractBCD _other = (DecimalQuantity_AbstractBCD) other;
        if (this.scale == _other.scale && this.precision == _other.precision && this.flags == _other.flags && this.lReqPos == _other.lReqPos && this.rReqPos == _other.rReqPos && this.isApproximate == _other.isApproximate) {
            basicEquals = true;
        } else {
            basicEquals = false;
        }
        if (!basicEquals) {
            return false;
        }
        if (this.precision == 0) {
            return true;
        }
        if (this.isApproximate) {
            if (this.origDouble == _other.origDouble && this.origDelta == _other.origDelta) {
                return true;
            }
            return false;
        }
        for (int m = getUpperDisplayMagnitude(); m >= getLowerDisplayMagnitude(); m--) {
            if (getDigit(m) != _other.getDigit(m)) {
                return false;
            }
        }
        return true;
    }
}
