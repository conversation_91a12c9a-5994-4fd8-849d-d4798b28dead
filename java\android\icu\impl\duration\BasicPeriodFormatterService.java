package android.icu.impl.duration;

import android.icu.impl.duration.impl.PeriodFormatterDataService;
import android.icu.impl.duration.impl.ResourceBasedPeriodFormatterDataService;
import java.util.Collection;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class BasicPeriodFormatterService implements PeriodFormatterService {
    private static BasicPeriodFormatterService instance;

    /* renamed from: ds */
    private PeriodFormatterDataService f65ds;

    public static BasicPeriodFormatterService getInstance() {
        if (instance == null) {
            PeriodFormatterDataService ds = ResourceBasedPeriodFormatterDataService.getInstance();
            instance = new BasicPeriodFormatterService(ds);
        }
        return instance;
    }

    public BasicPeriodFormatterService(PeriodFormatterDataService ds) {
        this.f65ds = ds;
    }

    @Override // android.icu.impl.duration.PeriodFormatterService
    public DurationFormatterFactory newDurationFormatterFactory() {
        return new BasicDurationFormatterFactory(this);
    }

    @Override // android.icu.impl.duration.PeriodFormatterService
    public PeriodFormatterFactory newPeriodFormatterFactory() {
        return new BasicPeriodFormatterFactory(this.f65ds);
    }

    @Override // android.icu.impl.duration.PeriodFormatterService
    public PeriodBuilderFactory newPeriodBuilderFactory() {
        return new BasicPeriodBuilderFactory(this.f65ds);
    }

    @Override // android.icu.impl.duration.PeriodFormatterService
    public Collection<String> getAvailableLocaleNames() {
        return this.f65ds.getAvailableLocales();
    }
}
