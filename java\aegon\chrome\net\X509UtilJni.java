package aegon.chrome.net;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.net.X509Util;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class X509UtilJni implements X509Util.Natives {
    public static final JniStaticTestMocker<X509Util.Natives> TEST_HOOKS = new JniStaticTestMocker<X509Util.Natives>() { // from class: aegon.chrome.net.X509UtilJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(X509Util.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                X509Util.Natives unused = X509UtilJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static X509Util.Natives testInstance;

    X509UtilJni() {
    }

    @Override // aegon.chrome.net.X509Util.Natives
    public void notifyKeyChainChanged() {
        GEN_JNI.org_chromium_net_X509Util_notifyKeyChainChanged();
    }

    public static X509Util.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            X509Util.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.net.X509Util.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new X509UtilJni();
    }
}
