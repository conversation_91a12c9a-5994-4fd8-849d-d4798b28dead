package aegon.chrome.base;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public interface UnownedUserData {

    /* renamed from: aegon.chrome.base.UnownedUserData$-CC, reason: invalid class name */
    public final /* synthetic */ class CC {
        public static boolean $default$informOnDetachmentFromHost(UnownedUserData unownedUserData) {
            return true;
        }

        public static void $default$onDetachedFromHost(UnownedUserData unownedUserData, UnownedUserDataHost unownedUserDataHost) {
        }
    }

    boolean informOnDetachmentFromHost();

    void onDetachedFromHost(UnownedUserDataHost unownedUserDataHost);
}
