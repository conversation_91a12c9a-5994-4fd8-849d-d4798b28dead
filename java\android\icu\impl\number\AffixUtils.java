package android.icu.impl.number;

import android.icu.impl.FormattedStringBuilder;
import android.icu.lang.UCharacter;
import android.icu.text.NumberFormat;
import android.icu.text.UnicodeSet;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class AffixUtils {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final int STATE_AFTER_QUOTE = 3;
    private static final int STATE_BASE = 0;
    private static final int STATE_FIFTH_CURR = 8;
    private static final int STATE_FIRST_CURR = 4;
    private static final int STATE_FIRST_QUOTE = 1;
    private static final int STATE_FOURTH_CURR = 7;
    private static final int STATE_INSIDE_QUOTE = 2;
    private static final int STATE_OVERFLOW_CURR = 9;
    private static final int STATE_SECOND_CURR = 5;
    private static final int STATE_THIRD_CURR = 6;
    private static final int TYPE_CODEPOINT = 0;
    public static final int TYPE_CURRENCY_DOUBLE = -6;
    public static final int TYPE_CURRENCY_OVERFLOW = -15;
    public static final int TYPE_CURRENCY_QUAD = -8;
    public static final int TYPE_CURRENCY_QUINT = -9;
    public static final int TYPE_CURRENCY_SINGLE = -5;
    public static final int TYPE_CURRENCY_TRIPLE = -7;
    public static final int TYPE_MINUS_SIGN = -1;
    public static final int TYPE_PERCENT = -3;
    public static final int TYPE_PERMILLE = -4;
    public static final int TYPE_PLUS_SIGN = -2;

    public interface SymbolProvider {
        CharSequence getSymbol(int i);
    }

    public interface TokenConsumer {
        void consumeToken(int i);
    }

    public static int estimateLength(CharSequence patternString) {
        if (patternString == null) {
            return 0;
        }
        int state = 0;
        int offset = 0;
        int length = 0;
        while (offset < patternString.length()) {
            int cp = Character.codePointAt(patternString, offset);
            if (state != 0) {
                if (state != 1) {
                    if (state != 2) {
                        if (state == 3) {
                            if (cp == 39) {
                                length++;
                                state = 2;
                            } else {
                                length++;
                            }
                        } else {
                            throw new AssertionError();
                        }
                    } else if (cp == 39) {
                        state = 3;
                    } else {
                        length++;
                    }
                } else if (cp == 39) {
                    length++;
                    state = 0;
                } else {
                    length++;
                    state = 2;
                }
            } else if (cp == 39) {
                state = 1;
            } else {
                length++;
            }
            offset += Character.charCount(cp);
        }
        if (state == 1 || state == 2) {
            throw new IllegalArgumentException("Unterminated quote: \"" + ((Object) patternString) + "\"");
        }
        return length;
    }

    /* JADX WARN: Removed duplicated region for block: B:23:0x0041  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static int escape(java.lang.CharSequence r7, java.lang.StringBuilder r8) {
        /*
            if (r7 != 0) goto L4
            r0 = 0
            return r0
        L4:
            r0 = 0
            r1 = 0
            int r2 = r8.length()
        La:
            int r3 = r7.length()
            r4 = 2
            r5 = 39
            if (r1 >= r3) goto L55
            int r3 = java.lang.Character.codePointAt(r7, r1)
            r6 = 37
            if (r3 == r6) goto L41
            if (r3 == r5) goto L3b
            r6 = 43
            if (r3 == r6) goto L41
            r6 = 45
            if (r3 == r6) goto L41
            r6 = 164(0xa4, float:2.3E-43)
            if (r3 == r6) goto L41
            r6 = 8240(0x2030, float:1.1547E-41)
            if (r3 == r6) goto L41
            if (r0 != r4) goto L37
            r8.append(r5)
            r8.appendCodePoint(r3)
            r0 = 0
            goto L4f
        L37:
            r8.appendCodePoint(r3)
            goto L4f
        L3b:
            java.lang.String r4 = "''"
            r8.append(r4)
            goto L4f
        L41:
            if (r0 != 0) goto L4b
            r8.append(r5)
            r8.appendCodePoint(r3)
            r0 = 2
            goto L4f
        L4b:
            r8.appendCodePoint(r3)
        L4f:
            int r4 = java.lang.Character.charCount(r3)
            int r1 = r1 + r4
            goto La
        L55:
            if (r0 != r4) goto L5a
            r8.append(r5)
        L5a:
            int r3 = r8.length()
            int r3 = r3 - r2
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.number.AffixUtils.escape(java.lang.CharSequence, java.lang.StringBuilder):int");
    }

    public static String escape(CharSequence input) {
        if (input == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        escape(input, sb);
        return sb.toString();
    }

    public static final NumberFormat.Field getFieldForType(int type) {
        if (type != -15) {
            switch (type) {
                case -9:
                    return NumberFormat.Field.CURRENCY;
                case -8:
                    return NumberFormat.Field.CURRENCY;
                case -7:
                    return NumberFormat.Field.CURRENCY;
                case -6:
                    return NumberFormat.Field.CURRENCY;
                case -5:
                    return NumberFormat.Field.CURRENCY;
                case -4:
                    return NumberFormat.Field.PERMILLE;
                case -3:
                    return NumberFormat.Field.PERCENT;
                case -2:
                    return NumberFormat.Field.SIGN;
                case -1:
                    return NumberFormat.Field.SIGN;
                default:
                    throw new AssertionError();
            }
        }
        return NumberFormat.Field.CURRENCY;
    }

    public static int unescape(CharSequence affixPattern, FormattedStringBuilder output, int position, SymbolProvider provider, NumberFormat.Field field) {
        int length = 0;
        long tag = 0;
        while (hasNext(tag, affixPattern)) {
            tag = nextToken(tag, affixPattern);
            int typeOrCp = getTypeOrCp(tag);
            if (typeOrCp == -15) {
                length += output.insertCodePoint(position + length, UCharacter.REPLACEMENT_CHAR, NumberFormat.Field.CURRENCY);
            } else if (typeOrCp < 0) {
                length += output.insert(position + length, provider.getSymbol(typeOrCp), getFieldForType(typeOrCp));
            } else {
                length += output.insertCodePoint(position + length, typeOrCp, field);
            }
        }
        return length;
    }

    public static int unescapedCount(CharSequence affixPattern, boolean lengthOrCount, SymbolProvider provider) {
        int length = 0;
        long tag = 0;
        while (hasNext(tag, affixPattern)) {
            tag = nextToken(tag, affixPattern);
            int typeOrCp = getTypeOrCp(tag);
            if (typeOrCp == -15) {
                length++;
            } else if (typeOrCp < 0) {
                CharSequence symbol = provider.getSymbol(typeOrCp);
                length += lengthOrCount ? symbol.length() : Character.codePointCount(symbol, 0, symbol.length());
            } else {
                length += lengthOrCount ? Character.charCount(typeOrCp) : 1;
            }
        }
        return length;
    }

    public static boolean containsType(CharSequence affixPattern, int type) {
        if (affixPattern == null || affixPattern.length() == 0) {
            return false;
        }
        long tag = 0;
        while (hasNext(tag, affixPattern)) {
            tag = nextToken(tag, affixPattern);
            if (getTypeOrCp(tag) == type) {
                return true;
            }
        }
        return false;
    }

    public static boolean hasCurrencySymbols(CharSequence affixPattern) {
        if (affixPattern == null || affixPattern.length() == 0) {
            return false;
        }
        long tag = 0;
        while (hasNext(tag, affixPattern)) {
            tag = nextToken(tag, affixPattern);
            int typeOrCp = getTypeOrCp(tag);
            if (typeOrCp < 0 && getFieldForType(typeOrCp) == NumberFormat.Field.CURRENCY) {
                return true;
            }
        }
        return false;
    }

    public static String replaceType(CharSequence affixPattern, int type, char replacementChar) {
        if (affixPattern == null || affixPattern.length() == 0) {
            return "";
        }
        char[] chars = affixPattern.toString().toCharArray();
        long tag = 0;
        while (hasNext(tag, affixPattern)) {
            tag = nextToken(tag, affixPattern);
            if (getTypeOrCp(tag) == type) {
                int offset = getOffset(tag);
                chars[offset - 1] = replacementChar;
            }
        }
        return new String(chars);
    }

    public static boolean containsOnlySymbolsAndIgnorables(CharSequence affixPattern, UnicodeSet ignorables) {
        if (affixPattern == null) {
            return true;
        }
        long tag = 0;
        while (hasNext(tag, affixPattern)) {
            tag = nextToken(tag, affixPattern);
            int typeOrCp = getTypeOrCp(tag);
            if (typeOrCp >= 0 && !ignorables.contains(typeOrCp)) {
                return false;
            }
        }
        return true;
    }

    public static void iterateWithConsumer(CharSequence affixPattern, TokenConsumer consumer) {
        long tag = 0;
        while (hasNext(tag, affixPattern)) {
            tag = nextToken(tag, affixPattern);
            int typeOrCp = getTypeOrCp(tag);
            consumer.consumeToken(typeOrCp);
        }
    }

    private static long nextToken(long tag, CharSequence patternString) {
        int offset = getOffset(tag);
        int state = getState(tag);
        while (offset < patternString.length()) {
            int cp = Character.codePointAt(patternString, offset);
            int count = Character.charCount(cp);
            switch (state) {
                case 0:
                    if (cp == 37) {
                        return makeTag(offset + count, -3, 0, 0);
                    }
                    if (cp == 39) {
                        state = 1;
                        offset += count;
                        break;
                    } else {
                        if (cp == 43) {
                            return makeTag(offset + count, -2, 0, 0);
                        }
                        if (cp == 45) {
                            return makeTag(offset + count, -1, 0, 0);
                        }
                        if (cp == 164) {
                            state = 4;
                            offset += count;
                            break;
                        } else {
                            if (cp == 8240) {
                                return makeTag(offset + count, -4, 0, 0);
                            }
                            return makeTag(offset + count, 0, 0, cp);
                        }
                    }
                case 1:
                    if (cp != 39) {
                        return makeTag(offset + count, 0, 2, cp);
                    }
                    return makeTag(offset + count, 0, 0, cp);
                case 2:
                    if (cp != 39) {
                        return makeTag(offset + count, 0, 2, cp);
                    }
                    state = 3;
                    offset += count;
                    break;
                case 3:
                    if (cp == 39) {
                        return makeTag(offset + count, 0, 2, cp);
                    }
                    state = 0;
                    break;
                case 4:
                    if (cp == 164) {
                        state = 5;
                        offset += count;
                        break;
                    } else {
                        return makeTag(offset, -5, 0, 0);
                    }
                case 5:
                    if (cp == 164) {
                        state = 6;
                        offset += count;
                        break;
                    } else {
                        return makeTag(offset, -6, 0, 0);
                    }
                case 6:
                    if (cp == 164) {
                        state = 7;
                        offset += count;
                        break;
                    } else {
                        return makeTag(offset, -7, 0, 0);
                    }
                case 7:
                    if (cp == 164) {
                        state = 8;
                        offset += count;
                        break;
                    } else {
                        return makeTag(offset, -8, 0, 0);
                    }
                case 8:
                    if (cp == 164) {
                        state = 9;
                        offset += count;
                        break;
                    } else {
                        return makeTag(offset, -9, 0, 0);
                    }
                case 9:
                    if (cp == 164) {
                        offset += count;
                        break;
                    } else {
                        return makeTag(offset, -15, 0, 0);
                    }
                default:
                    throw new AssertionError();
            }
        }
        switch (state) {
            case 0:
                return -1L;
            case 1:
            case 2:
                throw new IllegalArgumentException("Unterminated quote in pattern affix: \"" + ((Object) patternString) + "\"");
            case 3:
                return -1L;
            case 4:
                return makeTag(offset, -5, 0, 0);
            case 5:
                return makeTag(offset, -6, 0, 0);
            case 6:
                return makeTag(offset, -7, 0, 0);
            case 7:
                return makeTag(offset, -8, 0, 0);
            case 8:
                return makeTag(offset, -9, 0, 0);
            case 9:
                return makeTag(offset, -15, 0, 0);
            default:
                throw new AssertionError();
        }
    }

    private static boolean hasNext(long tag, CharSequence string) {
        int state = getState(tag);
        int offset = getOffset(tag);
        if (state == 2 && offset == string.length() - 1 && string.charAt(offset) == '\'') {
            return false;
        }
        return state != 0 || offset < string.length();
    }

    private static int getTypeOrCp(long tag) {
        int type = getType(tag);
        return type == 0 ? getCodePoint(tag) : -type;
    }

    private static long makeTag(int offset, int type, int state, int cp) {
        long tag = 0 | offset;
        return tag | ((-type) << 32) | (state << 36) | (cp << 40);
    }

    private static int getOffset(long tag) {
        return (int) ((-1) & tag);
    }

    private static int getType(long tag) {
        return (int) ((tag >>> 32) & 15);
    }

    private static int getState(long tag) {
        return (int) ((tag >>> 36) & 15);
    }

    private static int getCodePoint(long tag) {
        return (int) (tag >>> 40);
    }
}
