package aegon.chrome.net.impl;

import aegon.chrome.base.ContextUtils;
import aegon.chrome.base.Log;
import aegon.chrome.base.annotations.JNINamespace;
import aegon.chrome.net.NetworkChangeNotifier;
import aegon.chrome.net.impl.SafeNativeFunctionCaller;
import android.content.Context;
import android.os.ConditionVariable;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Process;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace("cronet")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class CronetLibraryLoader {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static volatile boolean sInitThreadInitDone;
    private static final Object sLoadLock = new Object();
    private static final String LIBRARY_NAME = "cronet." + ImplVersion.getCronetVersion();
    private static final String TAG = CronetLibraryLoader.class.getSimpleName();
    private static InitThreadHandler sInitThreadHandler = new DefaultInitThreadHandler();
    private static volatile boolean sLibraryLoaded = false;
    private static final ConditionVariable sWaitForLibLoad = new ConditionVariable();

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    public interface InitThreadHandler {
        boolean onInitThread();

        void postTask(Runnable runnable);
    }

    interface Natives {
        void cronetInitOnInitThread();

        String getCronetVersion();
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static class DefaultInitThreadHandler implements InitThreadHandler {
        private final Object sInitLock;
        private HandlerThread sInitThread;

        private DefaultInitThreadHandler() {
            this.sInitLock = new Object();
        }

        private void ensureThread() {
            synchronized (this.sInitLock) {
                if (this.sInitThread == null) {
                    this.sInitThread = new HandlerThread("CronetInit");
                }
                if (!this.sInitThread.isAlive()) {
                    this.sInitThread.start();
                }
            }
        }

        @Override // aegon.chrome.net.impl.CronetLibraryLoader.InitThreadHandler
        public boolean onInitThread() {
            ensureThread();
            return this.sInitThread.getLooper() == Looper.myLooper();
        }

        @Override // aegon.chrome.net.impl.CronetLibraryLoader.InitThreadHandler
        public void postTask(Runnable runnable) {
            ensureThread();
            if (onInitThread()) {
                runnable.run();
            } else {
                new Handler(this.sInitThread.getLooper()).post(runnable);
            }
        }
    }

    public static void setInitThreadHandler(InitThreadHandler initThreadHandler) {
        if (initThreadHandler != null) {
            sInitThreadHandler = initThreadHandler;
        }
    }

    public static void ensureInitialized(Context context, CronetEngineBuilderImpl cronetEngineBuilderImpl) {
        synchronized (sLoadLock) {
            if (!sInitThreadInitDone) {
                ContextUtils.initApplicationContext(context);
                postToInitThread(new Runnable() { // from class: aegon.chrome.net.impl.CronetLibraryLoader.1
                    @Override // java.lang.Runnable
                    public void run() {
                        CronetLibraryLoader.ensureInitializedOnInitThread();
                    }
                });
            }
            if (!sLibraryLoaded) {
                if (cronetEngineBuilderImpl.libraryLoader() != null) {
                    cronetEngineBuilderImpl.libraryLoader().loadLibrary(LIBRARY_NAME);
                } else {
                    System.loadLibrary(LIBRARY_NAME);
                }
                String cronetVersion = ImplVersion.getCronetVersion();
                if (!cronetVersion.equals(SafeNativeFunctionCaller.EnsureResult(new SafeNativeFunctionCaller.Supplier() { // from class: aegon.chrome.net.impl.-$$Lambda$CronetLibraryLoader$olrDOdD5DwZ6q_My_YegzgwWLBw
                    @Override // aegon.chrome.net.impl.SafeNativeFunctionCaller.Supplier
                    public final Object get() {
                        return CronetLibraryLoaderJni.get().getCronetVersion();
                    }
                }))) {
                    throw new RuntimeException(String.format("Expected Cronet version number %s, actual version number %s.", cronetVersion, CronetLibraryLoaderJni.get().getCronetVersion()));
                }
                Log.m44i(TAG, "Cronet version: %s, arch: %s", cronetVersion, System.getProperty("os.arch"));
                sLibraryLoaded = true;
                sWaitForLibLoad.open();
            }
        }
    }

    private static boolean onInitThread() {
        return sInitThreadHandler.onInitThread();
    }

    static void ensureInitializedOnInitThread() {
        if (sInitThreadInitDone) {
            return;
        }
        NetworkChangeNotifier.init();
        NetworkChangeNotifier.registerToReceiveNotificationsAlways();
        sWaitForLibLoad.block();
        SafeNativeFunctionCaller.Ensure(new Runnable() { // from class: aegon.chrome.net.impl.-$$Lambda$CronetLibraryLoader$80YFPLyLB2wVqKBttMndPCIbrN0
            @Override // java.lang.Runnable
            public final void run() {
                CronetLibraryLoaderJni.get().cronetInitOnInitThread();
            }
        });
        sInitThreadInitDone = true;
    }

    public static void postToInitThread(Runnable runnable) {
        sInitThreadHandler.postTask(runnable);
    }

    private static String getDefaultUserAgent() {
        return UserAgent.from(ContextUtils.getApplicationContext());
    }

    private static void ensureInitializedFromNative() {
        synchronized (sLoadLock) {
            sLibraryLoaded = true;
            sWaitForLibLoad.open();
        }
        ensureInitialized(ContextUtils.getApplicationContext(), null);
    }

    private static void setNetworkThreadPriorityOnNetworkThread(int i) throws SecurityException, IllegalArgumentException {
        Process.setThreadPriority(i);
    }
}
