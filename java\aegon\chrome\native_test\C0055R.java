package aegon.chrome.native_test;

import p002a.p007b.p008a.p009a.p010a.C0007a;

/* renamed from: aegon.chrome.native_test.R */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class C0055R {

    /* renamed from: aegon.chrome.native_test.R$anim */
    public static final class anim extends C0007a.a {
    }

    /* renamed from: aegon.chrome.native_test.R$animator */
    public static final class animator extends C0007a.b {
    }

    /* renamed from: aegon.chrome.native_test.R$array */
    public static final class array extends C0007a.c {
    }

    /* renamed from: aegon.chrome.native_test.R$attr */
    public static final class attr extends C0007a.d {
    }

    /* renamed from: aegon.chrome.native_test.R$bool */
    public static final class bool extends C0007a.e {
    }

    /* renamed from: aegon.chrome.native_test.R$color */
    public static final class color extends C0007a.f {
    }

    /* renamed from: aegon.chrome.native_test.R$dimen */
    public static final class dimen extends C0007a.g {
    }

    /* renamed from: aegon.chrome.native_test.R$drawable */
    public static final class drawable extends C0007a.h {
    }

    /* renamed from: aegon.chrome.native_test.R$font */
    public static final class font extends C0007a.i {
    }

    /* renamed from: aegon.chrome.native_test.R$fraction */
    public static final class fraction extends C0007a.j {
    }

    /* renamed from: aegon.chrome.native_test.R$id */
    public static final class id extends C0007a.k {
    }

    /* renamed from: aegon.chrome.native_test.R$integer */
    public static final class integer extends C0007a.l {
    }

    /* renamed from: aegon.chrome.native_test.R$interpolator */
    public static final class interpolator extends C0007a.m {
    }

    /* renamed from: aegon.chrome.native_test.R$layout */
    public static final class layout extends C0007a.n {
    }

    /* renamed from: aegon.chrome.native_test.R$macro */
    public static final class macro extends C0007a.o {
    }

    /* renamed from: aegon.chrome.native_test.R$menu */
    public static final class menu extends C0007a.p {
    }

    /* renamed from: aegon.chrome.native_test.R$mipmap */
    public static final class mipmap extends C0007a.q {
    }

    /* renamed from: aegon.chrome.native_test.R$plurals */
    public static final class plurals extends C0007a.r {
    }

    /* renamed from: aegon.chrome.native_test.R$raw */
    public static final class raw extends C0007a.s {
    }

    /* renamed from: aegon.chrome.native_test.R$string */
    public static final class string extends C0007a.t {
    }

    /* renamed from: aegon.chrome.native_test.R$style */
    public static final class style extends C0007a.u {
    }

    /* renamed from: aegon.chrome.native_test.R$styleable */
    public static final class styleable extends C0007a.v {
    }

    /* renamed from: aegon.chrome.native_test.R$transition */
    public static final class transition extends C0007a.w {
    }

    /* renamed from: aegon.chrome.native_test.R$xml */
    public static final class xml extends C0007a.x {
    }

    public static void onResourcesLoaded(int i) {
    }
}
