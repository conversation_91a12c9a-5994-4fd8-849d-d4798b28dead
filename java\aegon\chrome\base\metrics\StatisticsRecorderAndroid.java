package aegon.chrome.base.metrics;

import aegon.chrome.base.annotations.JNINamespace;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@JNINamespace("base::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class StatisticsRecorderAndroid {

    interface Natives {
        String toJson(int i);
    }

    private StatisticsRecorderAndroid() {
    }

    public static String toJson(int i) {
        return StatisticsRecorderAndroidJni.get().toJson(i);
    }
}
