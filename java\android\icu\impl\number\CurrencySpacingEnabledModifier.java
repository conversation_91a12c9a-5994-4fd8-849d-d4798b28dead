package android.icu.impl.number;

import android.icu.impl.FormattedStringBuilder;
import android.icu.text.DecimalFormatSymbols;
import android.icu.text.NumberFormat;
import android.icu.text.UnicodeSet;
import java.text.Format;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class CurrencySpacingEnabledModifier extends ConstantMultiFieldModifier {
    static final short IN_CURRENCY = 0;
    static final short IN_NUMBER = 1;
    static final byte PREFIX = 0;
    static final byte SUFFIX = 1;
    private static final UnicodeSet UNISET_DIGIT = new UnicodeSet("[:digit:]").freeze();
    private static final UnicodeSet UNISET_NOTS = new UnicodeSet("[:^S:]").freeze();
    private final String afterPrefixInsert;
    private final UnicodeSet afterPrefixUnicodeSet;
    private final String beforeSuffixInsert;
    private final UnicodeSet beforeSuffixUnicodeSet;

    public CurrencySpacingEnabledModifier(FormattedStringBuilder prefix, FormattedStringBuilder suffix, boolean overwrite, boolean strong, DecimalFormatSymbols symbols) {
        super(prefix, suffix, overwrite, strong);
        if (prefix.length() > 0 && prefix.fieldAt(prefix.length() - 1) == NumberFormat.Field.CURRENCY) {
            int prefixCp = prefix.getLastCodePoint();
            UnicodeSet prefixUnicodeSet = getUnicodeSet(symbols, (short) 0, (byte) 0);
            if (prefixUnicodeSet.contains(prefixCp)) {
                UnicodeSet unicodeSet = getUnicodeSet(symbols, (short) 1, (byte) 0);
                this.afterPrefixUnicodeSet = unicodeSet;
                unicodeSet.freeze();
                this.afterPrefixInsert = getInsertString(symbols, (byte) 0);
            } else {
                this.afterPrefixUnicodeSet = null;
                this.afterPrefixInsert = null;
            }
        } else {
            this.afterPrefixUnicodeSet = null;
            this.afterPrefixInsert = null;
        }
        if (suffix.length() > 0 && suffix.fieldAt(0) == NumberFormat.Field.CURRENCY) {
            int suffixCp = suffix.getLastCodePoint();
            UnicodeSet suffixUnicodeSet = getUnicodeSet(symbols, (short) 0, (byte) 1);
            if (suffixUnicodeSet.contains(suffixCp)) {
                UnicodeSet unicodeSet2 = getUnicodeSet(symbols, (short) 1, (byte) 1);
                this.beforeSuffixUnicodeSet = unicodeSet2;
                unicodeSet2.freeze();
                this.beforeSuffixInsert = getInsertString(symbols, (byte) 1);
                return;
            }
            this.beforeSuffixUnicodeSet = null;
            this.beforeSuffixInsert = null;
            return;
        }
        this.beforeSuffixUnicodeSet = null;
        this.beforeSuffixInsert = null;
    }

    @Override // android.icu.impl.number.ConstantMultiFieldModifier, android.icu.impl.number.Modifier
    public int apply(FormattedStringBuilder output, int leftIndex, int rightIndex) {
        UnicodeSet unicodeSet;
        UnicodeSet unicodeSet2;
        int length = 0;
        if (rightIndex - leftIndex > 0 && (unicodeSet2 = this.afterPrefixUnicodeSet) != null && unicodeSet2.contains(output.codePointAt(leftIndex))) {
            length = 0 + output.insert(leftIndex, this.afterPrefixInsert, (Format.Field) null);
        }
        if (rightIndex - leftIndex > 0 && (unicodeSet = this.beforeSuffixUnicodeSet) != null && unicodeSet.contains(output.codePointBefore(rightIndex))) {
            length += output.insert(rightIndex + length, this.beforeSuffixInsert, (Format.Field) null);
        }
        return length + super.apply(output, leftIndex, rightIndex + length);
    }

    public static int applyCurrencySpacing(FormattedStringBuilder output, int prefixStart, int prefixLen, int suffixStart, int suffixLen, DecimalFormatSymbols symbols) {
        int length = 0;
        boolean hasPrefix = prefixLen > 0;
        boolean hasSuffix = suffixLen > 0;
        boolean hasNumber = (suffixStart - prefixStart) - prefixLen > 0;
        if (hasPrefix && hasNumber) {
            length = 0 + applyCurrencySpacingAffix(output, prefixStart + prefixLen, (byte) 0, symbols);
        }
        if (hasSuffix && hasNumber) {
            return length + applyCurrencySpacingAffix(output, suffixStart + length, (byte) 1, symbols);
        }
        return length;
    }

    private static int applyCurrencySpacingAffix(FormattedStringBuilder output, int index, byte affix, DecimalFormatSymbols symbols) {
        Format.Field affixField = affix == 0 ? output.fieldAt(index - 1) : output.fieldAt(index);
        if (affixField != NumberFormat.Field.CURRENCY) {
            return 0;
        }
        int affixCp = affix == 0 ? output.codePointBefore(index) : output.codePointAt(index);
        UnicodeSet affixUniset = getUnicodeSet(symbols, (short) 0, affix);
        if (!affixUniset.contains(affixCp)) {
            return 0;
        }
        int numberCp = affix == 0 ? output.codePointAt(index) : output.codePointBefore(index);
        UnicodeSet numberUniset = getUnicodeSet(symbols, (short) 1, affix);
        if (!numberUniset.contains(numberCp)) {
            return 0;
        }
        String spacingString = getInsertString(symbols, affix);
        return output.insert(index, spacingString, (Format.Field) null);
    }

    private static UnicodeSet getUnicodeSet(DecimalFormatSymbols symbols, short position, byte affix) {
        int i;
        if (position == 0) {
            i = 0;
        } else {
            i = 1;
        }
        String pattern = symbols.getPatternForCurrencySpacing(i, affix == 1);
        if (pattern.equals("[:digit:]")) {
            return UNISET_DIGIT;
        }
        if (pattern.equals("[:^S:]")) {
            return UNISET_NOTS;
        }
        return new UnicodeSet(pattern);
    }

    private static String getInsertString(DecimalFormatSymbols symbols, byte affix) {
        return symbols.getPatternForCurrencySpacing(2, affix == 1);
    }
}
