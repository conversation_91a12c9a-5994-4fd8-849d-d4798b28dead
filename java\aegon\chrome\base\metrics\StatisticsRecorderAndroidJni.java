package aegon.chrome.base.metrics;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.metrics.StatisticsRecorderAndroid;
import aegon.chrome.base.natives.GEN_JNI;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class StatisticsRecorderAndroidJni implements StatisticsRecorderAndroid.Natives {
    public static final JniStaticTestMocker<StatisticsRecorderAndroid.Natives> TEST_HOOKS = new JniStaticTestMocker<StatisticsRecorderAndroid.Natives>() { // from class: aegon.chrome.base.metrics.StatisticsRecorderAndroidJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(StatisticsRecorderAndroid.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                StatisticsRecorderAndroid.Natives unused = StatisticsRecorderAndroidJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static StatisticsRecorderAndroid.Natives testInstance;

    StatisticsRecorderAndroidJni() {
    }

    @Override // aegon.chrome.base.metrics.StatisticsRecorderAndroid.Natives
    public String toJson(int i) {
        return GEN_JNI.org_chromium_base_metrics_StatisticsRecorderAndroid_toJson(i);
    }

    public static StatisticsRecorderAndroid.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            StatisticsRecorderAndroid.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.metrics.StatisticsRecorderAndroid.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new StatisticsRecorderAndroidJni();
    }
}
