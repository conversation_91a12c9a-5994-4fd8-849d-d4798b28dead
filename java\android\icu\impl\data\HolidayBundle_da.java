package android.icu.impl.data;

import java.util.ListResourceBundle;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class HolidayBundle_da extends ListResourceBundle {
    private static final Object[][] fContents = {new Object[]{"Armistice Day", "våbenhvile"}, new Object[]{"Ascension", "himmelfart"}, new Object[]{"Boxing Day", "anden juledag"}, new Object[]{"Christmas Eve", "juleaften"}, new Object[]{"Easter", "påske"}, new Object[]{"Epiphany", "helligtrekongersdag"}, new Object[]{"Good Friday", "langfredag"}, new Object[]{"Halloween", "allehelgensaften"}, new Object[]{"Maundy Thursday", "skærtorsdag"}, new Object[]{"Palm Sunday", "palmesøndag"}, new Object[]{"Pentecost", "pinse"}, new Object[]{"Shrove Tuesday", "hvidetirsdag"}};

    @Override // java.util.ListResourceBundle
    public synchronized Object[][] getContents() {
        return fContents;
    }
}
