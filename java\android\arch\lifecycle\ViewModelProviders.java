package android.arch.lifecycle;

import android.app.Activity;
import android.app.Application;
import android.arch.lifecycle.ViewModelProvider;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentActivity;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
public class ViewModelProviders {
    @Deprecated
    public ViewModelProviders() {
    }

    private static Application checkApplication(Activity activity) {
        Application application = activity.getApplication();
        if (application != null) {
            return application;
        }
        throw new IllegalStateException("Your activity/fragment is not yet attached to Application. You can't request ViewModel before onCreate call.");
    }

    private static Activity checkActivity(Fragment fragment) {
        FragmentActivity activity = fragment.getActivity();
        if (activity != null) {
            return activity;
        }
        throw new IllegalStateException("Can't create ViewModelProvider for detached fragment");
    }

    /* renamed from: of */
    public static ViewModelProvider m72of(Fragment fragment) {
        return m73of(fragment, (ViewModelProvider.Factory) null);
    }

    /* renamed from: of */
    public static ViewModelProvider m74of(FragmentActivity fragmentActivity) {
        return m75of(fragmentActivity, (ViewModelProvider.Factory) null);
    }

    /* renamed from: of */
    public static ViewModelProvider m73of(Fragment fragment, ViewModelProvider.Factory factory) {
        Application applicationCheckApplication = checkApplication(checkActivity(fragment));
        if (factory == null) {
            factory = ViewModelProvider.AndroidViewModelFactory.getInstance(applicationCheckApplication);
        }
        return new ViewModelProvider(ViewModelStores.m76of(fragment), factory);
    }

    /* renamed from: of */
    public static ViewModelProvider m75of(FragmentActivity fragmentActivity, ViewModelProvider.Factory factory) {
        Application applicationCheckApplication = checkApplication(fragmentActivity);
        if (factory == null) {
            factory = ViewModelProvider.AndroidViewModelFactory.getInstance(applicationCheckApplication);
        }
        return new ViewModelProvider(ViewModelStores.m77of(fragmentActivity), factory);
    }

    @Deprecated
    public static class DefaultFactory extends ViewModelProvider.AndroidViewModelFactory {
        @Deprecated
        public DefaultFactory(Application application) {
            super(application);
        }
    }
}
