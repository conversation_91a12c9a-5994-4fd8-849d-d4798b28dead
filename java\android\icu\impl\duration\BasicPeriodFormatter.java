package android.icu.impl.duration;

import android.icu.impl.duration.BasicPeriodFormatterFactory;
import android.icu.impl.duration.impl.PeriodFormatterData;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
class BasicPeriodFormatter implements PeriodFormatter {
    private BasicPeriodFormatterFactory.Customizations customs;
    private PeriodFormatterData data;
    private BasicPeriodFormatterFactory factory;
    private String localeName;

    BasicPeriodFormatter(BasicPeriodFormatterFactory factory, String localeName, PeriodFormatterData data, BasicPeriodFormatterFactory.Customizations customs) {
        this.factory = factory;
        this.localeName = localeName;
        this.data = data;
        this.customs = customs;
    }

    @Override // android.icu.impl.duration.PeriodFormatter
    public String format(Period period) {
        if (!period.isSet()) {
            throw new IllegalArgumentException("period is not set");
        }
        return format(period.timeLimit, period.inFuture, period.counts);
    }

    @Override // android.icu.impl.duration.PeriodFormatter
    public PeriodFormatter withLocale(String locName) {
        if (!this.localeName.equals(locName)) {
            PeriodFormatterData newData = this.factory.getData(locName);
            return new BasicPeriodFormatter(this.factory, locName, newData, this.customs);
        }
        return this;
    }

    /* JADX WARN: Multi-variable type inference failed */
    private String format(int tl, boolean inFuture, int[] counts) {
        int i;
        int td;
        boolean wasSkipped;
        boolean skipped;
        int j;
        int cv;
        char c2;
        boolean zAppendUnitSeparator;
        int[] iArr = counts;
        int mask = 0;
        int i2 = 0;
        while (true) {
            i = 1;
            if (i2 >= iArr.length) {
                break;
            }
            if (iArr[i2] > 0) {
                mask |= 1 << i2;
            }
            i2++;
        }
        if (!this.data.allowZero()) {
            int i3 = 0;
            int m = 1;
            while (i3 < iArr.length) {
                if ((mask & m) != 0 && iArr[i3] == 1) {
                    mask &= ~m;
                }
                i3++;
                m <<= 1;
            }
            if (mask == 0) {
                return null;
            }
        }
        boolean forceD3Seconds = false;
        if (this.data.useMilliseconds() != 0 && ((1 << TimeUnit.MILLISECOND.ordinal) & mask) != 0) {
            int sx = TimeUnit.SECOND.ordinal;
            int mx = TimeUnit.MILLISECOND.ordinal;
            int sf = 1 << sx;
            int mf = 1 << mx;
            int iUseMilliseconds = this.data.useMilliseconds();
            if (iUseMilliseconds != 1) {
                if (iUseMilliseconds == 2 && (mask & sf) != 0) {
                    iArr[sx] = iArr[sx] + ((iArr[mx] - 1) / 1000);
                    mask &= ~mf;
                    forceD3Seconds = true;
                }
            } else {
                if ((mask & sf) == 0) {
                    mask |= sf;
                    iArr[sx] = 1;
                }
                iArr[sx] = iArr[sx] + ((iArr[mx] - 1) / 1000);
                mask &= ~mf;
                forceD3Seconds = true;
            }
        }
        int first = 0;
        int last = iArr.length - 1;
        while (first < iArr.length && ((1 << first) & mask) == 0) {
            first++;
        }
        while (last > first && ((1 << last) & mask) == 0) {
            last--;
        }
        boolean isZero = true;
        int i4 = first;
        while (true) {
            if (i4 <= last) {
                if (((1 << i4) & mask) == 0 || iArr[i4] <= 1) {
                    i4++;
                } else {
                    isZero = false;
                    break;
                }
            } else {
                break;
            }
        }
        StringBuffer sb = new StringBuffer();
        int tl2 = (!this.customs.displayLimit || isZero) ? 0 : tl;
        if (!this.customs.displayDirection || isZero) {
            td = 0;
        } else {
            td = inFuture ? 2 : 1;
        }
        boolean useDigitPrefix = this.data.appendPrefix(tl2, td, sb);
        boolean multiple = first != last;
        boolean wasSkipped2 = true;
        boolean skipped2 = false;
        boolean countSep = this.customs.separatorVariant != 0;
        int i5 = first;
        int j2 = i5;
        boolean useDigitPrefix2 = useDigitPrefix;
        int i6 = i5;
        while (i6 <= last) {
            if (!skipped2) {
                wasSkipped = wasSkipped2;
                skipped = skipped2;
            } else {
                this.data.appendSkippedUnit(sb);
                wasSkipped = true;
                skipped = false;
            }
            while (true) {
                j = j2 + 1;
                if (j >= last || ((i << j) & mask) != 0) {
                    break;
                }
                skipped = true;
                j2 = j;
            }
            TimeUnit unit = TimeUnit.units[i6];
            int count = iArr[i6] - 1;
            int cv2 = this.customs.countVariant;
            if (i6 == last) {
                if (!forceD3Seconds) {
                    cv = cv2;
                } else {
                    cv = 5;
                }
            } else {
                cv = 0;
            }
            int i7 = i6;
            int mask2 = mask;
            boolean forceD3Seconds2 = forceD3Seconds;
            int td2 = td;
            int td3 = cv;
            boolean isZero2 = isZero;
            int tl3 = tl2;
            boolean mustSkip = this.data.appendUnit(unit, count, td3, this.customs.unitVariant, countSep, useDigitPrefix2, multiple, i6 == last ? i : 0, wasSkipped, sb);
            skipped2 = skipped | mustSkip;
            wasSkipped2 = false;
            if (this.customs.separatorVariant == 0 || j > last) {
                c2 = 2;
                zAppendUnitSeparator = false;
            } else {
                boolean afterFirst = i7 == first;
                boolean beforeLast = j == last;
                c2 = 2;
                boolean fullSep = this.customs.separatorVariant == 2;
                zAppendUnitSeparator = this.data.appendUnitSeparator(unit, fullSep, afterFirst, beforeLast, sb);
            }
            useDigitPrefix2 = zAppendUnitSeparator;
            j2 = j;
            td = td2;
            tl2 = tl3;
            mask = mask2;
            forceD3Seconds = forceD3Seconds2;
            isZero = isZero2;
            i = 1;
            i6 = j;
            iArr = counts;
        }
        this.data.appendSuffix(tl2, td, sb);
        return sb.toString();
    }
}
