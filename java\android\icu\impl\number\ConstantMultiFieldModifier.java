package android.icu.impl.number;

import android.icu.impl.FormattedStringBuilder;
import android.icu.impl.number.Modifier;
import java.text.Format;
import java.util.Arrays;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class ConstantMultiFieldModifier implements Modifier {
    private final boolean overwrite;
    private final Modifier.Parameters parameters;
    protected final char[] prefixChars;
    protected final Format.Field[] prefixFields;
    private final boolean strong;
    protected final char[] suffixChars;
    protected final Format.Field[] suffixFields;

    public ConstantMultiFieldModifier(FormattedStringBuilder prefix, FormattedStringBuilder suffix, boolean overwrite, boolean strong) {
        this(prefix, suffix, overwrite, strong, null);
    }

    public ConstantMultiFieldModifier(FormattedStringBuilder prefix, FormattedStringBuilder suffix, boolean overwrite, boolean strong, Modifier.Parameters parameters) {
        this.prefixChars = prefix.toCharArray();
        this.suffixChars = suffix.toCharArray();
        this.prefixFields = prefix.toFieldArray();
        this.suffixFields = suffix.toFieldArray();
        this.overwrite = overwrite;
        this.strong = strong;
        this.parameters = parameters;
    }

    @Override // android.icu.impl.number.Modifier
    public int apply(FormattedStringBuilder output, int leftIndex, int rightIndex) {
        int length = output.insert(leftIndex, this.prefixChars, this.prefixFields);
        if (this.overwrite) {
            length += output.splice(leftIndex + length, rightIndex + length, "", 0, 0, null);
        }
        return length + output.insert(rightIndex + length, this.suffixChars, this.suffixFields);
    }

    @Override // android.icu.impl.number.Modifier
    public int getPrefixLength() {
        return this.prefixChars.length;
    }

    @Override // android.icu.impl.number.Modifier
    public int getCodePointCount() {
        char[] cArr = this.prefixChars;
        int iCodePointCount = Character.codePointCount(cArr, 0, cArr.length);
        char[] cArr2 = this.suffixChars;
        return iCodePointCount + Character.codePointCount(cArr2, 0, cArr2.length);
    }

    @Override // android.icu.impl.number.Modifier
    public boolean isStrong() {
        return this.strong;
    }

    @Override // android.icu.impl.number.Modifier
    public boolean containsField(Format.Field field) {
        int i = 0;
        while (true) {
            Format.Field[] fieldArr = this.prefixFields;
            if (i < fieldArr.length) {
                if (fieldArr[i] == field) {
                    return true;
                }
                i++;
            } else {
                int i2 = 0;
                while (true) {
                    Format.Field[] fieldArr2 = this.suffixFields;
                    if (i2 < fieldArr2.length) {
                        if (fieldArr2[i2] == field) {
                            return true;
                        }
                        i2++;
                    } else {
                        return false;
                    }
                }
            }
        }
    }

    @Override // android.icu.impl.number.Modifier
    public Modifier.Parameters getParameters() {
        return this.parameters;
    }

    @Override // android.icu.impl.number.Modifier
    public boolean semanticallyEquivalent(Modifier other) {
        if (!(other instanceof ConstantMultiFieldModifier)) {
            return false;
        }
        ConstantMultiFieldModifier _other = (ConstantMultiFieldModifier) other;
        Modifier.Parameters parameters = this.parameters;
        if (parameters == null || _other.parameters == null || parameters.obj != _other.parameters.obj) {
            return Arrays.equals(this.prefixChars, _other.prefixChars) && Arrays.equals(this.prefixFields, _other.prefixFields) && Arrays.equals(this.suffixChars, _other.suffixChars) && Arrays.equals(this.suffixFields, _other.suffixFields) && this.overwrite == _other.overwrite && this.strong == _other.strong;
        }
        return true;
    }

    public String toString() {
        FormattedStringBuilder temp = new FormattedStringBuilder();
        apply(temp, 0, 0);
        int prefixLength = getPrefixLength();
        return String.format("<ConstantMultiFieldModifier prefix:'%s' suffix:'%s'>", temp.subSequence(0, prefixLength), temp.subSequence(prefixLength, temp.length()));
    }
}
