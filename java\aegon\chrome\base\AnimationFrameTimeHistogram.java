package aegon.chrome.base;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.TimeAnimator;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
public class AnimationFrameTimeHistogram {
    private static final int MAX_FRAME_TIME_NUM = 600;
    private static final String TAG = "AnimationFrameTimeHistogram";
    private final String mHistogramName;
    private final Recorder mRecorder = new Recorder();

    interface Natives {
        void saveHistogram(String str, long[] jArr, int i);
    }

    public static Animator.AnimatorListener getAnimatorRecorder(final String str) {
        return new AnimatorListenerAdapter() { // from class: aegon.chrome.base.AnimationFrameTimeHistogram.1
            private final AnimationFrameTimeHistogram mAnimationFrameTimeHistogram;

            {
                this.mAnimationFrameTimeHistogram = new AnimationFrameTimeHistogram(str);
            }

            @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
            public void onAnimationStart(Animator animator) {
                this.mAnimationFrameTimeHistogram.startRecording();
            }

            @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
            public void onAnimationEnd(Animator animator) {
                this.mAnimationFrameTimeHistogram.endRecording();
            }

            @Override // android.animation.AnimatorListenerAdapter, android.animation.Animator.AnimatorListener
            public void onAnimationCancel(Animator animator) {
                this.mAnimationFrameTimeHistogram.endRecording();
            }
        };
    }

    public AnimationFrameTimeHistogram(String str) {
        this.mHistogramName = str;
    }

    public void startRecording() {
        this.mRecorder.startRecording();
    }

    public void endRecording() {
        if (this.mRecorder.endRecording()) {
            AnimationFrameTimeHistogramJni.get().saveHistogram(this.mHistogramName, this.mRecorder.getFrameTimesMs(), this.mRecorder.getFrameTimesCount());
        }
        this.mRecorder.cleanUp();
    }

    static class Recorder implements TimeAnimator.TimeListener {
        static final /* synthetic */ boolean $assertionsDisabled = false;
        private final TimeAnimator mAnimator;
        private int mFrameTimesCount;
        private long[] mFrameTimesMs;

        private Recorder() {
            this.mAnimator = new TimeAnimator();
            this.mAnimator.setTimeListener(this);
        }

        private void startRecording() {
            this.mFrameTimesCount = 0;
            this.mFrameTimesMs = new long[600];
            this.mAnimator.start();
        }

        private boolean endRecording() {
            boolean zIsStarted = this.mAnimator.isStarted();
            this.mAnimator.end();
            return zIsStarted;
        }

        private long[] getFrameTimesMs() {
            return this.mFrameTimesMs;
        }

        private int getFrameTimesCount() {
            return this.mFrameTimesCount;
        }

        private void cleanUp() {
            this.mFrameTimesMs = null;
        }

        @Override // android.animation.TimeAnimator.TimeListener
        public void onTimeUpdate(TimeAnimator timeAnimator, long j, long j2) {
            int i = this.mFrameTimesCount;
            long[] jArr = this.mFrameTimesMs;
            if (i == jArr.length) {
                this.mAnimator.end();
                cleanUp();
                android.util.Log.w(AnimationFrameTimeHistogram.TAG, "Animation frame time recording reached the maximum number. It's eitherthe animation took too long or recording end is not called.");
            } else if (j2 > 0) {
                this.mFrameTimesCount = i + 1;
                jArr[i] = j2;
            }
        }
    }
}
