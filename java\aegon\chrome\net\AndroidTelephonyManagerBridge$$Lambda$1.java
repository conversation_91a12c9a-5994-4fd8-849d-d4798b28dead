package aegon.chrome.net;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class AndroidTelephonyManagerBridge$$Lambda$1 implements Runnable {
    private final AndroidTelephonyManagerBridge arg$1;

    private AndroidTelephonyManagerBridge$$Lambda$1(AndroidTelephonyManagerBridge androidTelephonyManagerBridge) {
        this.arg$1 = androidTelephonyManagerBridge;
    }

    public static Runnable lambdaFactory$(AndroidTelephonyManagerBridge androidTelephonyManagerBridge) {
        return new AndroidTelephonyManagerBridge$$Lambda$1(androidTelephonyManagerBridge);
    }

    @Override // java.lang.Runnable
    public final void run() {
        AndroidTelephonyManagerBridge.lambda$create$0(this.arg$1);
    }
}
