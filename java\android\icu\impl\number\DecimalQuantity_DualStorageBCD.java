package android.icu.impl.number;

import java.math.BigDecimal;
import java.math.BigInteger;
import org.apache.xpath.XPath;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class DecimalQuantity_DualStorageBCD extends DecimalQuantity_AbstractBCD {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private byte[] bcdBytes;
    private long bcdLong = 0;
    private boolean usingBytes = false;

    @Override // android.icu.impl.number.DecimalQuantity
    public int maxRepresentableDigits() {
        return Integer.MAX_VALUE;
    }

    public DecimalQuantity_DualStorageBCD() {
        setBcdToZero();
        this.flags = (byte) 0;
    }

    public DecimalQuantity_DualStorageBCD(long input) {
        setToLong(input);
    }

    public DecimalQuantity_DualStorageBCD(int input) {
        setToInt(input);
    }

    public DecimalQuantity_DualStorageBCD(double input) {
        setToDouble(input);
    }

    public DecimalQuantity_DualStorageBCD(BigInteger input) {
        setToBigInteger(input);
    }

    public DecimalQuantity_DualStorageBCD(BigDecimal input) {
        setToBigDecimal(input);
    }

    public DecimalQuantity_DualStorageBCD(DecimalQuantity_DualStorageBCD other) {
        copyFrom(other);
    }

    public DecimalQuantity_DualStorageBCD(Number number) {
        if (number instanceof Long) {
            setToLong(number.longValue());
            return;
        }
        if (number instanceof Integer) {
            setToInt(number.intValue());
            return;
        }
        if (number instanceof Float) {
            setToDouble(number.doubleValue());
            return;
        }
        if (number instanceof Double) {
            setToDouble(number.doubleValue());
            return;
        }
        if (number instanceof BigInteger) {
            setToBigInteger((BigInteger) number);
            return;
        }
        if (number instanceof BigDecimal) {
            setToBigDecimal((BigDecimal) number);
        } else {
            if (number instanceof android.icu.math.BigDecimal) {
                setToBigDecimal(((android.icu.math.BigDecimal) number).toBigDecimal());
                return;
            }
            throw new IllegalArgumentException("Number is of an unsupported type: " + number.getClass().getName());
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity
    public DecimalQuantity createCopy() {
        return new DecimalQuantity_DualStorageBCD(this);
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected byte getDigitPos(int position) {
        if (this.usingBytes) {
            if (position < 0 || position >= this.precision) {
                return (byte) 0;
            }
            return this.bcdBytes[position];
        }
        if (position < 0 || position >= 16) {
            return (byte) 0;
        }
        return (byte) ((this.bcdLong >>> (position * 4)) & 15);
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected void setDigitPos(int position, byte value) {
        if (this.usingBytes) {
            ensureCapacity(position + 1);
            this.bcdBytes[position] = value;
        } else if (position >= 16) {
            switchStorage();
            ensureCapacity(position + 1);
            this.bcdBytes[position] = value;
        } else {
            int shift = position * 4;
            this.bcdLong = (this.bcdLong & (~(15 << shift))) | (value << shift);
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected void shiftLeft(int numDigits) {
        if (!this.usingBytes && this.precision + numDigits > 16) {
            switchStorage();
        }
        if (this.usingBytes) {
            ensureCapacity(this.precision + numDigits);
            int i = (this.precision + numDigits) - 1;
            while (i >= numDigits) {
                byte[] bArr = this.bcdBytes;
                bArr[i] = bArr[i - numDigits];
                i--;
            }
            while (i >= 0) {
                this.bcdBytes[i] = 0;
                i--;
            }
        } else {
            this.bcdLong <<= numDigits * 4;
        }
        this.scale -= numDigits;
        this.precision += numDigits;
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected void shiftRight(int numDigits) {
        if (this.usingBytes) {
            int i = 0;
            while (i < this.precision - numDigits) {
                byte[] bArr = this.bcdBytes;
                bArr[i] = bArr[i + numDigits];
                i++;
            }
            while (i < this.precision) {
                this.bcdBytes[i] = 0;
                i++;
            }
        } else {
            this.bcdLong >>>= numDigits * 4;
        }
        this.scale += numDigits;
        this.precision -= numDigits;
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected void popFromLeft(int numDigits) {
        if (this.usingBytes) {
            int i = this.precision;
            while (true) {
                i--;
                if (i < this.precision - numDigits) {
                    break;
                } else {
                    this.bcdBytes[i] = 0;
                }
            }
        } else {
            this.bcdLong &= (1 << ((this.precision - numDigits) * 4)) - 1;
        }
        this.precision -= numDigits;
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected void setBcdToZero() {
        if (this.usingBytes) {
            this.bcdBytes = null;
            this.usingBytes = false;
        }
        this.bcdLong = 0L;
        this.scale = 0;
        this.precision = 0;
        this.isApproximate = false;
        this.origDouble = XPath.MATCH_SCORE_QNAME;
        this.origDelta = 0;
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected void readIntToBcd(int n) {
        long result = 0;
        int i = 16;
        while (n != 0) {
            result = (result >>> 4) + ((n % 10) << 60);
            n /= 10;
            i--;
        }
        this.bcdLong = result >>> (i * 4);
        this.scale = 0;
        this.precision = 16 - i;
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected void readLongToBcd(long n) {
        if (n >= 10000000000000000L) {
            ensureCapacity();
            int i = 0;
            while (n != 0) {
                this.bcdBytes[i] = (byte) (n % 10);
                n /= 10;
                i++;
            }
            this.scale = 0;
            this.precision = i;
            return;
        }
        long result = 0;
        int i2 = 16;
        while (n != 0) {
            result = (result >>> 4) + ((n % 10) << 60);
            n /= 10;
            i2--;
        }
        this.bcdLong = result >>> (i2 * 4);
        this.scale = 0;
        this.precision = 16 - i2;
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected void readBigIntegerToBcd(BigInteger n) {
        ensureCapacity();
        int i = 0;
        while (n.signum() != 0) {
            BigInteger[] temp = n.divideAndRemainder(BigInteger.TEN);
            ensureCapacity(i + 1);
            this.bcdBytes[i] = temp[1].byteValue();
            n = temp[0];
            i++;
        }
        this.scale = 0;
        this.precision = i;
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected BigDecimal bcdToBigDecimal() {
        BigDecimal result;
        if (this.usingBytes) {
            BigDecimal result2 = new BigDecimal(toNumberString());
            if (isNegative()) {
                return result2.negate();
            }
            return result2;
        }
        long tempLong = 0;
        for (int shift = this.precision - 1; shift >= 0; shift--) {
            tempLong = (10 * tempLong) + getDigitPos(shift);
        }
        BigDecimal result3 = BigDecimal.valueOf(tempLong);
        long newScale = result3.scale() + this.scale;
        if (newScale <= -2147483648L) {
            result = BigDecimal.ZERO;
        } else {
            result = result3.scaleByPowerOfTen(this.scale);
        }
        if (isNegative()) {
            return result.negate();
        }
        return result;
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected void compact() {
        if (this.usingBytes) {
            int delta = 0;
            while (delta < this.precision && this.bcdBytes[delta] == 0) {
                delta++;
            }
            if (delta == this.precision) {
                setBcdToZero();
                return;
            }
            shiftRight(delta);
            int leading = this.precision - 1;
            while (leading >= 0 && this.bcdBytes[leading] == 0) {
                leading--;
            }
            this.precision = leading + 1;
            if (this.precision <= 16) {
                switchStorage();
                return;
            }
            return;
        }
        long j = this.bcdLong;
        if (j == 0) {
            setBcdToZero();
            return;
        }
        int delta2 = Long.numberOfTrailingZeros(j) / 4;
        this.bcdLong >>>= delta2 * 4;
        this.scale += delta2;
        this.precision = 16 - (Long.numberOfLeadingZeros(this.bcdLong) / 4);
    }

    private void ensureCapacity() {
        ensureCapacity(40);
    }

    private void ensureCapacity(int capacity) {
        if (capacity == 0) {
            return;
        }
        int oldCapacity = this.usingBytes ? this.bcdBytes.length : 0;
        if (!this.usingBytes) {
            this.bcdBytes = new byte[capacity];
        } else if (oldCapacity < capacity) {
            byte[] bcd1 = new byte[capacity * 2];
            System.arraycopy((Object) this.bcdBytes, 0, (Object) bcd1, 0, oldCapacity);
            this.bcdBytes = bcd1;
        }
        this.usingBytes = true;
    }

    private void switchStorage() {
        if (this.usingBytes) {
            this.bcdLong = 0L;
            for (int i = this.precision - 1; i >= 0; i--) {
                long j = this.bcdLong << 4;
                this.bcdLong = j;
                this.bcdLong = j | this.bcdBytes[i];
            }
            this.bcdBytes = null;
            this.usingBytes = false;
            return;
        }
        ensureCapacity();
        for (int i2 = 0; i2 < this.precision; i2++) {
            byte[] bArr = this.bcdBytes;
            long j2 = this.bcdLong;
            bArr[i2] = (byte) (15 & j2);
            this.bcdLong = j2 >>> 4;
        }
    }

    @Override // android.icu.impl.number.DecimalQuantity_AbstractBCD
    protected void copyBcdFrom(DecimalQuantity _other) {
        DecimalQuantity_DualStorageBCD other = (DecimalQuantity_DualStorageBCD) _other;
        setBcdToZero();
        if (other.usingBytes) {
            ensureCapacity(other.precision);
            System.arraycopy((Object) other.bcdBytes, 0, (Object) this.bcdBytes, 0, other.precision);
        } else {
            this.bcdLong = other.bcdLong;
        }
    }

    @Deprecated
    public String checkHealth() {
        if (this.usingBytes) {
            if (this.bcdLong != 0) {
                return "Value in bcdLong but we are in byte mode";
            }
            if (this.precision == 0) {
                return "Zero precision but we are in byte mode";
            }
            if (this.precision > this.bcdBytes.length) {
                return "Precision exceeds length of byte array";
            }
            if (getDigitPos(this.precision - 1) == 0) {
                return "Most significant digit is zero in byte mode";
            }
            if (getDigitPos(0) == 0) {
                return "Least significant digit is zero in long mode";
            }
            for (int i = 0; i < this.precision; i++) {
                if (getDigitPos(i) >= 10) {
                    return "Digit exceeding 10 in byte array";
                }
                if (getDigitPos(i) < 0) {
                    return "Digit below 0 in byte array";
                }
            }
            for (int i2 = this.precision; i2 < this.bcdBytes.length; i2++) {
                if (getDigitPos(i2) != 0) {
                    return "Nonzero digits outside of range in byte array";
                }
            }
            return null;
        }
        if (this.bcdBytes != null) {
            int i3 = 0;
            while (true) {
                byte[] bArr = this.bcdBytes;
                if (i3 >= bArr.length) {
                    break;
                }
                if (bArr[i3] == 0) {
                    i3++;
                } else {
                    return "Nonzero digits in byte array but we are in long mode";
                }
            }
        }
        int i4 = this.precision;
        if (i4 == 0 && this.bcdLong != 0) {
            return "Value in bcdLong even though precision is zero";
        }
        if (this.precision > 16) {
            return "Precision exceeds length of long";
        }
        if (this.precision != 0 && getDigitPos(this.precision - 1) == 0) {
            return "Most significant digit is zero in long mode";
        }
        if (this.precision != 0 && getDigitPos(0) == 0) {
            return "Least significant digit is zero in long mode";
        }
        for (int i5 = 0; i5 < this.precision; i5++) {
            if (getDigitPos(i5) >= 10) {
                return "Digit exceeding 10 in long";
            }
            if (getDigitPos(i5) < 0) {
                return "Digit below 0 in long (?!)";
            }
        }
        for (int i6 = this.precision; i6 < 16; i6++) {
            if (getDigitPos(i6) != 0) {
                return "Nonzero digits outside of range in long";
            }
        }
        return null;
    }

    @Deprecated
    public boolean isUsingBytes() {
        return this.usingBytes;
    }

    public String toString() {
        Object[] objArr = new Object[5];
        objArr[0] = Integer.valueOf(this.lReqPos);
        objArr[1] = Integer.valueOf(this.rReqPos);
        objArr[2] = this.usingBytes ? "bytes" : "long";
        objArr[3] = isNegative() ? "-" : "";
        objArr[4] = toNumberString();
        return String.format("<DecimalQuantity %d:%d %s %s%s>", objArr);
    }

    private String toNumberString() {
        StringBuilder sb = new StringBuilder();
        if (this.usingBytes) {
            if (this.precision == 0) {
                sb.append('0');
            }
            for (int i = this.precision - 1; i >= 0; i--) {
                sb.append((int) this.bcdBytes[i]);
            }
        } else {
            sb.append(Long.toHexString(this.bcdLong));
        }
        sb.append("E");
        sb.append(this.scale);
        return sb.toString();
    }
}
