package aegon.chrome.base.supplier;

import aegon.chrome.base.UnownedUserData;
import aegon.chrome.base.UnownedUserDataHost;
import aegon.chrome.base.UnownedUserDataKey;
import aegon.chrome.base.lifetime.DestroyChecker;
import aegon.chrome.base.lifetime.Destroyable;
import aegon.chrome.base.supplier.Supplier;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public abstract class UnownedUserDataSupplier<E> extends ObservableSupplierImpl<E> implements UnownedUserData, Destroyable {
    private final DestroyChecker mDestroyChecker = new DestroyChecker();
    private final UnownedUserDataKey<UnownedUserDataSupplier<E>> mUudKey;

    @Override // aegon.chrome.base.supplier.ObservableSupplierImpl, aegon.chrome.base.supplier.Supplier
    public /* synthetic */ boolean hasValue() {
        return Supplier.CC.$default$hasValue(this);
    }

    @Override // aegon.chrome.base.UnownedUserData
    public /* synthetic */ boolean informOnDetachmentFromHost() {
        return UnownedUserData.CC.$default$informOnDetachmentFromHost(this);
    }

    @Override // aegon.chrome.base.UnownedUserData
    public /* synthetic */ void onDetachedFromHost(UnownedUserDataHost unownedUserDataHost) {
        UnownedUserData.CC.$default$onDetachedFromHost(this, unownedUserDataHost);
    }

    /* JADX WARN: Multi-variable type inference failed */
    protected UnownedUserDataSupplier(UnownedUserDataKey<? extends UnownedUserDataSupplier<E>> unownedUserDataKey) {
        this.mUudKey = unownedUserDataKey;
    }

    public void attach(UnownedUserDataHost unownedUserDataHost) {
        this.mDestroyChecker.checkNotDestroyed();
        this.mUudKey.attachToHost(unownedUserDataHost, this);
    }

    @Override // aegon.chrome.base.lifetime.Destroyable
    public void destroy() {
        this.mDestroyChecker.destroy();
        this.mUudKey.detachFromAllHosts(this);
    }
}
