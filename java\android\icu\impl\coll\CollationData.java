package android.icu.impl.coll;

import android.icu.impl.Normalizer2Impl;
import android.icu.impl.Trie2_32;
import android.icu.text.UnicodeSet;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationData {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final int[] EMPTY_INT_ARRAY = new int[0];
    static final int JAMO_CE32S_LENGTH = 67;
    static final int MAX_NUM_SPECIAL_REORDER_CODES = 8;
    static final int REORDER_RESERVED_AFTER_LATIN = 4111;
    static final int REORDER_RESERVED_BEFORE_LATIN = 4110;
    public CollationData base;
    int[] ce32s;
    long[] ces;
    public boolean[] compressibleBytes;
    String contexts;
    public char[] fastLatinTable;
    char[] fastLatinTableHeader;
    public Normalizer2Impl nfcImpl;
    int numScripts;
    public long[] rootElements;
    char[] scriptStarts;
    char[] scriptsIndex;
    Trie2_32 trie;
    UnicodeSet unsafeBackwardSet;
    int[] jamoCE32s = new int[67];
    long numericPrimary = 301989888;

    CollationData(Normalizer2Impl nfc) {
        this.nfcImpl = nfc;
    }

    public int getCE32(int c2) {
        return this.trie.get(c2);
    }

    int getCE32FromSupplementary(int c2) {
        return this.trie.get(c2);
    }

    boolean isDigit(int c2) {
        if (c2 < 1632) {
            return c2 <= 57 && 48 <= c2;
        }
        return Collation.hasCE32Tag(getCE32(c2), 10);
    }

    public boolean isUnsafeBackward(int c2, boolean numeric) {
        return this.unsafeBackwardSet.contains(c2) || (numeric && isDigit(c2));
    }

    public boolean isCompressibleLeadByte(int b2) {
        return this.compressibleBytes[b2];
    }

    public boolean isCompressiblePrimary(long p) {
        return isCompressibleLeadByte(((int) p) >>> 24);
    }

    int getCE32FromContexts(int index) {
        return (this.contexts.charAt(index) << 16) | this.contexts.charAt(index + 1);
    }

    int getIndirectCE32(int ce32) {
        int tag = Collation.tagFromCE32(ce32);
        if (tag == 10) {
            return this.ce32s[Collation.indexFromCE32(ce32)];
        }
        if (tag == 13) {
            return -1;
        }
        if (tag == 11) {
            return this.ce32s[0];
        }
        return ce32;
    }

    int getFinalCE32(int ce32) {
        if (Collation.isSpecialCE32(ce32)) {
            return getIndirectCE32(ce32);
        }
        return ce32;
    }

    long getCEFromOffsetCE32(int c2, int ce32) {
        long dataCE = this.ces[Collation.indexFromCE32(ce32)];
        return Collation.makeCE(Collation.getThreeBytePrimaryForOffsetData(c2, dataCE));
    }

    long getSingleCE(int c2) {
        CollationData d2;
        int ce32 = getCE32(c2);
        if (ce32 == 192) {
            d2 = this.base;
            ce32 = this.base.getCE32(c2);
        } else {
            d2 = this;
        }
        while (Collation.isSpecialCE32(ce32)) {
            switch (Collation.tagFromCE32(ce32)) {
                case 0:
                case 3:
                    throw new AssertionError((Object) String.format("unexpected CE32 tag for U+%04X (CE32 0x%08x)", Integer.valueOf(c2), Integer.valueOf(ce32)));
                case 1:
                    return Collation.ceFromLongPrimaryCE32(ce32);
                case 2:
                    return Collation.ceFromLongSecondaryCE32(ce32);
                case 4:
                case 7:
                case 8:
                case 9:
                case 12:
                case 13:
                    throw new UnsupportedOperationException(String.format("there is not exactly one collation element for U+%04X (CE32 0x%08x)", Integer.valueOf(c2), Integer.valueOf(ce32)));
                case 5:
                    if (Collation.lengthFromCE32(ce32) == 1) {
                        ce32 = d2.ce32s[Collation.indexFromCE32(ce32)];
                        break;
                    } else {
                        throw new UnsupportedOperationException(String.format("there is not exactly one collation element for U+%04X (CE32 0x%08x)", Integer.valueOf(c2), Integer.valueOf(ce32)));
                    }
                case 6:
                    if (Collation.lengthFromCE32(ce32) == 1) {
                        return d2.ces[Collation.indexFromCE32(ce32)];
                    }
                    throw new UnsupportedOperationException(String.format("there is not exactly one collation element for U+%04X (CE32 0x%08x)", Integer.valueOf(c2), Integer.valueOf(ce32)));
                case 10:
                    ce32 = d2.ce32s[Collation.indexFromCE32(ce32)];
                    break;
                case 11:
                    ce32 = d2.ce32s[0];
                    break;
                case 14:
                    return d2.getCEFromOffsetCE32(c2, ce32);
                case 15:
                    return Collation.unassignedCEFromCodePoint(c2);
            }
        }
        return Collation.ceFromSimpleCE32(ce32);
    }

    int getFCD16(int c2) {
        return this.nfcImpl.getFCD16(c2);
    }

    long getFirstPrimaryForGroup(int script) {
        int index = getScriptIndex(script);
        if (index == 0) {
            return 0L;
        }
        return this.scriptStarts[index] << 16;
    }

    public long getLastPrimaryForGroup(int script) {
        int index = getScriptIndex(script);
        if (index == 0) {
            return 0L;
        }
        long limit = this.scriptStarts[index + 1];
        return (limit << 16) - 1;
    }

    public int getGroupForPrimary(long p) {
        long p2 = p >> 16;
        char[] cArr = this.scriptStarts;
        if (p2 < cArr[1] || cArr[cArr.length - 1] <= p2) {
            return -1;
        }
        int index = 1;
        while (p2 >= this.scriptStarts[index + 1]) {
            index++;
        }
        for (int i = 0; i < this.numScripts; i++) {
            if (this.scriptsIndex[i] == index) {
                return i;
            }
        }
        for (int i2 = 0; i2 < 8; i2++) {
            if (this.scriptsIndex[this.numScripts + i2] == index) {
                return i2 + 4096;
            }
        }
        return -1;
    }

    private int getScriptIndex(int script) {
        int script2;
        if (script < 0) {
            return 0;
        }
        int i = this.numScripts;
        if (script < i) {
            return this.scriptsIndex[script];
        }
        if (script < 4096 || script - 4096 >= 8) {
            return 0;
        }
        return this.scriptsIndex[i + script2];
    }

    public int[] getEquivalentScripts(int script) {
        int index = getScriptIndex(script);
        if (index == 0) {
            return EMPTY_INT_ARRAY;
        }
        if (script >= 4096) {
            return new int[]{script};
        }
        int length = 0;
        for (int i = 0; i < this.numScripts; i++) {
            if (this.scriptsIndex[i] == index) {
                length++;
            }
        }
        int[] dest = new int[length];
        if (length == 1) {
            dest[0] = script;
            return dest;
        }
        int length2 = 0;
        for (int i2 = 0; i2 < this.numScripts; i2++) {
            if (this.scriptsIndex[i2] == index) {
                dest[length2] = i2;
                length2++;
            }
        }
        return dest;
    }

    void makeReorderRanges(int[] reorder, UVector32 ranges) {
        makeReorderRanges(reorder, false, ranges);
    }

    /* JADX WARN: Code restructure failed: missing block: B:100:0x0177, code lost:
    
        r6 = r6 + 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:102:0x017f, code lost:
    
        if (r3 != 0) goto L105;
     */
    /* JADX WARN: Code restructure failed: missing block: B:104:0x0186, code lost:
    
        if (r6 >= (r18.scriptStarts.length - 1)) goto L106;
     */
    /* JADX WARN: Code restructure failed: missing block: B:105:0x0188, code lost:
    
        r21.addElement((r18.scriptStarts[r6] << 16) | (65535 & r3));
     */
    /* JADX WARN: Code restructure failed: missing block: B:107:0x019b, code lost:
    
        if (r6 != (r18.scriptStarts.length - 1)) goto L109;
     */
    /* JADX WARN: Code restructure failed: missing block: B:108:0x019d, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:109:0x019e, code lost:
    
        r3 = r9;
        r6 = r6 + 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x0127, code lost:
    
        r3 = 1;
        r10 = r10;
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x0128, code lost:
    
        r6 = r18.scriptStarts;
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x012d, code lost:
    
        if (r3 >= (r6.length - 1)) goto L137;
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x0131, code lost:
    
        if (r7[r3] == 0) goto L77;
     */
    /* JADX WARN: Code restructure failed: missing block: B:77:0x0134, code lost:
    
        r6 = r6[r3];
        r10 = r10;
        r10 = r10;
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x0136, code lost:
    
        if (r5 != false) goto L81;
     */
    /* JADX WARN: Code restructure failed: missing block: B:79:0x0138, code lost:
    
        if (r6 <= r10) goto L81;
     */
    /* JADX WARN: Code restructure failed: missing block: B:80:0x013a, code lost:
    
        r10 = r6;
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x013b, code lost:
    
        r10 = addLowScriptRange(r7, r3, r10);
     */
    /* JADX WARN: Code restructure failed: missing block: B:82:0x013f, code lost:
    
        r3 = r3 + 1;
        r10 = r10;
     */
    /* JADX WARN: Code restructure failed: missing block: B:83:0x0142, code lost:
    
        if (r10 <= r8) goto L90;
     */
    /* JADX WARN: Code restructure failed: missing block: B:85:0x014a, code lost:
    
        if ((r10 - (65280 & r12)) > r8) goto L88;
     */
    /* JADX WARN: Code restructure failed: missing block: B:86:0x014c, code lost:
    
        makeReorderRanges(r19, true, r21);
     */
    /* JADX WARN: Code restructure failed: missing block: B:87:0x0150, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:89:0x0158, code lost:
    
        throw new android.icu.util.ICUException("setReorderCodes(): reordering too many partial-primary-lead-byte scripts");
     */
    /* JADX WARN: Code restructure failed: missing block: B:90:0x0159, code lost:
    
        r3 = 0;
        r6 = 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:91:0x015b, code lost:
    
        r9 = r3;
     */
    /* JADX WARN: Code restructure failed: missing block: B:92:0x015c, code lost:
    
        r13 = r18.scriptStarts;
     */
    /* JADX WARN: Code restructure failed: missing block: B:93:0x0161, code lost:
    
        if (r6 >= (r13.length - 1)) goto L142;
     */
    /* JADX WARN: Code restructure failed: missing block: B:94:0x0163, code lost:
    
        r14 = r7[r6];
     */
    /* JADX WARN: Code restructure failed: missing block: B:95:0x0167, code lost:
    
        if (r14 != 255) goto L97;
     */
    /* JADX WARN: Code restructure failed: missing block: B:97:0x016c, code lost:
    
        r9 = r14 - (r13[r6] >> '\b');
     */
    /* JADX WARN: Code restructure failed: missing block: B:98:0x0174, code lost:
    
        if (r9 == r3) goto L144;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private void makeReorderRanges(int[] r19, boolean r20, android.icu.impl.coll.UVector32 r21) {
        /*
            Method dump skipped, instructions count: 419
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationData.makeReorderRanges(int[], boolean, android.icu.impl.coll.UVector32):void");
    }

    private int addLowScriptRange(short[] table, int index, int lowStart) {
        char c2 = this.scriptStarts[index];
        if ((c2 & 255) < (lowStart & 255)) {
            lowStart += 256;
        }
        table[index] = (short) (lowStart >> 8);
        char c3 = this.scriptStarts[index + 1];
        return ((lowStart & 65280) + ((c3 & 65280) - (65280 & c2))) | (c3 & 255);
    }

    private int addHighScriptRange(short[] table, int index, int highLimit) {
        char c2 = this.scriptStarts[index + 1];
        if ((c2 & 255) > (highLimit & 255)) {
            highLimit -= 256;
        }
        char c3 = this.scriptStarts[index];
        int highLimit2 = ((highLimit & 65280) - ((c2 & 65280) - (65280 & c3))) | (c3 & 255);
        table[index] = (short) (highLimit2 >> 8);
        return highLimit2;
    }

    private static String scriptCodeString(int script) {
        if (script < 4096) {
            return Integer.toString(script);
        }
        return "0x" + Integer.toHexString(script);
    }
}
