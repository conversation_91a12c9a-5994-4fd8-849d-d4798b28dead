package aegon.chrome.net.test;

import aegon.chrome.net.UrlResponseInfo;
import com.getui.gtc.extension.distribution.gbd.p156g.p157a.C1965e;
import java.p654io.UnsupportedEncodingException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class FakeUrlResponse {
    private final List<Map.Entry<String, String>> mAllHeadersList;
    private final int mHttpStatusCode;
    private final String mNegotiatedProtocol;
    private final String mProxyServer;
    private final byte[] mResponseBody;
    private final boolean mWasCached;

    private static <T> T getNullableOrDefault(T t, T t2) {
        return t != null ? t : t2;
    }

    private FakeUrlResponse(Builder builder) {
        this.mHttpStatusCode = builder.mHttpStatusCode;
        this.mAllHeadersList = Collections.unmodifiableList(new ArrayList(builder.mAllHeadersList));
        this.mWasCached = builder.mWasCached;
        this.mNegotiatedProtocol = builder.mNegotiatedProtocol;
        this.mProxyServer = builder.mProxyServer;
        this.mResponseBody = builder.mResponseBody;
    }

    public FakeUrlResponse(UrlResponseInfo urlResponseInfo) {
        this.mHttpStatusCode = urlResponseInfo.getHttpStatusCode();
        this.mAllHeadersList = Collections.unmodifiableList(new ArrayList(urlResponseInfo.getAllHeadersAsList()));
        this.mWasCached = urlResponseInfo.wasCached();
        this.mNegotiatedProtocol = (String) getNullableOrDefault(urlResponseInfo.getNegotiatedProtocol(), "");
        this.mProxyServer = (String) getNullableOrDefault(urlResponseInfo.getProxyServer(), "");
        this.mResponseBody = Builder.DEFAULT_RESPONSE_BODY;
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    public static class Builder {
        private static final int DEFAULT_HTTP_STATUS_CODE = 200;
        private static final String DEFAULT_NEGOTIATED_PROTOCOL = "";
        private static final String DEFAULT_PROXY_SERVER = "";
        private static final boolean DEFAULT_WAS_CACHED = false;
        private List<Map.Entry<String, String>> mAllHeadersList;
        private int mHttpStatusCode;
        private String mNegotiatedProtocol;
        private String mProxyServer;
        private byte[] mResponseBody;
        private boolean mWasCached;
        private static final List<Map.Entry<String, String>> INTERNAL_INITIAL_HEADERS_LIST = new ArrayList();
        private static final byte[] DEFAULT_RESPONSE_BODY = new byte[0];

        public Builder() {
            this.mHttpStatusCode = 200;
            this.mAllHeadersList = new ArrayList(INTERNAL_INITIAL_HEADERS_LIST);
            this.mWasCached = false;
            this.mNegotiatedProtocol = "";
            this.mProxyServer = "";
            this.mResponseBody = DEFAULT_RESPONSE_BODY;
        }

        private Builder(FakeUrlResponse fakeUrlResponse) {
            this.mHttpStatusCode = 200;
            this.mAllHeadersList = new ArrayList(INTERNAL_INITIAL_HEADERS_LIST);
            this.mWasCached = false;
            this.mNegotiatedProtocol = "";
            this.mProxyServer = "";
            this.mResponseBody = DEFAULT_RESPONSE_BODY;
            this.mHttpStatusCode = fakeUrlResponse.getHttpStatusCode();
            this.mAllHeadersList = new ArrayList(fakeUrlResponse.getAllHeadersList());
            this.mWasCached = fakeUrlResponse.getWasCached();
            this.mNegotiatedProtocol = fakeUrlResponse.getNegotiatedProtocol();
            this.mProxyServer = fakeUrlResponse.getProxyServer();
            this.mResponseBody = fakeUrlResponse.getResponseBody();
        }

        public Builder setHttpStatusCode(int i) {
            this.mHttpStatusCode = i;
            return this;
        }

        public Builder addHeader(String str, String str2) {
            this.mAllHeadersList.add(new AbstractMap.SimpleEntry(str, str2));
            return this;
        }

        public Builder setWasCached(boolean z) {
            this.mWasCached = z;
            return this;
        }

        public Builder setNegotiatedProtocol(String str) {
            this.mNegotiatedProtocol = str;
            return this;
        }

        public Builder setProxyServer(String str) {
            this.mProxyServer = str;
            return this;
        }

        public Builder setResponseBody(byte[] bArr) {
            this.mResponseBody = bArr;
            return this;
        }

        public FakeUrlResponse build() {
            return new FakeUrlResponse(this);
        }
    }

    int getHttpStatusCode() {
        return this.mHttpStatusCode;
    }

    List<Map.Entry<String, String>> getAllHeadersList() {
        return this.mAllHeadersList;
    }

    boolean getWasCached() {
        return this.mWasCached;
    }

    String getNegotiatedProtocol() {
        return this.mNegotiatedProtocol;
    }

    String getProxyServer() {
        return this.mProxyServer;
    }

    byte[] getResponseBody() {
        return this.mResponseBody;
    }

    public Builder toBuilder() {
        return new Builder();
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof FakeUrlResponse)) {
            return false;
        }
        FakeUrlResponse fakeUrlResponse = (FakeUrlResponse) obj;
        return this.mHttpStatusCode == fakeUrlResponse.mHttpStatusCode && this.mAllHeadersList.equals(fakeUrlResponse.mAllHeadersList) && this.mWasCached == fakeUrlResponse.mWasCached && this.mNegotiatedProtocol.equals(fakeUrlResponse.mNegotiatedProtocol) && this.mProxyServer.equals(fakeUrlResponse.mProxyServer) && Arrays.equals(this.mResponseBody, fakeUrlResponse.mResponseBody);
    }

    public int hashCode() {
        return Objects.hash(Integer.valueOf(this.mHttpStatusCode), this.mAllHeadersList, Boolean.valueOf(this.mWasCached), this.mNegotiatedProtocol, this.mProxyServer, Integer.valueOf(Arrays.hashCode(this.mResponseBody)));
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("HTTP Status Code: " + this.mHttpStatusCode);
        sb.append(" Headers: " + this.mAllHeadersList.toString());
        sb.append(" Was Cached: " + this.mWasCached);
        sb.append(" Negotiated Protocol: " + this.mNegotiatedProtocol);
        sb.append(" Proxy Server: " + this.mProxyServer);
        sb.append(" Response Body ");
        try {
            sb.append("(UTF-8): " + new String(this.mResponseBody, C1965e.f6503z));
        } catch (UnsupportedEncodingException unused) {
            sb.append("(hexadecimal): " + getHexStringFromBytes(this.mResponseBody));
        }
        return sb.toString();
    }

    private String getHexStringFromBytes(byte[] bArr) {
        StringBuilder sb = new StringBuilder();
        for (byte b2 : this.mResponseBody) {
            sb.append(String.format("%02x", Byte.valueOf(b2)));
        }
        return sb.toString();
    }
}
