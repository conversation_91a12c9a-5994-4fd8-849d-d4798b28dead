package aegon.chrome.base;

import aegon.chrome.base.annotations.MainDex;
import android.text.TextUtils;
import java.p654io.File;
import java.p654io.FileReader;
import java.p654io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public abstract class CommandLine {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final String SWITCH_PREFIX = "--";
    private static final String SWITCH_TERMINATOR = "--";
    private static final String SWITCH_VALUE_SEPARATOR = "=";
    private static final String TAG = "CommandLine";
    private static final AtomicReference<CommandLine> sCommandLine = new AtomicReference<>();

    interface Natives {
        void appendSwitch(String str);

        void appendSwitchWithValue(String str, String str2);

        void appendSwitchesAndArguments(String[] strArr);

        String getSwitchValue(String str);

        String[] getSwitchesFlattened();

        boolean hasSwitch(String str);

        void init(String[] strArr);

        void removeSwitch(String str);
    }

    public abstract void appendSwitch(String str);

    public abstract void appendSwitchWithValue(String str, String str2);

    public abstract void appendSwitchesAndArguments(String[] strArr);

    protected void destroy() {
    }

    protected abstract String[] getCommandLineArguments();

    public abstract String getSwitchValue(String str);

    public abstract Map getSwitches();

    public abstract boolean hasSwitch(String str);

    public boolean isNativeImplementation() {
        return false;
    }

    public abstract void removeSwitch(String str);

    public String getSwitchValue(String str, String str2) {
        String switchValue = getSwitchValue(str);
        return TextUtils.isEmpty(switchValue) ? str2 : switchValue;
    }

    public static boolean isInitialized() {
        return sCommandLine.get() != null;
    }

    public static CommandLine getInstance() {
        return sCommandLine.get();
    }

    public static void init(String[] strArr) {
        setInstance(new JavaCommandLine(strArr));
    }

    public static void initFromFile(String str) {
        char[] fileAsUtf8 = readFileAsUtf8(str);
        init(fileAsUtf8 == null ? null : tokenizeQuotedArguments(fileAsUtf8));
    }

    public static void reset() {
        setInstance(null);
    }

    static String[] tokenizeQuotedArguments(char[] cArr) {
        if (cArr.length > 65536) {
            throw new RuntimeException("Flags file too big: " + cArr.length);
        }
        ArrayList arrayList = new ArrayList();
        StringBuilder sb = null;
        char c2 = 0;
        for (char c3 : cArr) {
            if ((c2 == 0 && (c3 == '\'' || c3 == '\"')) || c3 == c2) {
                if (sb == null || sb.length() <= 0 || sb.charAt(sb.length() - 1) != '\\') {
                    if (c2 != 0) {
                        c3 = 0;
                    }
                    c2 = c3;
                } else {
                    sb.setCharAt(sb.length() - 1, c3);
                }
            } else if (c2 != 0 || !Character.isWhitespace(c3)) {
                if (sb == null) {
                    sb = new StringBuilder();
                }
                sb.append(c3);
            } else if (sb != null) {
                arrayList.add(sb.toString());
                sb = null;
            }
        }
        if (sb != null) {
            if (c2 != 0) {
                android.util.Log.w(TAG, "Unterminated quoted string: " + ((Object) sb));
            }
            arrayList.add(sb.toString());
        }
        return (String[]) arrayList.toArray(new String[arrayList.size()]);
    }

    public static void enableNativeProxy() {
        sCommandLine.set(new NativeCommandLine(getJavaSwitchesOrNull()));
    }

    public static String[] getJavaSwitchesOrNull() {
        CommandLine commandLine = sCommandLine.get();
        if (commandLine != null) {
            return commandLine.getCommandLineArguments();
        }
        return null;
    }

    private static void setInstance(CommandLine commandLine) {
        CommandLine andSet = sCommandLine.getAndSet(commandLine);
        if (andSet != null) {
            andSet.destroy();
        }
    }

    public static void setInstanceForTesting(CommandLine commandLine) {
        setInstance(commandLine);
    }

    private static char[] readFileAsUtf8(String str) {
        File file = new File(str);
        try {
            FileReader fileReader = new FileReader(file);
            try {
                char[] cArr = new char[(int) file.length()];
                char[] cArrCopyOfRange = Arrays.copyOfRange(cArr, 0, fileReader.read(cArr));
                fileReader.lambda$new$0();
                return cArrCopyOfRange;
            } catch (Throwable th) {
                try {
                    fileReader.lambda$new$0();
                } catch (Throwable unused) {
                }
                throw th;
            }
        } catch (IOException unused2) {
            return null;
        }
    }

    private CommandLine() {
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static class JavaCommandLine extends CommandLine {
        static final /* synthetic */ boolean $assertionsDisabled = false;
        private ArrayList<String> mArgs;
        private int mArgsBegin;
        private HashMap<String, String> mSwitches;

        JavaCommandLine(String[] strArr) {
            super();
            this.mSwitches = new HashMap<>();
            this.mArgs = new ArrayList<>();
            this.mArgsBegin = 1;
            if (strArr == null || strArr.length == 0 || strArr[0] == null) {
                this.mArgs.add("");
            } else {
                this.mArgs.add(strArr[0]);
                appendSwitchesInternal(strArr, 1);
            }
        }

        @Override // aegon.chrome.base.CommandLine
        protected String[] getCommandLineArguments() {
            ArrayList<String> arrayList = this.mArgs;
            return (String[]) arrayList.toArray(new String[arrayList.size()]);
        }

        @Override // aegon.chrome.base.CommandLine
        public boolean hasSwitch(String str) {
            return this.mSwitches.containsKey(str);
        }

        @Override // aegon.chrome.base.CommandLine
        public String getSwitchValue(String str) {
            String str2 = this.mSwitches.get(str);
            if (str2 == null || str2.isEmpty()) {
                return null;
            }
            return str2;
        }

        @Override // aegon.chrome.base.CommandLine
        public Map<String, String> getSwitches() {
            return new HashMap(this.mSwitches);
        }

        @Override // aegon.chrome.base.CommandLine
        public void appendSwitch(String str) {
            appendSwitchWithValue(str, null);
        }

        @Override // aegon.chrome.base.CommandLine
        public void appendSwitchWithValue(String str, String str2) {
            this.mSwitches.put(str, str2 == null ? "" : str2);
            String str3 = "--" + str;
            if (str2 != null && !str2.isEmpty()) {
                str3 = str3 + CommandLine.SWITCH_VALUE_SEPARATOR + str2;
            }
            ArrayList<String> arrayList = this.mArgs;
            int i = this.mArgsBegin;
            this.mArgsBegin = i + 1;
            arrayList.add(i, str3);
        }

        @Override // aegon.chrome.base.CommandLine
        public void appendSwitchesAndArguments(String[] strArr) {
            appendSwitchesInternal(strArr, 0);
        }

        private void appendSwitchesInternal(String[] strArr, int i) {
            int i2 = i;
            boolean z = true;
            for (String str : strArr) {
                if (i2 > 0) {
                    i2--;
                } else {
                    if (str.equals("--")) {
                        z = false;
                    }
                    if (z && str.startsWith("--")) {
                        String[] strArrSplit = str.split(CommandLine.SWITCH_VALUE_SEPARATOR, 2);
                        appendSwitchWithValue(strArrSplit[0].substring(2), strArrSplit.length > 1 ? strArrSplit[1] : null);
                    } else {
                        this.mArgs.add(str);
                    }
                }
            }
        }

        /* JADX WARN: Removed duplicated region for block: B:8:0x0046  */
        @Override // aegon.chrome.base.CommandLine
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public void removeSwitch(java.lang.String r5) {
            /*
                r4 = this;
                java.util.HashMap<java.lang.String, java.lang.String> r0 = r4.mSwitches
                r0.remove(r5)
                java.lang.StringBuilder r0 = new java.lang.StringBuilder
                java.lang.String r1 = "--"
                r0.<init>(r1)
                r0.append(r5)
                java.lang.String r5 = r0.toString()
                int r0 = r4.mArgsBegin
                int r0 = r0 + (-1)
            L17:
                if (r0 <= 0) goto L54
                java.util.ArrayList<java.lang.String> r1 = r4.mArgs
                java.lang.Object r1 = r1.get(r0)
                java.lang.String r1 = (java.lang.String) r1
                boolean r1 = r1.equals(r5)
                if (r1 != 0) goto L46
                java.util.ArrayList<java.lang.String> r1 = r4.mArgs
                java.lang.Object r1 = r1.get(r0)
                java.lang.String r1 = (java.lang.String) r1
                java.lang.StringBuilder r2 = new java.lang.StringBuilder
                r2.<init>()
                r2.append(r5)
                java.lang.String r3 = "="
                r2.append(r3)
                java.lang.String r2 = r2.toString()
                boolean r1 = r1.startsWith(r2)
                if (r1 == 0) goto L51
            L46:
                int r1 = r4.mArgsBegin
                int r1 = r1 + (-1)
                r4.mArgsBegin = r1
                java.util.ArrayList<java.lang.String> r1 = r4.mArgs
                r1.remove(r0)
            L51:
                int r0 = r0 + (-1)
                goto L17
            L54:
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: aegon.chrome.base.CommandLine.JavaCommandLine.removeSwitch(java.lang.String):void");
        }
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    static class NativeCommandLine extends CommandLine {
        static final /* synthetic */ boolean $assertionsDisabled = false;

        @Override // aegon.chrome.base.CommandLine
        protected String[] getCommandLineArguments() {
            return null;
        }

        @Override // aegon.chrome.base.CommandLine
        public boolean isNativeImplementation() {
            return true;
        }

        public NativeCommandLine(String[] strArr) {
            super();
            CommandLineJni.get().init(strArr);
        }

        @Override // aegon.chrome.base.CommandLine
        public boolean hasSwitch(String str) {
            return CommandLineJni.get().hasSwitch(str);
        }

        @Override // aegon.chrome.base.CommandLine
        public String getSwitchValue(String str) {
            return CommandLineJni.get().getSwitchValue(str);
        }

        @Override // aegon.chrome.base.CommandLine
        public Map<String, String> getSwitches() {
            HashMap map = new HashMap();
            String[] switchesFlattened = CommandLineJni.get().getSwitchesFlattened();
            for (int i = 0; i < switchesFlattened.length; i += 2) {
                map.put(switchesFlattened[i], switchesFlattened[i + 1]);
            }
            return map;
        }

        @Override // aegon.chrome.base.CommandLine
        public void appendSwitch(String str) {
            CommandLineJni.get().appendSwitch(str);
        }

        @Override // aegon.chrome.base.CommandLine
        public void appendSwitchWithValue(String str, String str2) {
            Natives natives = CommandLineJni.get();
            if (str2 == null) {
                str2 = "";
            }
            natives.appendSwitchWithValue(str, str2);
        }

        @Override // aegon.chrome.base.CommandLine
        public void appendSwitchesAndArguments(String[] strArr) {
            CommandLineJni.get().appendSwitchesAndArguments(strArr);
        }

        @Override // aegon.chrome.base.CommandLine
        public void removeSwitch(String str) {
            CommandLineJni.get().removeSwitch(str);
        }

        @Override // aegon.chrome.base.CommandLine
        protected void destroy() {
            throw new IllegalStateException("Can't destroy native command line after startup");
        }
    }
}
