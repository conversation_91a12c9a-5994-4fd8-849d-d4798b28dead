package aegon.chrome.net.test;

import aegon.chrome.net.CronetException;
import aegon.chrome.net.InlineExecutionProhibitedException;
import aegon.chrome.net.UploadDataProvider;
import aegon.chrome.net.UrlRequest;
import aegon.chrome.net.UrlResponseInfo;
import aegon.chrome.net.impl.CallbackExceptionImpl;
import aegon.chrome.net.impl.CronetExceptionImpl;
import aegon.chrome.net.impl.JavaUploadDataSinkBase;
import aegon.chrome.net.impl.JavaUrlRequestUtils;
import aegon.chrome.net.impl.Preconditions;
import aegon.chrome.net.impl.UrlRequestBase;
import aegon.chrome.net.impl.UrlResponseInfoImpl;
import android.util.Log;
import com.android.volley.toolbox.HttpClientStack;
import com.kuaishou.socket.nano.SocketMessages;
import com.kwai.middleware.azeroth.network.HttpMethod;
import com.xiaomi.stat.MiStat;
import java.net.HttpURLConnection;
import java.net.URI;
import java.nio.ByteBuffer;
import java.p654io.ByteArrayOutputStream;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionException;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
final class FakeUrlRequest extends UrlRequestBase {
    private static final int DEFAULT_UPLOAD_BUFFER_SIZE = 8192;
    private static final Map<Integer, String> HTTP_STATUS_CODE_TO_TEXT;
    private static final String TAG = FakeUrlRequest.class.getSimpleName();
    private final boolean mAllowDirectExecutor;
    private final UrlRequest.Callback mCallback;
    private FakeUrlResponse mCurrentFakeResponse;
    private String mCurrentUrl;
    private final Executor mExecutor;
    private final FakeCronetController mFakeCronetController;
    private final FakeCronetEngine mFakeCronetEngine;
    FakeDataSink mFakeDataSink;
    private String mHttpMethod;
    private byte[] mRequestBody;
    private ByteBuffer mResponse;
    private UploadDataProvider mUploadDataProvider;
    private Executor mUploadExecutor;
    private boolean mUploadProviderClosed;
    private UrlResponseInfo mUrlResponseInfo;
    private final Executor mUserExecutor;
    private final Object mLock = new Object();
    private final List<String> mUrlChain = new ArrayList();
    private final ArrayList<Map.Entry<String, String>> mAllHeadersList = new ArrayList<>();
    private int mState = 0;
    private volatile int mAdditionalStatusDetails = -1;

    static {
        HashMap map = new HashMap();
        map.put(100, "Continue");
        map.put(101, "Switching Protocols");
        map.put(102, "Processing");
        map.put(103, "Early Hints");
        map.put(200, "OK");
        map.put(201, "Created");
        map.put(202, "Accepted");
        map.put(203, "Non-Authoritative Information");
        map.put(204, "No Content");
        map.put(205, "Reset Content");
        map.put(206, "Partial Content");
        map.put(207, "Multi-Status");
        map.put(208, "Already Reported");
        map.put(226, "IM Used");
        map.put(300, "Multiple Choices");
        map.put(301, "Moved Permanently");
        map.put(302, "Found");
        map.put(303, "See Other");
        map.put(304, "Not Modified");
        map.put(305, "Use Proxy");
        map.put(306, "Unused");
        map.put(307, "Temporary Redirect");
        map.put(308, "Permanent Redirect");
        map.put(400, "Bad Request");
        map.put(401, "Unauthorized");
        map.put(402, "Payment Required");
        map.put(403, "Forbidden");
        map.put(Integer.valueOf(HttpURLConnection.HTTP_NOT_FOUND), "Not Found");
        map.put(Integer.valueOf(HttpURLConnection.HTTP_BAD_METHOD), "Method Not Allowed");
        map.put(Integer.valueOf(HttpURLConnection.HTTP_NOT_ACCEPTABLE), "Not Acceptable");
        map.put(Integer.valueOf(HttpURLConnection.HTTP_PROXY_AUTH), "Proxy Authentication Required");
        map.put(Integer.valueOf(HttpURLConnection.HTTP_CLIENT_TIMEOUT), "Request Timeout");
        map.put(Integer.valueOf(HttpURLConnection.HTTP_CONFLICT), "Conflict");
        map.put(410, "Gone");
        map.put(411, "Length Required");
        map.put(412, "Precondition Failed");
        map.put(Integer.valueOf(HttpURLConnection.HTTP_ENTITY_TOO_LARGE), "Payload Too Large");
        map.put(414, "URI Too Long");
        map.put(415, "Unsupported Media Type");
        map.put(Integer.valueOf(SocketMessages.PayloadType.SC_FORBID_COMMENT_RECOVER), "Range Not Satisfiable");
        map.put(Integer.valueOf(SocketMessages.PayloadType.SC_FANS_TOP_OPENED), "Expectation Failed");
        map.put(Integer.valueOf(SocketMessages.PayloadType.SC_RED_PACK_RAIN_TOKEN_READY), "Misdirected Request");
        map.put(Integer.valueOf(SocketMessages.PayloadType.SC_RED_PACK_RAIN_GROUP_CLOSED), "Unprocessable Entity");
        map.put(Integer.valueOf(SocketMessages.PayloadType.SC_RED_PACK_RAIN_HIDDEN), "Locked");
        map.put(Integer.valueOf(SocketMessages.PayloadType.SC_RED_PACK_RAIN_SHOW), "Failed Dependency");
        map.put(Integer.valueOf(SocketMessages.PayloadType.SC_RED_PACK_RAIN_WIDGET_READY), "Too Early");
        map.put(Integer.valueOf(SocketMessages.PayloadType.SC_RED_PACK_RAIN_WIDGET_CLOSE), "Upgrade Required");
        map.put(428, "Precondition Required");
        map.put(429, "Too Many Requests");
        map.put(Integer.valueOf(SocketMessages.PayloadType.SC_VOICE_PARTY_CLOSED), "Request Header Fields Too Large");
        map.put(451, "Unavailable For Legal Reasons");
        map.put(500, "Internal Server Error");
        map.put(501, "Not Implemented");
        map.put(502, "Bad Gateway");
        map.put(503, "Service Unavailable");
        map.put(Integer.valueOf(HttpURLConnection.HTTP_GATEWAY_TIMEOUT), "Gateway Timeout");
        map.put(Integer.valueOf(HttpURLConnection.HTTP_VERSION), "HTTP Version Not Supported");
        map.put(506, "Variant Also Negotiates");
        map.put(507, "Insufficient Storage");
        map.put(508, "Loop Denied");
        map.put(Integer.valueOf(SocketMessages.PayloadType.SC_ACTION_SIGNAL), "Not Extended");
        map.put(511, "Network Authentication Required");
        HTTP_STATUS_CODE_TO_TEXT = Collections.unmodifiableMap(map);
    }

    FakeUrlRequest(UrlRequest.Callback callback, Executor executor, Executor executor2, String str, boolean z, boolean z2, int i, boolean z3, int i2, FakeCronetController fakeCronetController, FakeCronetEngine fakeCronetEngine) {
        if (str == null) {
            throw new NullPointerException("URL is required");
        }
        if (callback == null) {
            throw new NullPointerException("Listener is required");
        }
        if (executor2 == null) {
            throw new NullPointerException("Executor is required");
        }
        this.mCallback = callback;
        this.mUserExecutor = z ? executor : new JavaUrlRequestUtils.DirectPreventingExecutor(executor);
        this.mExecutor = executor2;
        this.mCurrentUrl = str;
        this.mFakeCronetController = fakeCronetController;
        this.mFakeCronetEngine = fakeCronetEngine;
        this.mAllowDirectExecutor = z;
    }

    @Override // aegon.chrome.net.impl.UrlRequestBase
    public final void setUploadDataProvider(UploadDataProvider uploadDataProvider, Executor executor) {
        if (uploadDataProvider == null) {
            throw new NullPointerException("Invalid UploadDataProvider.");
        }
        synchronized (this.mLock) {
            if (!checkHasContentTypeHeader()) {
                throw new IllegalArgumentException("Requests with upload data must have a Content-Type.");
            }
            checkNotStarted();
            if (this.mHttpMethod == null) {
                this.mHttpMethod = HttpMethod.POST;
            }
            if (!this.mAllowDirectExecutor) {
                executor = new JavaUrlRequestUtils.DirectPreventingExecutor(executor);
            }
            this.mUploadExecutor = executor;
            this.mUploadDataProvider = uploadDataProvider;
        }
    }

    @Override // aegon.chrome.net.impl.UrlRequestBase
    public final void setHttpMethod(String str) {
        synchronized (this.mLock) {
            checkNotStarted();
            if (str == null) {
                throw new NullPointerException("Method is required.");
            }
            if (!"OPTIONS".equalsIgnoreCase(str) && !HttpMethod.GET.equalsIgnoreCase(str) && !"HEAD".equalsIgnoreCase(str) && !HttpMethod.POST.equalsIgnoreCase(str) && !"PUT".equalsIgnoreCase(str) && !"DELETE".equalsIgnoreCase(str) && !"TRACE".equalsIgnoreCase(str) && !HttpClientStack.HttpPatch.METHOD_NAME.equalsIgnoreCase(str)) {
                throw new IllegalArgumentException("Invalid http method: " + str);
            }
            this.mHttpMethod = str;
        }
    }

    @Override // aegon.chrome.net.impl.UrlRequestBase
    public final void addHeader(String str, String str2) {
        synchronized (this.mLock) {
            checkNotStarted();
            this.mAllHeadersList.add(new AbstractMap.SimpleEntry(str, str2));
        }
    }

    private void checkNotStarted() {
        if (this.mState == 0) {
            return;
        }
        throw new IllegalStateException("Request is already started. State is: " + this.mState);
    }

    @Override // aegon.chrome.net.UrlRequest
    public final void start() {
        synchronized (this.mLock) {
            if (this.mFakeCronetEngine.startRequest()) {
                try {
                    transitionStates(0, 1);
                    this.mAdditionalStatusDetails = 10;
                    this.mUrlChain.add(this.mCurrentUrl);
                    if (this.mUploadDataProvider != null) {
                        this.mFakeDataSink = new FakeDataSink(this.mUploadExecutor, this.mExecutor, this.mUploadDataProvider);
                        this.mFakeDataSink.start(true);
                    } else {
                        fakeConnect();
                    }
                } catch (Throwable th) {
                    cleanup();
                    throw th;
                }
            } else {
                throw new IllegalStateException("This request's CronetEngine is already shutdown.");
            }
        }
    }

    private void fakeConnect() {
        this.mAdditionalStatusDetails = 13;
        this.mCurrentFakeResponse = this.mFakeCronetController.getResponse(this.mCurrentUrl, this.mHttpMethod, this.mAllHeadersList, this.mRequestBody);
        int httpStatusCode = this.mCurrentFakeResponse.getHttpStatusCode();
        this.mUrlResponseInfo = new UrlResponseInfoImpl(Collections.unmodifiableList(new ArrayList(this.mUrlChain)), httpStatusCode, getDescriptionByCode(Integer.valueOf(httpStatusCode)), this.mCurrentFakeResponse.getAllHeadersList(), this.mCurrentFakeResponse.getWasCached(), this.mCurrentFakeResponse.getNegotiatedProtocol(), this.mCurrentFakeResponse.getProxyServer(), this.mCurrentFakeResponse.getResponseBody().length);
        this.mResponse = ByteBuffer.wrap(this.mCurrentFakeResponse.getResponseBody());
        if (httpStatusCode >= 300 && httpStatusCode < 400) {
            processRedirectResponse();
            return;
        }
        closeUploadDataProvider();
        final UrlResponseInfo urlResponseInfo = this.mUrlResponseInfo;
        transitionStates(1, 4);
        executeCheckedRunnable(new JavaUrlRequestUtils.CheckedRunnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.1
            @Override // aegon.chrome.net.impl.JavaUrlRequestUtils.CheckedRunnable
            public void run() {
                FakeUrlRequest.this.mCallback.onResponseStarted(FakeUrlRequest.this, urlResponseInfo);
            }
        });
    }

    private void processRedirectResponse() {
        transitionStates(1, 2);
        if (this.mUrlResponseInfo.getAllHeaders().get(MiStat.Param.LOCATION) == null) {
            final String str = this.mCurrentUrl;
            this.mUserExecutor.execute(new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.2
                @Override // java.lang.Runnable
                public void run() {
                    FakeUrlRequest.this.tryToFailWithException(new CronetExceptionImpl("Request failed due to bad redirect HTTP headers", new IllegalStateException("Response recieved from URL: " + str + " was a redirect, but lacked a location header.")));
                }
            });
            return;
        }
        final String string = URI.create(this.mCurrentUrl).resolve(this.mUrlResponseInfo.getAllHeaders().get(MiStat.Param.LOCATION).get(0)).toString();
        this.mCurrentUrl = string;
        this.mUrlChain.add(this.mCurrentUrl);
        transitionStates(2, 3);
        final UrlResponseInfo urlResponseInfo = this.mUrlResponseInfo;
        this.mExecutor.execute(new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.3
            @Override // java.lang.Runnable
            public void run() {
                FakeUrlRequest.this.executeCheckedRunnable(new JavaUrlRequestUtils.CheckedRunnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.3.1
                    @Override // aegon.chrome.net.impl.JavaUrlRequestUtils.CheckedRunnable
                    public void run() {
                        FakeUrlRequest.this.mCallback.onRedirectReceived(FakeUrlRequest.this, urlResponseInfo, string);
                    }
                });
            }
        });
    }

    @Override // aegon.chrome.net.UrlRequest
    public final void read(final ByteBuffer byteBuffer) {
        Preconditions.checkHasRemaining(byteBuffer);
        Preconditions.checkDirect(byteBuffer);
        synchronized (this.mLock) {
            transitionStates(4, 5);
            final UrlResponseInfo urlResponseInfo = this.mUrlResponseInfo;
            if (this.mResponse.hasRemaining()) {
                transitionStates(5, 4);
                fillBufferWithResponse(byteBuffer);
                this.mExecutor.execute(new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.4
                    @Override // java.lang.Runnable
                    public void run() {
                        FakeUrlRequest.this.executeCheckedRunnable(new JavaUrlRequestUtils.CheckedRunnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.4.1
                            @Override // aegon.chrome.net.impl.JavaUrlRequestUtils.CheckedRunnable
                            public void run() {
                                FakeUrlRequest.this.mCallback.onReadCompleted(FakeUrlRequest.this, urlResponseInfo, byteBuffer);
                            }
                        });
                    }
                });
            } else if (setTerminalState(7)) {
                this.mUserExecutor.execute(new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.5
                    @Override // java.lang.Runnable
                    public void run() {
                        FakeUrlRequest.this.mCallback.onSucceeded(FakeUrlRequest.this, urlResponseInfo);
                    }
                });
            }
        }
    }

    private void fillBufferWithResponse(ByteBuffer byteBuffer) {
        int iMin = Math.min(byteBuffer.remaining(), this.mResponse.remaining());
        ByteBuffer byteBufferDuplicate = this.mResponse.duplicate();
        byteBufferDuplicate.limit(byteBufferDuplicate.position() + iMin);
        byteBuffer.put(byteBufferDuplicate);
        ByteBuffer byteBuffer2 = this.mResponse;
        byteBuffer2.position(byteBuffer2.position() + iMin);
    }

    @Override // aegon.chrome.net.UrlRequest
    public final void followRedirect() {
        synchronized (this.mLock) {
            transitionStates(3, 1);
            if (this.mFakeDataSink != null) {
                this.mFakeDataSink = new FakeDataSink(this.mUploadExecutor, this.mExecutor, this.mUploadDataProvider);
                this.mFakeDataSink.start(false);
            } else {
                fakeConnect();
            }
        }
    }

    @Override // aegon.chrome.net.UrlRequest
    public final void cancel() {
        synchronized (this.mLock) {
            final UrlResponseInfo urlResponseInfo = this.mUrlResponseInfo;
            if (setTerminalState(8)) {
                this.mUserExecutor.execute(new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.6
                    @Override // java.lang.Runnable
                    public void run() {
                        FakeUrlRequest.this.mCallback.onCanceled(FakeUrlRequest.this, urlResponseInfo);
                    }
                });
            }
        }
    }

    @Override // aegon.chrome.net.UrlRequest
    public final void getStatus(final UrlRequest.StatusListener statusListener) {
        synchronized (this.mLock) {
            final int i = this.mAdditionalStatusDetails;
            switch (this.mState) {
                case 0:
                case 6:
                case 7:
                case 8:
                    i = -1;
                    break;
                case 1:
                    break;
                case 2:
                case 3:
                case 4:
                    i = 0;
                    break;
                case 5:
                    i = 14;
                    break;
                default:
                    throw new IllegalStateException("Switch is exhaustive: " + this.mState);
            }
            this.mUserExecutor.execute(new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.7
                @Override // java.lang.Runnable
                public void run() {
                    statusListener.onStatus(i);
                }
            });
        }
    }

    @Override // aegon.chrome.net.UrlRequest
    public final boolean isDone() {
        boolean z;
        synchronized (this.mLock) {
            z = this.mState == 7 || this.mState == 6 || this.mState == 8;
        }
        return z;
    }

    private void transitionStates(int i, int i2) {
        int i3 = this.mState;
        if (i3 == i) {
            this.mState = i2;
            return;
        }
        if (i3 == 8 || i3 == 6) {
            return;
        }
        throw new IllegalStateException("Invalid state transition - expected " + i + " but was " + this.mState);
    }

    private void tryToFailWithException(CronetException cronetException) {
        synchronized (this.mLock) {
            if (setTerminalState(6)) {
                this.mCallback.onFailed(this, this.mUrlResponseInfo, cronetException);
            }
        }
    }

    private void executeCheckedRunnable(final JavaUrlRequestUtils.CheckedRunnable checkedRunnable) {
        try {
            this.mUserExecutor.execute(new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.8
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        checkedRunnable.run();
                    } catch (Exception e) {
                        FakeUrlRequest.this.tryToFailWithException(new CallbackExceptionImpl("Exception received from UrlRequest.Callback", e));
                    }
                }
            });
        } catch (InlineExecutionProhibitedException e) {
            tryToFailWithException(new CronetExceptionImpl("Exception posting task to executor", e));
        }
    }

    private boolean setTerminalState(int i) {
        int i2 = this.mState;
        if (i2 == 0) {
            throw new IllegalStateException("Can't enter terminal state before start");
        }
        if (i2 == 6 || i2 == 7 || i2 == 8) {
            return false;
        }
        this.mState = i;
        cleanup();
        return true;
    }

    private void cleanup() {
        closeUploadDataProvider();
        this.mFakeCronetEngine.onRequestDestroyed();
    }

    private void closeUploadDataProvider() {
        if (this.mUploadDataProvider == null || this.mUploadProviderClosed) {
            return;
        }
        try {
            this.mUploadExecutor.execute(uploadErrorSetting(new JavaUrlRequestUtils.CheckedRunnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.9
                @Override // aegon.chrome.net.impl.JavaUrlRequestUtils.CheckedRunnable
                public void run() {
                    synchronized (FakeUrlRequest.this.mLock) {
                        FakeUrlRequest.this.mUploadDataProvider.lambda$new$0();
                        FakeUrlRequest.this.mUploadProviderClosed = true;
                    }
                }
            }));
        } catch (RejectedExecutionException e) {
            Log.e(TAG, "Exception when closing uploadDataProvider", e);
        }
    }

    private Runnable uploadErrorSetting(final JavaUrlRequestUtils.CheckedRunnable checkedRunnable) {
        return new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.10
            @Override // java.lang.Runnable
            public void run() {
                try {
                    checkedRunnable.run();
                } catch (Throwable th) {
                    FakeUrlRequest.this.enterUploadErrorState(th);
                }
            }
        };
    }

    private void enterUploadErrorState(final Throwable th) {
        synchronized (this.mLock) {
            this.mUserExecutor.execute(new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.11
                @Override // java.lang.Runnable
                public void run() {
                    FakeUrlRequest.this.tryToFailWithException(new CronetExceptionImpl("Exception received from UploadDataProvider", th));
                }
            });
        }
    }

    final class FakeDataSink extends JavaUploadDataSinkBase {
        private final ByteArrayOutputStream mTotalUploadStream;

        @Override // aegon.chrome.net.impl.JavaUploadDataSinkBase
        public final void initializeRead() {
        }

        @Override // aegon.chrome.net.impl.JavaUploadDataSinkBase
        public final void initializeStart(long j) {
        }

        FakeDataSink(Executor executor, Executor executor2, UploadDataProvider uploadDataProvider) {
            super(executor, executor2, uploadDataProvider);
            this.mTotalUploadStream = new ByteArrayOutputStream();
        }

        @Override // aegon.chrome.net.impl.JavaUploadDataSinkBase
        public final Runnable getErrorSettingRunnable(final JavaUrlRequestUtils.CheckedRunnable checkedRunnable) {
            return new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.FakeDataSink.1
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        checkedRunnable.run();
                    } catch (Throwable th) {
                        FakeUrlRequest.this.mUserExecutor.execute(new Runnable() { // from class: aegon.chrome.net.test.FakeUrlRequest.FakeDataSink.1.1
                            @Override // java.lang.Runnable
                            public void run() {
                                FakeUrlRequest.this.tryToFailWithException(new CronetExceptionImpl("System error", th));
                            }
                        });
                    }
                }
            };
        }

        @Override // aegon.chrome.net.impl.JavaUploadDataSinkBase
        public final Runnable getUploadErrorSettingRunnable(JavaUrlRequestUtils.CheckedRunnable checkedRunnable) {
            return FakeUrlRequest.this.uploadErrorSetting(checkedRunnable);
        }

        @Override // aegon.chrome.net.impl.JavaUploadDataSinkBase
        public final void processUploadError(Throwable th) {
            FakeUrlRequest.this.enterUploadErrorState(th);
        }

        @Override // aegon.chrome.net.impl.JavaUploadDataSinkBase
        public final int processSuccessfulRead(ByteBuffer byteBuffer) {
            this.mTotalUploadStream.write(byteBuffer.array(), byteBuffer.arrayOffset(), byteBuffer.remaining());
            return byteBuffer.remaining();
        }

        @Override // aegon.chrome.net.impl.JavaUploadDataSinkBase
        public final void finish() {
            synchronized (FakeUrlRequest.this.mLock) {
                FakeUrlRequest.this.mRequestBody = this.mTotalUploadStream.toByteArray();
                FakeUrlRequest.this.fakeConnect();
            }
        }
    }

    private boolean checkHasContentTypeHeader() {
        Iterator<Map.Entry<String, String>> itIterator2 = this.mAllHeadersList.iterator2();
        while (itIterator2.hasNext()) {
            if (itIterator2.mo35924next().getKey().equalsIgnoreCase("content-type")) {
                return true;
            }
        }
        return false;
    }

    private static String getDescriptionByCode(Integer num) {
        return HTTP_STATUS_CODE_TO_TEXT.containsKey(num) ? HTTP_STATUS_CODE_TO_TEXT.get(num) : "Unassigned";
    }
}
