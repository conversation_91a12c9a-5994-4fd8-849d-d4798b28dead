package android.icu.impl.data;

import java.util.ListResourceBundle;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class HolidayBundle_es extends ListResourceBundle {
    private static final Object[][] fContents = {new Object[]{"All Saints' Day", "Todos los Santos"}, new Object[]{"Armistice Day", "Día del Armisticio"}, new Object[]{"Ascension", "Ascensión"}, new Object[]{"Benito Juárez Day", "Día de la Benito Juárez"}, new Object[]{"Boxing Day", "Día en que se dan Aguinaldos Navideños"}, new Object[]{"Canada Day", "Día del Canadá"}, new Object[]{"Christmas Eve", "Víspera de Navidad"}, new Object[]{"Christmas", "Navidad"}, new Object[]{"Constitution Day", "Día de la Constitución"}, new Object[]{"Day of the Dead", "Día de los Muertos"}, new Object[]{"Easter Sunday", "Pascua"}, new Object[]{"Easter Monday", "Pascua Lunes"}, new Object[]{"Epiphany", "Epifanía"}, new Object[]{"Father's Day", "Día del Padre"}, new Object[]{"Flag Day", "Día de la Bandera"}, new Object[]{"Good Friday", "Viernes Santo"}, new Object[]{"Halloween", "víspera de Todos los Santos"}, new Object[]{"Independence Day", "Día de la Independencia"}, new Object[]{"Labor Day", "Día de Trabajadores"}, new Object[]{"Maundy Thursday", "Jueves Santo"}, new Object[]{"May Day", "Primero de Mayo"}, new Object[]{"Memorial Day", "Día de la Rememoración"}, new Object[]{"Mother's Day", "Día de la Madre"}, new Object[]{"New Year's Day", "Año Nuevo"}, new Object[]{"Palm Sunday", "Domingo de Ramos"}, new Object[]{"Pentecost", "Pentecostés"}, new Object[]{"Presidents' Day", "Día de Presidentes"}, new Object[]{"Revolution Day", "Día de la Revolución"}, new Object[]{"Shrove Tuesday", "Martes de Carnaval"}, new Object[]{"Thanksgiving", "Día de Acción de Gracias"}, new Object[]{"Veterans' Day", "Día de Veteranos"}, new Object[]{"Victoria Day", "Día de Victoria"}, new Object[]{"Whit Sunday", "Pentecostés"}};

    @Override // java.util.ListResourceBundle
    public synchronized Object[][] getContents() {
        return fContents;
    }
}
