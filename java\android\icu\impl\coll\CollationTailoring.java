package android.icu.impl.coll;

import android.icu.impl.Norm2AllModes;
import android.icu.impl.Normalizer2Impl;
import android.icu.impl.Trie2_32;
import android.icu.impl.coll.SharedObject;
import android.icu.text.UnicodeSet;
import android.icu.util.ULocale;
import android.icu.util.UResourceBundle;
import android.icu.util.VersionInfo;
import java.util.Map;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationTailoring {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    public CollationData data;
    public Map<Integer, Integer> maxExpansions;
    CollationData ownedData;
    private String rules;
    private UResourceBundle rulesResource;
    public SharedObject.Reference<CollationSettings> settings;
    Trie2_32 trie;
    UnicodeSet unsafeBackwardSet;
    public ULocale actualLocale = ULocale.ROOT;
    public int version = 0;

    CollationTailoring(SharedObject.Reference<CollationSettings> baseSettings) {
        if (baseSettings != null) {
            this.settings = baseSettings.m35918clone();
        } else {
            this.settings = new SharedObject.Reference<>(new CollationSettings());
        }
    }

    void ensureOwnedData() {
        if (this.ownedData == null) {
            Normalizer2Impl nfcImpl = Norm2AllModes.getNFCInstance().impl;
            this.ownedData = new CollationData(nfcImpl);
        }
        this.data = this.ownedData;
    }

    void setRules(String r) {
        this.rules = r;
    }

    void setRulesResource(UResourceBundle res) {
        this.rulesResource = res;
    }

    public String getRules() {
        String str = this.rules;
        if (str != null) {
            return str;
        }
        UResourceBundle uResourceBundle = this.rulesResource;
        if (uResourceBundle != null) {
            return uResourceBundle.getString();
        }
        return "";
    }

    static VersionInfo makeBaseVersion(VersionInfo ucaVersion) {
        return VersionInfo.getInstance(VersionInfo.UCOL_BUILDER_VERSION.getMajor(), (ucaVersion.getMajor() << 3) + ucaVersion.getMinor(), ucaVersion.getMilli() << 6, 0);
    }

    void setVersion(int baseVersion, int rulesVersion) {
        int r = (rulesVersion >> 16) & 65280;
        int s = (rulesVersion >> 16) & 255;
        int t = (rulesVersion >> 8) & 255;
        int q = rulesVersion & 255;
        this.version = (VersionInfo.UCOL_BUILDER_VERSION.getMajor() << 24) | (16760832 & baseVersion) | (((r >> 6) + r) & 16128) | (((s << 3) + (s >> 5) + t + (q << 4) + (q >> 4)) & 255);
    }

    int getUCAVersion() {
        int i = this.version;
        return ((i >> 14) & 3) | ((i >> 12) & 4080);
    }
}
