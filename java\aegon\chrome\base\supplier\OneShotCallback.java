package aegon.chrome.base.supplier;

import aegon.chrome.base.Callback;
import java.lang.ref.WeakReference;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class OneShotCallback<E> {
    private final Callback<E> mCallback;
    private final Callback<E> mCallbackWrapper = new CallbackWrapper();
    private final WeakReference<ObservableSupplier<E>> mWeakSupplier;

    public OneShotCallback(ObservableSupplier<E> observableSupplier, Callback<E> callback) {
        this.mWeakSupplier = new WeakReference<>(observableSupplier);
        this.mCallback = callback;
        observableSupplier.addObserver(this.mCallbackWrapper);
    }

    class CallbackWrapper implements Callback<E> {
        static final /* synthetic */ boolean $assertionsDisabled = false;

        @Override // aegon.chrome.base.Callback
        public /* synthetic */ Runnable bind(T t) {
            return Callback.CC.$default$bind(this, t);
        }

        private CallbackWrapper() {
        }

        @Override // aegon.chrome.base.Callback
        public void onResult(E e) {
            OneShotCallback.this.mCallback.onResult(e);
            ((ObservableSupplier) OneShotCallback.this.mWeakSupplier.get()).removeObserver(OneShotCallback.this.mCallbackWrapper);
        }
    }
}
