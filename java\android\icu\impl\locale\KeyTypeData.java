package android.icu.impl.locale;

import android.icu.impl.ICUData;
import android.icu.impl.ICUResourceBundle;
import android.icu.util.Output;
import android.icu.util.UResourceBundle;
import android.icu.util.UResourceBundleIterator;
import java.util.Collections;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.regex.Pattern;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class KeyTypeData {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static Map<String, Set<String>> BCP47_KEYS;
    static Set<String> DEPRECATED_KEYS = Collections.emptySet();
    static Map<String, ValueType> VALUE_TYPES = Collections.emptyMap();
    static Map<String, Set<String>> DEPRECATED_KEY_TYPES = Collections.emptyMap();
    private static final Object[][] KEY_DATA = new Object[0][];
    private static final Map<String, KeyData> KEYMAP = new HashMap();

    private enum KeyInfoType {
        deprecated,
        valueType
    }

    private enum TypeInfoType {
        deprecated
    }

    public enum ValueType {
        single,
        multiple,
        incremental,
        any
    }

    static {
        initFromResourceBundle();
    }

    private static abstract class SpecialTypeHandler {
        abstract boolean isWellFormed(String str);

        private SpecialTypeHandler() {
        }

        /* synthetic */ SpecialTypeHandler(C02661 x0) {
            this();
        }

        String canonicalize(String value) {
            return AsciiUtil.toLowerString(value);
        }
    }

    private static class CodepointsTypeHandler extends SpecialTypeHandler {
        private static final Pattern pat = Pattern.compile("[0-9a-fA-F]{4,6}(-[0-9a-fA-F]{4,6})*");

        private CodepointsTypeHandler() {
            super(null);
        }

        /* synthetic */ CodepointsTypeHandler(C02661 x0) {
            this();
        }

        @Override // android.icu.impl.locale.KeyTypeData.SpecialTypeHandler
        boolean isWellFormed(String value) {
            return pat.matcher(value).matches();
        }
    }

    private static class ReorderCodeTypeHandler extends SpecialTypeHandler {
        private static final Pattern pat = Pattern.compile("[a-zA-Z]{3,8}(-[a-zA-Z]{3,8})*");

        private ReorderCodeTypeHandler() {
            super(null);
        }

        /* synthetic */ ReorderCodeTypeHandler(C02661 x0) {
            this();
        }

        @Override // android.icu.impl.locale.KeyTypeData.SpecialTypeHandler
        boolean isWellFormed(String value) {
            return pat.matcher(value).matches();
        }
    }

    private static class RgKeyValueTypeHandler extends SpecialTypeHandler {
        private static final Pattern pat = Pattern.compile("([a-zA-Z]{2}|[0-9]{3})[zZ]{4}");

        private RgKeyValueTypeHandler() {
            super(null);
        }

        /* synthetic */ RgKeyValueTypeHandler(C02661 x0) {
            this();
        }

        @Override // android.icu.impl.locale.KeyTypeData.SpecialTypeHandler
        boolean isWellFormed(String value) {
            return pat.matcher(value).matches();
        }
    }

    private static class SubdivisionKeyValueTypeHandler extends SpecialTypeHandler {
        private static final Pattern pat = Pattern.compile("([a-zA-Z]{2}|[0-9]{3})");

        private SubdivisionKeyValueTypeHandler() {
            super(null);
        }

        /* synthetic */ SubdivisionKeyValueTypeHandler(C02661 x0) {
            this();
        }

        @Override // android.icu.impl.locale.KeyTypeData.SpecialTypeHandler
        boolean isWellFormed(String value) {
            return pat.matcher(value).matches();
        }
    }

    private static class PrivateUseKeyValueTypeHandler extends SpecialTypeHandler {
        private static final Pattern pat = Pattern.compile("[a-zA-Z0-9]{3,8}(-[a-zA-Z0-9]{3,8})*");

        private PrivateUseKeyValueTypeHandler() {
            super(null);
        }

        /* synthetic */ PrivateUseKeyValueTypeHandler(C02661 x0) {
            this();
        }

        @Override // android.icu.impl.locale.KeyTypeData.SpecialTypeHandler
        boolean isWellFormed(String value) {
            return pat.matcher(value).matches();
        }
    }

    /* JADX WARN: Enum visitor error
    jadx.core.utils.exceptions.JadxRuntimeException: Init of enum field 'CODEPOINTS' uses external variables
    	at jadx.core.dex.visitors.EnumVisitor.createEnumFieldByConstructor(EnumVisitor.java:451)
    	at jadx.core.dex.visitors.EnumVisitor.processEnumFieldByField(EnumVisitor.java:372)
    	at jadx.core.dex.visitors.EnumVisitor.processEnumFieldByWrappedInsn(EnumVisitor.java:337)
    	at jadx.core.dex.visitors.EnumVisitor.extractEnumFieldsFromFilledArray(EnumVisitor.java:322)
    	at jadx.core.dex.visitors.EnumVisitor.extractEnumFieldsFromInsn(EnumVisitor.java:262)
    	at jadx.core.dex.visitors.EnumVisitor.convertToEnum(EnumVisitor.java:151)
    	at jadx.core.dex.visitors.EnumVisitor.visit(EnumVisitor.java:100)
     */
    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    private static final class SpecialType {
        private static final /* synthetic */ SpecialType[] $VALUES;
        public static final SpecialType CODEPOINTS;
        public static final SpecialType PRIVATE_USE;
        public static final SpecialType REORDER_CODE;
        public static final SpecialType RG_KEY_VALUE;
        public static final SpecialType SUBDIVISION_CODE;
        SpecialTypeHandler handler;

        public static SpecialType valueOf(String name) {
            return (SpecialType) Enum.valueOf(SpecialType.class, name);
        }

        public static SpecialType[] values() {
            return (SpecialType[]) $VALUES.clone();
        }

        static {
            C02661 c02661 = null;
            CODEPOINTS = new SpecialType("CODEPOINTS", 0, new CodepointsTypeHandler(c02661));
            REORDER_CODE = new SpecialType("REORDER_CODE", 1, new ReorderCodeTypeHandler(c02661));
            RG_KEY_VALUE = new SpecialType("RG_KEY_VALUE", 2, new RgKeyValueTypeHandler(c02661));
            SUBDIVISION_CODE = new SpecialType("SUBDIVISION_CODE", 3, new SubdivisionKeyValueTypeHandler(c02661));
            SpecialType specialType = new SpecialType("PRIVATE_USE", 4, new PrivateUseKeyValueTypeHandler(c02661));
            PRIVATE_USE = specialType;
            $VALUES = new SpecialType[]{CODEPOINTS, REORDER_CODE, RG_KEY_VALUE, SUBDIVISION_CODE, specialType};
        }

        private SpecialType(String str, int i, SpecialTypeHandler handler) {
            this.handler = handler;
        }
    }

    private static class KeyData {
        String bcpId;
        String legacyId;
        EnumSet<SpecialType> specialTypes;
        Map<String, Type> typeMap;

        KeyData(String legacyId, String bcpId, Map<String, Type> typeMap, EnumSet<SpecialType> specialTypes) {
            this.legacyId = legacyId;
            this.bcpId = bcpId;
            this.typeMap = typeMap;
            this.specialTypes = specialTypes;
        }
    }

    private static class Type {
        String bcpId;
        String legacyId;

        Type(String legacyId, String bcpId) {
            this.legacyId = legacyId;
            this.bcpId = bcpId;
        }
    }

    public static String toBcpKey(String key) {
        KeyData keyData = KEYMAP.get(AsciiUtil.toLowerString(key));
        if (keyData != null) {
            return keyData.bcpId;
        }
        return null;
    }

    public static String toLegacyKey(String key) {
        KeyData keyData = KEYMAP.get(AsciiUtil.toLowerString(key));
        if (keyData != null) {
            return keyData.legacyId;
        }
        return null;
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [T, java.lang.Boolean] */
    /* JADX WARN: Type inference failed for: r1v6, types: [T, java.lang.Boolean] */
    /* JADX WARN: Type inference failed for: r2v6, types: [T, java.lang.Boolean] */
    public static String toBcpType(String key, String type, Output<Boolean> isKnownKey, Output<Boolean> isSpecialType) {
        if (isKnownKey != null) {
            isKnownKey.value = false;
        }
        if (isSpecialType != null) {
            isSpecialType.value = false;
        }
        String key2 = AsciiUtil.toLowerString(key);
        String type2 = AsciiUtil.toLowerString(type);
        KeyData keyData = KEYMAP.get(key2);
        if (keyData != null) {
            if (isKnownKey != null) {
                isKnownKey.value = Boolean.TRUE;
            }
            Type t = keyData.typeMap.get(type2);
            if (t != null) {
                return t.bcpId;
            }
            if (keyData.specialTypes != null) {
                Iterator<SpecialType> it = keyData.specialTypes.iterator2();
                while (it.hasNext()) {
                    SpecialType st = it.mo35924next();
                    if (st.handler.isWellFormed(type2)) {
                        if (isSpecialType != null) {
                            isSpecialType.value = true;
                        }
                        return st.handler.canonicalize(type2);
                    }
                }
                return null;
            }
            return null;
        }
        return null;
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [T, java.lang.Boolean] */
    /* JADX WARN: Type inference failed for: r1v6, types: [T, java.lang.Boolean] */
    /* JADX WARN: Type inference failed for: r2v6, types: [T, java.lang.Boolean] */
    public static String toLegacyType(String key, String type, Output<Boolean> isKnownKey, Output<Boolean> isSpecialType) {
        if (isKnownKey != null) {
            isKnownKey.value = false;
        }
        if (isSpecialType != null) {
            isSpecialType.value = false;
        }
        String key2 = AsciiUtil.toLowerString(key);
        String type2 = AsciiUtil.toLowerString(type);
        KeyData keyData = KEYMAP.get(key2);
        if (keyData != null) {
            if (isKnownKey != null) {
                isKnownKey.value = Boolean.TRUE;
            }
            Type t = keyData.typeMap.get(type2);
            if (t != null) {
                return t.legacyId;
            }
            if (keyData.specialTypes != null) {
                Iterator<SpecialType> it = keyData.specialTypes.iterator2();
                while (it.hasNext()) {
                    SpecialType st = it.mo35924next();
                    if (st.handler.isWellFormed(type2)) {
                        if (isSpecialType != null) {
                            isSpecialType.value = true;
                        }
                        return st.handler.canonicalize(type2);
                    }
                }
                return null;
            }
            return null;
        }
        return null;
    }

    private static void initFromResourceBundle() throws NoSuchElementException {
        boolean hasSameKey;
        String bcpKeyId;
        UResourceBundle keyTypeDataRes;
        UResourceBundle keyMapRes;
        UResourceBundle typeAliasRes;
        UResourceBundle typeMapRes;
        UResourceBundle bcpTypeAliasRes;
        EnumSet<SpecialType> specialTypeSet;
        String bcpTypeId;
        Set<String> bcpTypeAliasSet;
        Set<String> typeAliasSet;
        UResourceBundle typeAliasRes2;
        Set<String> aliasSet;
        UResourceBundle keyTypeDataRes2;
        UResourceBundle keyMapRes2;
        UResourceBundle keyTypeDataRes3 = ICUResourceBundle.getBundleInstance(ICUData.ICU_BASE_NAME, "keyTypeData", ICUResourceBundle.ICU_DATA_CLASS_LOADER, ICUResourceBundle.OpenType.DIRECT);
        getKeyInfo(keyTypeDataRes3.get("keyInfo"));
        getTypeInfo(keyTypeDataRes3.get("typeInfo"));
        UResourceBundle keyMapRes3 = keyTypeDataRes3.get("keyMap");
        UResourceBundle typeMapRes2 = keyTypeDataRes3.get("typeMap");
        UResourceBundle typeAliasRes3 = null;
        UResourceBundle bcpTypeAliasRes2 = null;
        try {
            typeAliasRes3 = keyTypeDataRes3.get("typeAlias");
        } catch (MissingResourceException e) {
        }
        try {
            bcpTypeAliasRes2 = keyTypeDataRes3.get("bcpTypeAlias");
        } catch (MissingResourceException e2) {
        }
        UResourceBundleIterator keyMapItr = keyMapRes3.getIterator();
        Map<String, Set<String>> _Bcp47Keys = new LinkedHashMap<>();
        while (keyMapItr.hasNext()) {
            UResourceBundle keyMapEntry = keyMapItr.next();
            String legacyKeyId = keyMapEntry.getKey();
            String bcpKeyId2 = keyMapEntry.getString();
            if (bcpKeyId2.length() != 0) {
                hasSameKey = false;
                bcpKeyId = bcpKeyId2;
            } else {
                hasSameKey = true;
                bcpKeyId = legacyKeyId;
            }
            LinkedHashSet<String> _bcp47Types = new LinkedHashSet<>();
            _Bcp47Keys.put(bcpKeyId, Collections.unmodifiableSet(_bcp47Types));
            boolean isTZ = legacyKeyId.equals("timezone");
            Map<String, Set<String>> typeAliasMap = null;
            if (typeAliasRes3 == null) {
                keyTypeDataRes = keyTypeDataRes3;
                keyMapRes = keyMapRes3;
            } else {
                UResourceBundle typeAliasResByKey = null;
                try {
                    typeAliasResByKey = typeAliasRes3.get(legacyKeyId);
                } catch (MissingResourceException e3) {
                }
                if (typeAliasResByKey == null) {
                    keyTypeDataRes = keyTypeDataRes3;
                    keyMapRes = keyMapRes3;
                } else {
                    typeAliasMap = new HashMap<>();
                    UResourceBundleIterator typeAliasResItr = typeAliasResByKey.getIterator();
                    while (typeAliasResItr.hasNext()) {
                        UResourceBundle typeAliasDataEntry = typeAliasResItr.next();
                        String from = typeAliasDataEntry.getKey();
                        UResourceBundleIterator typeAliasResItr2 = typeAliasResItr;
                        String to = typeAliasDataEntry.getString();
                        if (!isTZ) {
                            keyTypeDataRes2 = keyTypeDataRes3;
                            keyMapRes2 = keyMapRes3;
                        } else {
                            keyTypeDataRes2 = keyTypeDataRes3;
                            keyMapRes2 = keyMapRes3;
                            from = from.replace(':', '/');
                        }
                        Set<String> aliasSet2 = typeAliasMap.get(to);
                        if (aliasSet2 == null) {
                            aliasSet2 = new HashSet<>();
                            typeAliasMap.put(to, aliasSet2);
                        }
                        aliasSet2.add(from);
                        typeAliasResItr = typeAliasResItr2;
                        keyTypeDataRes3 = keyTypeDataRes2;
                        keyMapRes3 = keyMapRes2;
                    }
                    keyTypeDataRes = keyTypeDataRes3;
                    keyMapRes = keyMapRes3;
                }
            }
            Map<String, Set<String>> bcpTypeAliasMap = null;
            if (bcpTypeAliasRes2 == null) {
                typeAliasRes = typeAliasRes3;
            } else {
                UResourceBundle bcpTypeAliasResByKey = null;
                try {
                    bcpTypeAliasResByKey = bcpTypeAliasRes2.get(bcpKeyId);
                } catch (MissingResourceException e4) {
                }
                if (bcpTypeAliasResByKey != null) {
                    bcpTypeAliasMap = new HashMap<>();
                    UResourceBundleIterator bcpTypeAliasResItr = bcpTypeAliasResByKey.getIterator();
                    while (bcpTypeAliasResItr.hasNext()) {
                        UResourceBundle bcpTypeAliasDataEntry = bcpTypeAliasResItr.next();
                        UResourceBundleIterator bcpTypeAliasResItr2 = bcpTypeAliasResItr;
                        String from2 = bcpTypeAliasDataEntry.getKey();
                        UResourceBundle bcpTypeAliasResByKey2 = bcpTypeAliasResByKey;
                        String to2 = bcpTypeAliasDataEntry.getString();
                        Set<String> aliasSet3 = bcpTypeAliasMap.get(to2);
                        if (aliasSet3 != null) {
                            typeAliasRes2 = typeAliasRes3;
                            aliasSet = aliasSet3;
                        } else {
                            typeAliasRes2 = typeAliasRes3;
                            aliasSet = new HashSet<>();
                            bcpTypeAliasMap.put(to2, aliasSet);
                        }
                        aliasSet.add(from2);
                        bcpTypeAliasResItr = bcpTypeAliasResItr2;
                        bcpTypeAliasResByKey = bcpTypeAliasResByKey2;
                        typeAliasRes3 = typeAliasRes2;
                    }
                    typeAliasRes = typeAliasRes3;
                } else {
                    typeAliasRes = typeAliasRes3;
                }
            }
            Map<String, Type> typeDataMap = new HashMap<>();
            EnumSet<SpecialType> specialTypeSet2 = null;
            UResourceBundle typeMapResByKey = null;
            try {
                typeMapResByKey = typeMapRes2.get(legacyKeyId);
            } catch (MissingResourceException e5) {
            }
            if (typeMapResByKey == null) {
                typeMapRes = typeMapRes2;
                bcpTypeAliasRes = bcpTypeAliasRes2;
            } else {
                UResourceBundleIterator typeMapResByKeyItr = typeMapResByKey.getIterator();
                while (typeMapResByKeyItr.hasNext()) {
                    UResourceBundle typeMapEntry = typeMapResByKeyItr.next();
                    UResourceBundleIterator typeMapResByKeyItr2 = typeMapResByKeyItr;
                    String legacyTypeId = typeMapEntry.getKey();
                    String bcpTypeId2 = typeMapEntry.getString();
                    UResourceBundle typeMapRes3 = typeMapRes2;
                    UResourceBundle bcpTypeAliasRes3 = bcpTypeAliasRes2;
                    char first = legacyTypeId.charAt(0);
                    boolean isSpecialType = '9' < first && first < 'a' && bcpTypeId2.length() == 0;
                    if (isSpecialType) {
                        if (specialTypeSet2 == null) {
                            specialTypeSet2 = EnumSet.noneOf(SpecialType.class);
                        }
                        specialTypeSet2.add(SpecialType.valueOf(legacyTypeId));
                        _bcp47Types.add(legacyTypeId);
                        typeMapResByKeyItr = typeMapResByKeyItr2;
                        typeMapRes2 = typeMapRes3;
                        bcpTypeAliasRes2 = bcpTypeAliasRes3;
                    } else {
                        if (!isTZ) {
                            specialTypeSet = specialTypeSet2;
                        } else {
                            specialTypeSet = specialTypeSet2;
                            legacyTypeId = legacyTypeId.replace(':', '/');
                        }
                        boolean hasSameType = false;
                        if (bcpTypeId2.length() != 0) {
                            bcpTypeId = bcpTypeId2;
                        } else {
                            hasSameType = true;
                            bcpTypeId = legacyTypeId;
                        }
                        _bcp47Types.add(bcpTypeId);
                        Type t = new Type(legacyTypeId, bcpTypeId);
                        typeDataMap.put(AsciiUtil.toLowerString(legacyTypeId), t);
                        if (!hasSameType) {
                            typeDataMap.put(AsciiUtil.toLowerString(bcpTypeId), t);
                        }
                        if (typeAliasMap != null && (typeAliasSet = typeAliasMap.get(legacyTypeId)) != null) {
                            for (String alias : typeAliasSet) {
                                typeDataMap.put(AsciiUtil.toLowerString(alias), t);
                                legacyTypeId = legacyTypeId;
                            }
                        }
                        if (bcpTypeAliasMap != null && (bcpTypeAliasSet = bcpTypeAliasMap.get(bcpTypeId)) != null) {
                            for (String alias2 : bcpTypeAliasSet) {
                                typeDataMap.put(AsciiUtil.toLowerString(alias2), t);
                                bcpTypeAliasSet = bcpTypeAliasSet;
                            }
                        }
                        specialTypeSet2 = specialTypeSet;
                        typeMapResByKeyItr = typeMapResByKeyItr2;
                        typeMapRes2 = typeMapRes3;
                        bcpTypeAliasRes2 = bcpTypeAliasRes3;
                    }
                }
                typeMapRes = typeMapRes2;
                bcpTypeAliasRes = bcpTypeAliasRes2;
            }
            KeyData keyData = new KeyData(legacyKeyId, bcpKeyId, typeDataMap, specialTypeSet2);
            KEYMAP.put(AsciiUtil.toLowerString(legacyKeyId), keyData);
            if (!hasSameKey) {
                KEYMAP.put(AsciiUtil.toLowerString(bcpKeyId), keyData);
            }
            keyTypeDataRes3 = keyTypeDataRes;
            keyMapRes3 = keyMapRes;
            typeAliasRes3 = typeAliasRes;
            typeMapRes2 = typeMapRes;
            bcpTypeAliasRes2 = bcpTypeAliasRes;
        }
        BCP47_KEYS = Collections.unmodifiableMap(_Bcp47Keys);
    }

    private static void getKeyInfo(UResourceBundle keyInfoRes) throws NoSuchElementException {
        Set<String> _deprecatedKeys = new LinkedHashSet<>();
        Map<String, ValueType> _valueTypes = new LinkedHashMap<>();
        UResourceBundleIterator keyInfoIt = keyInfoRes.getIterator();
        while (keyInfoIt.hasNext()) {
            UResourceBundle keyInfoEntry = keyInfoIt.next();
            String key = keyInfoEntry.getKey();
            KeyInfoType keyInfo = KeyInfoType.valueOf(key);
            UResourceBundleIterator keyInfoIt2 = keyInfoEntry.getIterator();
            while (keyInfoIt2.hasNext()) {
                UResourceBundle keyInfoEntry2 = keyInfoIt2.next();
                String key2 = keyInfoEntry2.getKey();
                String value2 = keyInfoEntry2.getString();
                int i = C02661.$SwitchMap$android$icu$impl$locale$KeyTypeData$KeyInfoType[keyInfo.ordinal()];
                if (i == 1) {
                    _deprecatedKeys.add(key2);
                } else if (i == 2) {
                    _valueTypes.put(key2, ValueType.valueOf(value2));
                }
            }
        }
        DEPRECATED_KEYS = Collections.unmodifiableSet(_deprecatedKeys);
        VALUE_TYPES = Collections.unmodifiableMap(_valueTypes);
    }

    private static void getTypeInfo(UResourceBundle typeInfoRes) throws NoSuchElementException {
        Map<String, Set<String>> _deprecatedKeyTypes = new LinkedHashMap<>();
        UResourceBundleIterator keyInfoIt = typeInfoRes.getIterator();
        while (keyInfoIt.hasNext()) {
            UResourceBundle keyInfoEntry = keyInfoIt.next();
            String key = keyInfoEntry.getKey();
            TypeInfoType typeInfo = TypeInfoType.valueOf(key);
            UResourceBundleIterator keyInfoIt2 = keyInfoEntry.getIterator();
            while (keyInfoIt2.hasNext()) {
                UResourceBundle keyInfoEntry2 = keyInfoIt2.next();
                String key2 = keyInfoEntry2.getKey();
                Set<String> _deprecatedTypes = new LinkedHashSet<>();
                UResourceBundleIterator keyInfoIt3 = keyInfoEntry2.getIterator();
                while (keyInfoIt3.hasNext()) {
                    UResourceBundle keyInfoEntry3 = keyInfoIt3.next();
                    String key3 = keyInfoEntry3.getKey();
                    if (C02661.$SwitchMap$android$icu$impl$locale$KeyTypeData$TypeInfoType[typeInfo.ordinal()] == 1) {
                        _deprecatedTypes.add(key3);
                    }
                }
                _deprecatedKeyTypes.put(key2, Collections.unmodifiableSet(_deprecatedTypes));
            }
        }
        DEPRECATED_KEY_TYPES = Collections.unmodifiableMap(_deprecatedKeyTypes);
    }

    /* renamed from: android.icu.impl.locale.KeyTypeData$1 */
    static /* synthetic */ class C02661 {
        static final /* synthetic */ int[] $SwitchMap$android$icu$impl$locale$KeyTypeData$KeyInfoType;
        static final /* synthetic */ int[] $SwitchMap$android$icu$impl$locale$KeyTypeData$TypeInfoType;

        static {
            int[] iArr = new int[TypeInfoType.values().length];
            $SwitchMap$android$icu$impl$locale$KeyTypeData$TypeInfoType = iArr;
            try {
                iArr[TypeInfoType.deprecated.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            int[] iArr2 = new int[KeyInfoType.values().length];
            $SwitchMap$android$icu$impl$locale$KeyTypeData$KeyInfoType = iArr2;
            try {
                iArr2[KeyInfoType.deprecated.ordinal()] = 1;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$android$icu$impl$locale$KeyTypeData$KeyInfoType[KeyInfoType.valueType.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    private static void initFromTables() {
        Object[][] objArr;
        int i;
        String[][] typeAliasData;
        String bcpTypeId;
        Set<SpecialType> specialTypeSet;
        Map<String, Set<String>> bcpTypeAliasMap;
        Set<String> aliasSet;
        Object[][] objArr2 = KEY_DATA;
        int length = objArr2.length;
        int i2 = 0;
        int i3 = 0;
        while (i3 < length) {
            Object[] keyDataEntry = objArr2[i3];
            String legacyKeyId = (String) keyDataEntry[i2];
            char c2 = 1;
            String bcpKeyId = (String) keyDataEntry[1];
            String[][] typeData = (String[][]) keyDataEntry[2];
            String[][] typeAliasData2 = (String[][]) keyDataEntry[3];
            String[][] bcpTypeAliasData = (String[][]) keyDataEntry[4];
            boolean hasSameKey = false;
            if (bcpKeyId == null) {
                bcpKeyId = legacyKeyId;
                hasSameKey = true;
            }
            Map<String, Set<String>> typeAliasMap = null;
            if (typeAliasData2 == null) {
                objArr = objArr2;
            } else {
                typeAliasMap = new HashMap<>();
                int length2 = typeAliasData2.length;
                int i4 = i2;
                while (i4 < length2) {
                    String[] typeAliasDataEntry = typeAliasData2[i4];
                    Object[][] objArr3 = objArr2;
                    String from = typeAliasDataEntry[i2];
                    String to = typeAliasDataEntry[c2];
                    Set<String> aliasSet2 = typeAliasMap.get(to);
                    if (aliasSet2 != null) {
                        aliasSet = aliasSet2;
                    } else {
                        aliasSet = new HashSet<>();
                        typeAliasMap.put(to, aliasSet);
                    }
                    aliasSet.add(from);
                    i4++;
                    objArr2 = objArr3;
                    i2 = 0;
                    c2 = 1;
                }
                objArr = objArr2;
            }
            Map<String, Set<String>> bcpTypeAliasMap2 = null;
            if (bcpTypeAliasData == null) {
                i = length;
            } else {
                bcpTypeAliasMap2 = new HashMap<>();
                int length3 = bcpTypeAliasData.length;
                int i5 = 0;
                while (i5 < length3) {
                    String[] bcpTypeAliasDataEntry = bcpTypeAliasData[i5];
                    String from2 = bcpTypeAliasDataEntry[0];
                    int i6 = length;
                    String to2 = bcpTypeAliasDataEntry[1];
                    Set<String> aliasSet3 = bcpTypeAliasMap2.get(to2);
                    if (aliasSet3 == null) {
                        aliasSet3 = new HashSet<>();
                        bcpTypeAliasMap2.put(to2, aliasSet3);
                    }
                    aliasSet3.add(from2);
                    i5++;
                    length = i6;
                }
                i = length;
            }
            Map<String, Type> typeDataMap = new HashMap<>();
            Set<SpecialType> specialTypeSet2 = null;
            int length4 = typeData.length;
            int i7 = 0;
            while (i7 < length4) {
                String[] typeDataEntry = typeData[i7];
                Object[] keyDataEntry2 = keyDataEntry;
                String legacyTypeId = typeDataEntry[0];
                String bcpTypeId2 = typeDataEntry[1];
                boolean isSpecialType = false;
                SpecialType[] specialTypeArrValues = SpecialType.values();
                int i8 = length4;
                int length5 = specialTypeArrValues.length;
                String[][] typeData2 = typeData;
                int i9 = 0;
                while (true) {
                    if (i9 >= length5) {
                        typeAliasData = typeAliasData2;
                        break;
                    }
                    int i10 = length5;
                    SpecialType st = specialTypeArrValues[i9];
                    typeAliasData = typeAliasData2;
                    if (!legacyTypeId.equals(st.toString())) {
                        i9++;
                        length5 = i10;
                        typeAliasData2 = typeAliasData;
                    } else {
                        isSpecialType = true;
                        if (specialTypeSet2 == null) {
                            specialTypeSet2 = new HashSet<>();
                        }
                        specialTypeSet2.add(st);
                    }
                }
                if (isSpecialType) {
                    bcpTypeAliasMap = bcpTypeAliasMap2;
                    specialTypeSet = specialTypeSet2;
                } else {
                    boolean hasSameType = false;
                    if (bcpTypeId2 != null) {
                        bcpTypeId = bcpTypeId2;
                    } else {
                        hasSameType = true;
                        bcpTypeId = legacyTypeId;
                    }
                    Type t = new Type(legacyTypeId, bcpTypeId);
                    typeDataMap.put(AsciiUtil.toLowerString(legacyTypeId), t);
                    if (!hasSameType) {
                        typeDataMap.put(AsciiUtil.toLowerString(bcpTypeId), t);
                    }
                    Set<String> typeAliasSet = typeAliasMap.get(legacyTypeId);
                    if (typeAliasSet == null) {
                        specialTypeSet = specialTypeSet2;
                    } else {
                        for (String alias : typeAliasSet) {
                            typeDataMap.put(AsciiUtil.toLowerString(alias), t);
                            specialTypeSet2 = specialTypeSet2;
                        }
                        specialTypeSet = specialTypeSet2;
                    }
                    Set<String> bcpTypeAliasSet = bcpTypeAliasMap2.get(bcpTypeId);
                    if (bcpTypeAliasSet == null) {
                        bcpTypeAliasMap = bcpTypeAliasMap2;
                    } else {
                        for (String alias2 : bcpTypeAliasSet) {
                            typeDataMap.put(AsciiUtil.toLowerString(alias2), t);
                            bcpTypeAliasMap2 = bcpTypeAliasMap2;
                        }
                        bcpTypeAliasMap = bcpTypeAliasMap2;
                    }
                }
                i7++;
                keyDataEntry = keyDataEntry2;
                length4 = i8;
                typeData = typeData2;
                typeAliasData2 = typeAliasData;
                specialTypeSet2 = specialTypeSet;
                bcpTypeAliasMap2 = bcpTypeAliasMap;
            }
            EnumSet<SpecialType> specialTypes = specialTypeSet2 != null ? EnumSet.copyOf(specialTypeSet2) : null;
            KeyData keyData = new KeyData(legacyKeyId, bcpKeyId, typeDataMap, specialTypes);
            KEYMAP.put(AsciiUtil.toLowerString(legacyKeyId), keyData);
            if (!hasSameKey) {
                KEYMAP.put(AsciiUtil.toLowerString(bcpKeyId), keyData);
            }
            i3++;
            objArr2 = objArr;
            length = i;
            i2 = 0;
        }
    }

    public static Set<String> getBcp47Keys() {
        return BCP47_KEYS.keySet();
    }

    public static Set<String> getBcp47KeyTypes(String key) {
        return BCP47_KEYS.get(key);
    }

    public static boolean isDeprecated(String key) {
        return DEPRECATED_KEYS.contains(key);
    }

    public static boolean isDeprecated(String key, String type) {
        Set<String> deprecatedTypes = DEPRECATED_KEY_TYPES.get(key);
        if (deprecatedTypes == null) {
            return false;
        }
        return deprecatedTypes.contains(type);
    }

    public static ValueType getValueType(String key) {
        ValueType type = VALUE_TYPES.get(key);
        return type == null ? ValueType.single : type;
    }
}
