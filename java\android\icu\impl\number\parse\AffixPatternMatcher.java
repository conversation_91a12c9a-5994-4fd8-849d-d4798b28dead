package android.icu.impl.number.parse;

import android.icu.impl.number.AffixUtils;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class AffixPatternMatcher extends SeriesMatcher implements AffixUtils.TokenConsumer {
    private final String affixPattern;
    private AffixTokenMatcherFactory factory;
    private IgnorablesMatcher ignorables;
    private int lastTypeOrCp;

    private AffixPatternMatcher(String affixPattern) {
        this.affixPattern = affixPattern;
    }

    public static AffixPatternMatcher fromAffixPattern(String affixPattern, AffixTokenMatcherFactory factory, int parseFlags) {
        if (affixPattern.isEmpty()) {
            return null;
        }
        AffixPatternMatcher series = new AffixPatternMatcher(affixPattern);
        series.factory = factory;
        series.ignorables = (parseFlags & 512) != 0 ? null : factory.ignorables();
        series.lastTypeOrCp = 0;
        AffixUtils.iterateWithConsumer(affixPattern, series);
        series.factory = null;
        series.ignorables = null;
        series.lastTypeOrCp = 0;
        series.freeze();
        return series;
    }

    @Override // android.icu.impl.number.AffixUtils.TokenConsumer
    public void consumeToken(int typeOrCp) {
        if (this.ignorables != null && length() > 0 && (this.lastTypeOrCp < 0 || !this.ignorables.getSet().contains(this.lastTypeOrCp))) {
            addMatcher(this.ignorables);
        }
        if (typeOrCp < 0) {
            switch (typeOrCp) {
                case -9:
                case -8:
                case -7:
                case -6:
                case -5:
                    addMatcher(this.factory.currency());
                    break;
                case -4:
                    addMatcher(this.factory.permille());
                    break;
                case -3:
                    addMatcher(this.factory.percent());
                    break;
                case -2:
                    addMatcher(this.factory.plusSign());
                    break;
                case -1:
                    addMatcher(this.factory.minusSign());
                    break;
                default:
                    throw new AssertionError();
            }
        } else {
            IgnorablesMatcher ignorablesMatcher = this.ignorables;
            if (ignorablesMatcher == null || !ignorablesMatcher.getSet().contains(typeOrCp)) {
                addMatcher(CodePointMatcher.getInstance(typeOrCp));
            }
        }
        this.lastTypeOrCp = typeOrCp;
    }

    public String getPattern() {
        return this.affixPattern;
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof AffixPatternMatcher)) {
            return false;
        }
        return this.affixPattern.equals(((AffixPatternMatcher) other).affixPattern);
    }

    public int hashCode() {
        return this.affixPattern.hashCode();
    }

    @Override // android.icu.impl.number.parse.SeriesMatcher
    public String toString() {
        return this.affixPattern;
    }
}
