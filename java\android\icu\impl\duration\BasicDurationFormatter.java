package android.icu.impl.duration;

import java.util.Date;
import java.util.TimeZone;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
class BasicDurationFormatter implements DurationFormatter {
    private PeriodBuilder builder;
    private DateFormatter fallback;
    private long fallbackLimit;
    private PeriodFormatter formatter;
    private String localeName;
    private TimeZone timeZone;

    public BasicDurationFormatter(PeriodFormatter formatter, PeriodBuilder builder, DateFormatter fallback, long fallbackLimit) {
        this.formatter = formatter;
        this.builder = builder;
        this.fallback = fallback;
        this.fallbackLimit = fallbackLimit >= 0 ? fallbackLimit : 0L;
    }

    protected BasicDurationFormatter(PeriodFormatter formatter, PeriodBuilder builder, DateFormatter fallback, long fallbackLimit, String localeName, TimeZone timeZone) {
        this.formatter = formatter;
        this.builder = builder;
        this.fallback = fallback;
        this.fallbackLimit = fallbackLimit;
        this.localeName = localeName;
        this.timeZone = timeZone;
    }

    @Override // android.icu.impl.duration.DurationFormatter
    public String formatDurationFromNowTo(Date targetDate) {
        long now = System.currentTimeMillis();
        long duration = targetDate.getTime() - now;
        return formatDurationFrom(duration, now);
    }

    @Override // android.icu.impl.duration.DurationFormatter
    public String formatDurationFromNow(long duration) {
        return formatDurationFrom(duration, System.currentTimeMillis());
    }

    @Override // android.icu.impl.duration.DurationFormatter
    public String formatDurationFrom(long duration, long referenceDate) {
        String s = doFallback(duration, referenceDate);
        if (s == null) {
            Period p = doBuild(duration, referenceDate);
            return doFormat(p);
        }
        return s;
    }

    @Override // android.icu.impl.duration.DurationFormatter
    public DurationFormatter withLocale(String locName) {
        DateFormatter newFallback;
        if (!locName.equals(this.localeName)) {
            PeriodFormatter newFormatter = this.formatter.withLocale(locName);
            PeriodBuilder newBuilder = this.builder.withLocale(locName);
            DateFormatter dateFormatter = this.fallback;
            if (dateFormatter == null) {
                newFallback = null;
            } else {
                newFallback = dateFormatter.withLocale(locName);
            }
            return new BasicDurationFormatter(newFormatter, newBuilder, newFallback, this.fallbackLimit, locName, this.timeZone);
        }
        return this;
    }

    @Override // android.icu.impl.duration.DurationFormatter
    public DurationFormatter withTimeZone(TimeZone tz) {
        DateFormatter newFallback;
        if (!tz.equals(this.timeZone)) {
            PeriodBuilder newBuilder = this.builder.withTimeZone(tz);
            DateFormatter dateFormatter = this.fallback;
            if (dateFormatter == null) {
                newFallback = null;
            } else {
                newFallback = dateFormatter.withTimeZone(tz);
            }
            return new BasicDurationFormatter(this.formatter, newBuilder, newFallback, this.fallbackLimit, this.localeName, tz);
        }
        return this;
    }

    protected String doFallback(long duration, long referenceDate) {
        if (this.fallback != null && this.fallbackLimit > 0 && Math.abs(duration) >= this.fallbackLimit) {
            return this.fallback.format(referenceDate + duration);
        }
        return null;
    }

    protected Period doBuild(long duration, long referenceDate) {
        return this.builder.createWithReferenceDate(duration, referenceDate);
    }

    protected String doFormat(Period period) {
        if (!period.isSet()) {
            throw new IllegalArgumentException("period is not set");
        }
        return this.formatter.format(period);
    }
}
