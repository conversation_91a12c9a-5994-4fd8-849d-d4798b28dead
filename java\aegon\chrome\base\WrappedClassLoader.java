package aegon.chrome.base;

import dalvik.system.BaseDexClassLoader;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class WrappedClassLoader extends ClassLoader {
    private ClassLoader mPrimaryClassLoader;
    private ClassLoader mSecondaryClassLoader;

    public WrappedClassLoader(ClassLoader classLoader, ClassLoader classLoader2) {
        this.mPrimaryClassLoader = classLoader;
        this.mSecondaryClassLoader = classLoader2;
    }

    @Override // java.lang.ClassLoader
    protected Class<?> findClass(String str) {
        try {
            return this.mPrimaryClassLoader.loadClass(str);
        } catch (ClassNotFoundException unused) {
            return this.mSecondaryClassLoader.loadClass(str);
        }
    }

    @Override // java.lang.ClassLoader
    public String findLibrary(String str) {
        String strFindLibrary;
        ClassLoader classLoader = this.mPrimaryClassLoader;
        if (classLoader instanceof BaseDexClassLoader) {
            strFindLibrary = ((BaseDexClassLoader) classLoader).findLibrary(str);
            if (strFindLibrary != null) {
                return strFindLibrary;
            }
        } else {
            strFindLibrary = null;
        }
        ClassLoader classLoader2 = this.mSecondaryClassLoader;
        return classLoader2 instanceof BaseDexClassLoader ? ((BaseDexClassLoader) classLoader2).findLibrary(str) : strFindLibrary;
    }
}
