package android.icu.impl.duration;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class Period {
    final int[] counts;
    final boolean inFuture;
    final byte timeLimit;

    /* renamed from: at */
    public static Period m89at(float count, TimeUnit unit) {
        checkCount(count);
        return new Period(0, false, count, unit);
    }

    public static Period moreThan(float count, TimeUnit unit) {
        checkCount(count);
        return new Period(2, false, count, unit);
    }

    public static Period lessThan(float count, TimeUnit unit) {
        checkCount(count);
        return new Period(1, false, count, unit);
    }

    public Period and(float count, TimeUnit unit) {
        checkCount(count);
        return setTimeUnitValue(unit, count);
    }

    public Period omit(TimeUnit unit) {
        return setTimeUnitInternalValue(unit, 0);
    }

    /* renamed from: at */
    public Period m90at() {
        return setTimeLimit((byte) 0);
    }

    public Period moreThan() {
        return setTimeLimit((byte) 2);
    }

    public Period lessThan() {
        return setTimeLimit((byte) 1);
    }

    public Period inFuture() {
        return setFuture(true);
    }

    public Period inPast() {
        return setFuture(false);
    }

    public Period inFuture(boolean future) {
        return setFuture(future);
    }

    public Period inPast(boolean past) {
        return setFuture(!past);
    }

    public boolean isSet() {
        int i = 0;
        while (true) {
            int[] iArr = this.counts;
            if (i < iArr.length) {
                if (iArr[i] == 0) {
                    i++;
                } else {
                    return true;
                }
            } else {
                return false;
            }
        }
    }

    public boolean isSet(TimeUnit unit) {
        return this.counts[unit.ordinal] > 0;
    }

    public float getCount(TimeUnit unit) {
        int ord = unit.ordinal;
        if (this.counts[ord] == 0) {
            return 0.0f;
        }
        return (r1[ord] - 1) / 1000.0f;
    }

    public boolean isInFuture() {
        return this.inFuture;
    }

    public boolean isInPast() {
        return !this.inFuture;
    }

    public boolean isMoreThan() {
        return this.timeLimit == 2;
    }

    public boolean isLessThan() {
        return this.timeLimit == 1;
    }

    public boolean equals(Object rhs) {
        try {
            return equals((Period) rhs);
        } catch (ClassCastException e) {
            return false;
        }
    }

    public boolean equals(Period rhs) {
        if (rhs == null || this.timeLimit != rhs.timeLimit || this.inFuture != rhs.inFuture) {
            return false;
        }
        int i = 0;
        while (true) {
            int[] iArr = this.counts;
            if (i < iArr.length) {
                if (iArr[i] != rhs.counts[i]) {
                    return false;
                }
                i++;
            } else {
                return true;
            }
        }
    }

    public int hashCode() {
        int i = (this.timeLimit << 1) | (this.inFuture ? 1 : 0);
        int i2 = 0;
        while (true) {
            int[] iArr = this.counts;
            if (i2 < iArr.length) {
                i = (i << 2) ^ iArr[i2];
                i2++;
            } else {
                return i;
            }
        }
    }

    private Period(int limit, boolean future, float count, TimeUnit unit) {
        this.timeLimit = (byte) limit;
        this.inFuture = future;
        int[] iArr = new int[TimeUnit.units.length];
        this.counts = iArr;
        iArr[unit.ordinal] = ((int) (1000.0f * count)) + 1;
    }

    Period(int timeLimit, boolean inFuture, int[] counts) {
        this.timeLimit = (byte) timeLimit;
        this.inFuture = inFuture;
        this.counts = counts;
    }

    private Period setTimeUnitValue(TimeUnit unit, float value) {
        if (value < 0.0f) {
            throw new IllegalArgumentException("value: " + value);
        }
        return setTimeUnitInternalValue(unit, ((int) (1000.0f * value)) + 1);
    }

    private Period setTimeUnitInternalValue(TimeUnit unit, int value) {
        int ord = unit.ordinal;
        int[] iArr = this.counts;
        if (iArr[ord] != value) {
            int[] newCounts = new int[iArr.length];
            int i = 0;
            while (true) {
                int[] iArr2 = this.counts;
                if (i < iArr2.length) {
                    newCounts[i] = iArr2[i];
                    i++;
                } else {
                    newCounts[ord] = value;
                    return new Period(this.timeLimit, this.inFuture, newCounts);
                }
            }
        } else {
            return this;
        }
    }

    private Period setFuture(boolean future) {
        if (this.inFuture != future) {
            return new Period(this.timeLimit, future, this.counts);
        }
        return this;
    }

    private Period setTimeLimit(byte limit) {
        if (this.timeLimit != limit) {
            return new Period(limit, this.inFuture, this.counts);
        }
        return this;
    }

    private static void checkCount(float count) {
        if (count < 0.0f) {
            throw new IllegalArgumentException("count (" + count + ") cannot be negative");
        }
    }
}
