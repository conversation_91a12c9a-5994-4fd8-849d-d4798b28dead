package android.compat;

import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes80.dex */
public final class Compatibility {
    private static volatile Callbacks sCallbacks = new Callbacks();

    private Compatibility() {
    }

    public static void reportChange(long changeId) {
        sCallbacks.reportChange(changeId);
    }

    public static boolean isChangeEnabled(long changeId) {
        return sCallbacks.isChangeEnabled(changeId);
    }

    public static void setCallbacks(Callbacks callbacks) {
        Objects.requireNonNull(callbacks);
        sCallbacks = callbacks;
    }

    public static void setOverrides(ChangeConfig overrides) {
        if (sCallbacks instanceof OverrideCallbacks) {
            throw new IllegalStateException("setOverrides has already been called!");
        }
        sCallbacks = new OverrideCallbacks(sCallbacks, overrides);
    }

    public static void clearOverrides() {
        if (!(sCallbacks instanceof OverrideCallbacks)) {
            throw new IllegalStateException("No overrides set");
        }
        sCallbacks = ((OverrideCallbacks) sCallbacks).delegate;
    }

    public static class Callbacks {
        protected Callbacks() {
        }

        protected void reportChange(long changeId) {
            System.logW("No Compatibility callbacks set! Reporting change " + changeId);
        }

        protected boolean isChangeEnabled(long changeId) {
            System.logW("No Compatibility callbacks set! Querying change " + changeId);
            return true;
        }
    }

    public static final class ChangeConfig {
        private final Set<Long> disabled;
        private final Set<Long> enabled;

        public ChangeConfig(Set<Long> enabled, Set<Long> disabled) {
            Objects.requireNonNull(enabled);
            this.enabled = enabled;
            Objects.requireNonNull(disabled);
            this.disabled = disabled;
            if (enabled.contains(null) || disabled.contains(null)) {
                throw null;
            }
            Set<Long> intersection = new HashSet<>(enabled);
            intersection.retainAll(disabled);
            if (!intersection.isEmpty()) {
                throw new IllegalArgumentException("Cannot have changes " + ((Object) intersection) + " enabled and disabled!");
            }
        }

        public boolean isEmpty() {
            return this.enabled.isEmpty() && this.disabled.isEmpty();
        }

        private static long[] toLongArray(Set<Long> values) {
            long[] result = new long[values.size()];
            int idx = 0;
            for (Long value : values) {
                result[idx] = value.longValue();
                idx++;
            }
            return result;
        }

        public long[] forceEnabledChangesArray() {
            return toLongArray(this.enabled);
        }

        public long[] forceDisabledChangesArray() {
            return toLongArray(this.disabled);
        }

        public Set<Long> forceEnabledSet() {
            return Collections.unmodifiableSet(this.enabled);
        }

        public Set<Long> forceDisabledSet() {
            return Collections.unmodifiableSet(this.disabled);
        }

        public boolean isForceEnabled(long changeId) {
            return this.enabled.contains(Long.valueOf(changeId));
        }

        public boolean isForceDisabled(long changeId) {
            return this.disabled.contains(Long.valueOf(changeId));
        }

        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof ChangeConfig)) {
                return false;
            }
            ChangeConfig that = (ChangeConfig) o;
            return this.enabled.equals(that.enabled) && this.disabled.equals(that.disabled);
        }

        public int hashCode() {
            return Objects.hash(this.enabled, this.disabled);
        }

        public String toString() {
            return "ChangeConfig{enabled=" + ((Object) this.enabled) + ", disabled=" + ((Object) this.disabled) + '}';
        }
    }

    private static class OverrideCallbacks extends Callbacks {
        private final ChangeConfig changeConfig;
        private final Callbacks delegate;

        private OverrideCallbacks(Callbacks delegate, ChangeConfig changeConfig) {
            Objects.requireNonNull(delegate);
            this.delegate = delegate;
            Objects.requireNonNull(changeConfig);
            this.changeConfig = changeConfig;
        }

        @Override // android.compat.Compatibility.Callbacks
        protected boolean isChangeEnabled(long changeId) {
            if (this.changeConfig.isForceEnabled(changeId)) {
                return true;
            }
            if (this.changeConfig.isForceDisabled(changeId)) {
                return false;
            }
            return this.delegate.isChangeEnabled(changeId);
        }
    }
}
