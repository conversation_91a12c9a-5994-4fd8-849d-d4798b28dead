package aegon.chrome.base;

import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.compat.ApiHelperForM;
import aegon.chrome.base.compat.ApiHelperForQ;
import aegon.chrome.base.compat.ApiHelperForR;
import aegon.chrome.base.task.AsyncTask;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.os.Build;
import android.os.Environment;
import android.os.storage.StorageManager;
import android.provider.MediaStore;
import android.system.C1087Os;
import android.text.TextUtils;
import java.p654io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.concurrent.atomic.AtomicBoolean;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public abstract class PathUtils {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final int CACHE_DIRECTORY = 2;
    private static final int DATA_DIRECTORY = 0;
    private static final int NUM_DIRECTORIES = 3;
    private static final String TAG = "PathUtils";
    private static final int THUMBNAIL_DIRECTORY = 1;
    private static final String THUMBNAIL_DIRECTORY_NAME = "textures";
    private static String sCacheSubDirectory;
    private static String sDataDirectorySuffix;
    private static FutureTask<String[]> sDirPathFetchTask;
    private static final AtomicBoolean sInitializationStarted = new AtomicBoolean();

    /* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
    static class Holder {
        private static final String[] DIRECTORY_PATHS = PathUtils.access$000();

        private Holder() {
        }
    }

    private PathUtils() {
    }

    public static void resetForTesting() {
        sInitializationStarted.set(false);
        sDirPathFetchTask = null;
        sDataDirectorySuffix = null;
        sCacheSubDirectory = null;
    }

    private static String[] getOrComputeDirectoryPaths() {
        if (!sDirPathFetchTask.isDone()) {
            StrictModeContext strictModeContextAllowDiskWrites = StrictModeContext.allowDiskWrites();
            try {
                sDirPathFetchTask.run();
                if (strictModeContextAllowDiskWrites != null) {
                    strictModeContextAllowDiskWrites.lambda$new$0();
                }
            } catch (Throwable th) {
                if (strictModeContextAllowDiskWrites != null) {
                    try {
                        strictModeContextAllowDiskWrites.lambda$new$0();
                    } catch (Throwable unused) {
                    }
                }
                throw th;
            }
        }
        try {
            return sDirPathFetchTask.get();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void chmod(String str, int i) {
        if (Build.VERSION.SDK_INT < 21) {
            return;
        }
        try {
            C1087Os.chmod(str, i);
        } catch (Exception unused) {
            Log.m43e(TAG, "Failed to set permissions for path \"" + str + "\"", new Object[0]);
        }
    }

    private static String[] setPrivateDataDirectorySuffixInternal() {
        String[] strArr = new String[3];
        Context applicationContext = ContextUtils.getApplicationContext();
        strArr[0] = applicationContext.getDir(sDataDirectorySuffix, 0).getPath();
        chmod(strArr[0], com.kwai.video.player.kwai_player.FileUtils.S_IRWXU);
        strArr[1] = applicationContext.getDir(THUMBNAIL_DIRECTORY_NAME, 0).getPath();
        if (applicationContext.getCacheDir() != null) {
            if (sCacheSubDirectory == null) {
                strArr[2] = applicationContext.getCacheDir().getPath();
            } else {
                File file = new File(applicationContext.getCacheDir(), sCacheSubDirectory);
                file.mkdir();
                strArr[2] = file.getPath();
                chmod(strArr[2], 1472);
            }
        }
        return strArr;
    }

    public static void setPrivateDataDirectorySuffix(String str, String str2) {
        if (sInitializationStarted.getAndSet(true)) {
            return;
        }
        sDataDirectorySuffix = str;
        sCacheSubDirectory = str2;
        sDirPathFetchTask = new FutureTask<>(new Callable() { // from class: aegon.chrome.base.-$$Lambda$PathUtils$Yb1cAP7EYzI7ZxGQFHcNjoxaTcQ
            @Override // java.util.concurrent.Callable
            public final Object call() {
                return PathUtils.setPrivateDataDirectorySuffixInternal();
            }
        });
        AsyncTask.THREAD_POOL_EXECUTOR.execute(sDirPathFetchTask);
    }

    public static void setPrivateDataDirectorySuffix(String str) {
        setPrivateDataDirectorySuffix(str, null);
    }

    private static String getDirectoryPath(int i) {
        return getOrComputeDirectoryPaths()[i];
    }

    public static String getDataDirectory() {
        return getDirectoryPath(0);
    }

    public static String getCacheDirectory() {
        return getDirectoryPath(2);
    }

    public static String getThumbnailCacheDirectory() {
        return getDirectoryPath(1);
    }

    public static String getDownloadsDirectory() {
        StrictModeContext strictModeContextAllowDiskReads = StrictModeContext.allowDiskReads();
        try {
            if (Build.VERSION.SDK_INT >= 29) {
                String[] allPrivateDownloadsDirectories = getAllPrivateDownloadsDirectories();
                String str = allPrivateDownloadsDirectories.length == 0 ? "" : allPrivateDownloadsDirectories[0];
                if (strictModeContextAllowDiskReads != null) {
                    strictModeContextAllowDiskReads.lambda$new$0();
                }
                return str;
            }
            String path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath();
            if (strictModeContextAllowDiskReads != null) {
                strictModeContextAllowDiskReads.lambda$new$0();
            }
            return path;
        } catch (Throwable th) {
            if (strictModeContextAllowDiskReads != null) {
                try {
                    strictModeContextAllowDiskReads.lambda$new$0();
                } catch (Throwable unused) {
                }
            }
            throw th;
        }
    }

    public static String[] getAllPrivateDownloadsDirectories() {
        List arrayList = new ArrayList();
        StrictModeContext strictModeContextAllowDiskWrites = StrictModeContext.allowDiskWrites();
        try {
            File[] externalFilesDirs = ContextUtils.getApplicationContext().getExternalFilesDirs(Environment.DIRECTORY_DOWNLOADS);
            if (externalFilesDirs != null) {
                arrayList = Arrays.asList(externalFilesDirs);
            }
            if (strictModeContextAllowDiskWrites != null) {
                strictModeContextAllowDiskWrites.lambda$new$0();
            }
            return toAbsolutePathStrings(arrayList);
        } catch (Throwable th) {
            if (strictModeContextAllowDiskWrites != null) {
                try {
                    strictModeContextAllowDiskWrites.lambda$new$0();
                } catch (Throwable unused) {
                }
            }
            throw th;
        }
    }

    public static String[] getExternalDownloadVolumesNames() {
        ArrayList arrayList = new ArrayList();
        for (String str : ApiHelperForQ.getExternalVolumeNames(ContextUtils.getApplicationContext())) {
            if (!TextUtils.isEmpty(str) && !str.contains(MediaStore.VOLUME_EXTERNAL_PRIMARY)) {
                arrayList.add(new File(ApiHelperForR.getVolumeDir((StorageManager) ApiHelperForM.getSystemService(ContextUtils.getApplicationContext(), StorageManager.class), MediaStore.Files.getContentUri(str)).getAbsolutePath(), Environment.DIRECTORY_DOWNLOADS));
            }
        }
        return toAbsolutePathStrings(arrayList);
    }

    private static String[] toAbsolutePathStrings(List<File> list) {
        ArrayList arrayList = new ArrayList();
        for (File file : list) {
            if (file != null && !TextUtils.isEmpty(file.getAbsolutePath())) {
                arrayList.add(file.getAbsolutePath());
            }
        }
        return (String[]) arrayList.toArray(new String[arrayList.size()]);
    }

    private static String getNativeLibraryDirectory() {
        ApplicationInfo applicationInfo = ContextUtils.getApplicationContext().getApplicationInfo();
        return ((applicationInfo.flags & 128) != 0 || (applicationInfo.flags & 1) == 0) ? applicationInfo.nativeLibraryDir : "/system/lib/";
    }

    public static String getExternalStorageDirectory() {
        return Environment.getExternalStorageDirectory().getAbsolutePath();
    }
}
