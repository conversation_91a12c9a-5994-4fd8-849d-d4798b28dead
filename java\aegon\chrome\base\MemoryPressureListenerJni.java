package aegon.chrome.base;

import aegon.chrome.base.MemoryPressureListener;
import aegon.chrome.base.annotations.MainDex;
import aegon.chrome.base.natives.GEN_JNI;

@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class MemoryPressureListenerJni implements MemoryPressureListener.Natives {
    public static final JniStaticTestMocker<MemoryPressureListener.Natives> TEST_HOOKS = new JniStaticTestMocker<MemoryPressureListener.Natives>() { // from class: aegon.chrome.base.MemoryPressureListenerJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(MemoryPressureListener.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                MemoryPressureListener.Natives unused = MemoryPressureListenerJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static MemoryPressureListener.Natives testInstance;

    MemoryPressureListenerJni() {
    }

    @Override // aegon.chrome.base.MemoryPressureListener.Natives
    public void onMemoryPressure(int i) {
        GEN_JNI.org_chromium_base_MemoryPressureListener_onMemoryPressure(i);
    }

    public static MemoryPressureListener.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            MemoryPressureListener.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.MemoryPressureListener.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(true);
        return new MemoryPressureListenerJni();
    }
}
