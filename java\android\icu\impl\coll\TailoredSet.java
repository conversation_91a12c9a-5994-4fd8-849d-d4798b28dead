package android.icu.impl.coll;

import android.icu.impl.Normalizer2Impl;
import android.icu.impl.Trie2;
import android.icu.impl.Utility;
import android.icu.text.UnicodeSet;
import android.icu.util.CharsTrie;
import java.util.Iterator;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class TailoredSet {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private CollationData baseData;
    private CollationData data;
    private String suffix;
    private UnicodeSet tailored;
    private StringBuilder unreversedPrefix = new StringBuilder();

    public TailoredSet(UnicodeSet t) {
        this.tailored = t;
    }

    public void forData(CollationData d2) {
        this.data = d2;
        this.baseData = d2.base;
        Iterator<Trie2.Range> trieIterator = this.data.trie.iterator2();
        while (trieIterator.hasNext()) {
            Trie2.Range range = trieIterator.mo35924next();
            if (!range.leadSurrogate) {
                enumTailoredRange(range.startCodePoint, range.endCodePoint, range.value, this);
            } else {
                return;
            }
        }
    }

    private void enumTailoredRange(int start, int end, int ce32, TailoredSet ts) {
        if (ce32 == 192) {
            return;
        }
        ts.handleCE32(start, end, ce32);
    }

    private void handleCE32(int start, int end, int ce32) {
        if (Collation.isSpecialCE32(ce32) && (ce32 = this.data.getIndirectCE32(ce32)) == 192) {
            return;
        }
        do {
            CollationData collationData = this.baseData;
            int baseCE32 = collationData.getFinalCE32(collationData.getCE32(start));
            if (Collation.isSelfContainedCE32(ce32) && Collation.isSelfContainedCE32(baseCE32)) {
                if (ce32 != baseCE32) {
                    this.tailored.add(start);
                }
            } else {
                compare(start, ce32, baseCE32);
            }
            start++;
        } while (start <= end);
    }

    private void compare(int c2, int ce32, int baseCE32) {
        int tag;
        int baseTag;
        if (Collation.isPrefixCE32(ce32)) {
            int dataIndex = Collation.indexFromCE32(ce32);
            CollationData collationData = this.data;
            ce32 = collationData.getFinalCE32(collationData.getCE32FromContexts(dataIndex));
            if (Collation.isPrefixCE32(baseCE32)) {
                int baseIndex = Collation.indexFromCE32(baseCE32);
                CollationData collationData2 = this.baseData;
                baseCE32 = collationData2.getFinalCE32(collationData2.getCE32FromContexts(baseIndex));
                comparePrefixes(c2, this.data.contexts, dataIndex + 2, this.baseData.contexts, baseIndex + 2);
            } else {
                CollationData collationData3 = this.data;
                addPrefixes(collationData3, c2, collationData3.contexts, dataIndex + 2);
            }
        } else if (Collation.isPrefixCE32(baseCE32)) {
            int baseIndex2 = Collation.indexFromCE32(baseCE32);
            CollationData collationData4 = this.baseData;
            baseCE32 = collationData4.getFinalCE32(collationData4.getCE32FromContexts(baseIndex2));
            CollationData collationData5 = this.baseData;
            addPrefixes(collationData5, c2, collationData5.contexts, baseIndex2 + 2);
        }
        if (Collation.isContractionCE32(ce32)) {
            int dataIndex2 = Collation.indexFromCE32(ce32);
            if ((ce32 & 256) != 0) {
                ce32 = 1;
            } else {
                CollationData collationData6 = this.data;
                ce32 = collationData6.getFinalCE32(collationData6.getCE32FromContexts(dataIndex2));
            }
            if (Collation.isContractionCE32(baseCE32)) {
                int baseIndex3 = Collation.indexFromCE32(baseCE32);
                if ((baseCE32 & 256) != 0) {
                    baseCE32 = 1;
                } else {
                    CollationData collationData7 = this.baseData;
                    baseCE32 = collationData7.getFinalCE32(collationData7.getCE32FromContexts(baseIndex3));
                }
                compareContractions(c2, this.data.contexts, dataIndex2 + 2, this.baseData.contexts, baseIndex3 + 2);
            } else {
                addContractions(c2, this.data.contexts, dataIndex2 + 2);
            }
        } else if (Collation.isContractionCE32(baseCE32)) {
            int baseIndex4 = Collation.indexFromCE32(baseCE32);
            CollationData collationData8 = this.baseData;
            baseCE32 = collationData8.getFinalCE32(collationData8.getCE32FromContexts(baseIndex4));
            addContractions(c2, this.baseData.contexts, baseIndex4 + 2);
        }
        if (Collation.isSpecialCE32(ce32)) {
            tag = Collation.tagFromCE32(ce32);
        } else {
            tag = -1;
        }
        if (Collation.isSpecialCE32(baseCE32)) {
            baseTag = Collation.tagFromCE32(baseCE32);
        } else {
            baseTag = -1;
        }
        if (baseTag == 14) {
            if (!Collation.isLongPrimaryCE32(ce32)) {
                add(c2);
                return;
            }
            long dataCE = this.baseData.ces[Collation.indexFromCE32(baseCE32)];
            long p = Collation.getThreeBytePrimaryForOffsetData(c2, dataCE);
            if (Collation.primaryFromLongPrimaryCE32(ce32) != p) {
                add(c2);
                return;
            }
        }
        if (tag != baseTag) {
            add(c2);
            return;
        }
        if (tag == 5) {
            int length = Collation.lengthFromCE32(ce32);
            int baseLength = Collation.lengthFromCE32(baseCE32);
            if (length != baseLength) {
                add(c2);
                return;
            }
            int idx0 = Collation.indexFromCE32(ce32);
            int idx1 = Collation.indexFromCE32(baseCE32);
            for (int i = 0; i < length; i++) {
                if (this.data.ce32s[idx0 + i] != this.baseData.ce32s[idx1 + i]) {
                    add(c2);
                    return;
                }
            }
            return;
        }
        if (tag == 6) {
            int length2 = Collation.lengthFromCE32(ce32);
            int baseLength2 = Collation.lengthFromCE32(baseCE32);
            if (length2 != baseLength2) {
                add(c2);
                return;
            }
            int idx02 = Collation.indexFromCE32(ce32);
            int idx12 = Collation.indexFromCE32(baseCE32);
            for (int i2 = 0; i2 < length2; i2++) {
                if (this.data.ces[idx02 + i2] != this.baseData.ces[idx12 + i2]) {
                    add(c2);
                    return;
                }
            }
            return;
        }
        if (tag == 12) {
            StringBuilder jamos = new StringBuilder();
            int length3 = Normalizer2Impl.Hangul.decompose(c2, jamos);
            if (this.tailored.contains(jamos.charAt(0)) || this.tailored.contains(jamos.charAt(1)) || (length3 == 3 && this.tailored.contains(jamos.charAt(2)))) {
                add(c2);
                return;
            }
            return;
        }
        if (ce32 != baseCE32) {
            add(c2);
        }
    }

    private void comparePrefixes(int c2, CharSequence p, int pidx, CharSequence q, int qidx) {
        Iterator<CharsTrie.Entry> itIterator2 = new CharsTrie(p, pidx).iterator2();
        Iterator<CharsTrie.Entry> itIterator22 = new CharsTrie(q, qidx).iterator2();
        String tp = null;
        String bp = null;
        CharsTrie.Entry te = null;
        CharsTrie.Entry be = null;
        while (true) {
            if (tp == null) {
                if (itIterator2.hasNext()) {
                    te = itIterator2.mo35924next();
                    tp = te.chars.toString();
                } else {
                    te = null;
                    tp = "\uffff";
                }
            }
            if (bp == null) {
                if (itIterator22.hasNext()) {
                    be = itIterator22.mo35924next();
                    bp = be.chars.toString();
                } else {
                    be = null;
                    bp = "\uffff";
                }
            }
            if (!Utility.sameObjects(tp, "\uffff") || !Utility.sameObjects(bp, "\uffff")) {
                int cmp = tp.compareTo(bp);
                if (cmp < 0) {
                    addPrefix(this.data, tp, c2, te.value);
                    te = null;
                    tp = null;
                } else if (cmp > 0) {
                    addPrefix(this.baseData, bp, c2, be.value);
                    be = null;
                    bp = null;
                } else {
                    setPrefix(tp);
                    compare(c2, te.value, be.value);
                    resetPrefix();
                    be = null;
                    te = null;
                    bp = null;
                    tp = null;
                }
            } else {
                return;
            }
        }
    }

    private void compareContractions(int c2, CharSequence p, int pidx, CharSequence q, int qidx) {
        Iterator<CharsTrie.Entry> itIterator2 = new CharsTrie(p, pidx).iterator2();
        Iterator<CharsTrie.Entry> itIterator22 = new CharsTrie(q, qidx).iterator2();
        String ts = null;
        String bs = null;
        CharsTrie.Entry te = null;
        CharsTrie.Entry be = null;
        while (true) {
            if (ts == null) {
                if (itIterator2.hasNext()) {
                    te = itIterator2.mo35924next();
                    ts = te.chars.toString();
                } else {
                    te = null;
                    ts = "\uffff\uffff";
                }
            }
            if (bs == null) {
                if (itIterator22.hasNext()) {
                    be = itIterator22.mo35924next();
                    bs = be.chars.toString();
                } else {
                    be = null;
                    bs = "\uffff\uffff";
                }
            }
            if (!Utility.sameObjects(ts, "\uffff\uffff") || !Utility.sameObjects(bs, "\uffff\uffff")) {
                int cmp = ts.compareTo(bs);
                if (cmp < 0) {
                    addSuffix(c2, ts);
                    te = null;
                    ts = null;
                } else if (cmp > 0) {
                    addSuffix(c2, bs);
                    be = null;
                    bs = null;
                } else {
                    this.suffix = ts;
                    compare(c2, te.value, be.value);
                    this.suffix = null;
                    be = null;
                    te = null;
                    bs = null;
                    ts = null;
                }
            } else {
                return;
            }
        }
    }

    private void addPrefixes(CollationData d2, int c2, CharSequence p, int pidx) {
        Iterator<CharsTrie.Entry> itIterator2 = new CharsTrie(p, pidx).iterator2();
        while (itIterator2.hasNext()) {
            CharsTrie.Entry e = itIterator2.mo35924next();
            addPrefix(d2, e.chars, c2, e.value);
        }
    }

    private void addPrefix(CollationData d2, CharSequence pfx, int c2, int ce32) {
        setPrefix(pfx);
        int ce322 = d2.getFinalCE32(ce32);
        if (Collation.isContractionCE32(ce322)) {
            int idx = Collation.indexFromCE32(ce322);
            addContractions(c2, d2.contexts, idx + 2);
        }
        this.tailored.add(new StringBuilder(this.unreversedPrefix.appendCodePoint(c2)));
        resetPrefix();
    }

    private void addContractions(int c2, CharSequence p, int pidx) {
        Iterator<CharsTrie.Entry> itIterator2 = new CharsTrie(p, pidx).iterator2();
        while (itIterator2.hasNext()) {
            CharsTrie.Entry e = itIterator2.mo35924next();
            addSuffix(c2, e.chars);
        }
    }

    private void addSuffix(int c2, CharSequence sfx) {
        UnicodeSet unicodeSet = this.tailored;
        StringBuilder sbAppendCodePoint = new StringBuilder(this.unreversedPrefix).appendCodePoint(c2);
        sbAppendCodePoint.append(sfx);
        unicodeSet.add(sbAppendCodePoint);
    }

    private void add(int c2) {
        if (this.unreversedPrefix.length() == 0 && this.suffix == null) {
            this.tailored.add(c2);
            return;
        }
        StringBuilder s = new StringBuilder(this.unreversedPrefix);
        s.appendCodePoint(c2);
        String str = this.suffix;
        if (str != null) {
            s.append(str);
        }
        this.tailored.add(s);
    }

    private void setPrefix(CharSequence pfx) {
        this.unreversedPrefix.setLength(0);
        StringBuilder sb = this.unreversedPrefix;
        sb.append(pfx);
        sb.reverse();
    }

    private void resetPrefix() {
        this.unreversedPrefix.setLength(0);
    }
}
