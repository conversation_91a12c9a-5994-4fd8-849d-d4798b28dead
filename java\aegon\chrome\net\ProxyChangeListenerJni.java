package aegon.chrome.net;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.net.ProxyChangeListener;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class ProxyChangeListenerJni implements ProxyChangeListener.Natives {
    public static final JniStaticTestMocker<ProxyChangeListener.Natives> TEST_HOOKS = new JniStaticTestMocker<ProxyChangeListener.Natives>() { // from class: aegon.chrome.net.ProxyChangeListenerJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(ProxyChangeListener.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                ProxyChangeListener.Natives unused = ProxyChangeListenerJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static ProxyChangeListener.Natives testInstance;

    ProxyChangeListenerJni() {
    }

    @Override // aegon.chrome.net.ProxyChangeListener.Natives
    public void proxySettingsChangedTo(long j, ProxyChangeListener proxyChangeListener, String str, int i, String str2, String[] strArr) {
        GEN_JNI.org_chromium_net_ProxyChangeListener_proxySettingsChangedTo(j, proxyChangeListener, str, i, str2, strArr);
    }

    @Override // aegon.chrome.net.ProxyChangeListener.Natives
    public void proxySettingsChanged(long j, ProxyChangeListener proxyChangeListener) {
        GEN_JNI.org_chromium_net_ProxyChangeListener_proxySettingsChanged(j, proxyChangeListener);
    }

    public static ProxyChangeListener.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            ProxyChangeListener.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.net.ProxyChangeListener.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new ProxyChangeListenerJni();
    }
}
