package android.icu.impl.locale;

import java.util.Objects;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class LSR {
    public static final boolean DEBUG_OUTPUT = false;
    public static final int REGION_INDEX_LIMIT = 1677;
    public final String language;
    public final String region;
    final int regionIndex;
    public final String script;

    public LSR(String language, String script, String region) {
        this.language = language;
        this.script = script;
        this.region = region;
        this.regionIndex = indexForRegion(region);
    }

    public static final int indexForRegion(String region) {
        int a2;
        int b2;
        int c2;
        int b3;
        if (region.length() == 2) {
            int a3 = region.charAt(0) - 'A';
            if (a3 < 0 || 25 < a3 || region.charAt(1) - 'A' < 0 || 25 < b3) {
                return 0;
            }
            return (a3 * 26) + b3 + 1001;
        }
        if (region.length() != 3 || region.charAt(0) - '0' < 0 || 9 < a2 || region.charAt(1) - '0' < 0 || 9 < b2 || region.charAt(2) - '0' < 0 || 9 < c2) {
            return 0;
        }
        return (((a2 * 10) + b2) * 10) + c2 + 1;
    }

    public String toString() {
        StringBuilder result = new StringBuilder(this.language);
        if (!this.script.isEmpty()) {
            result.append('-');
            result.append(this.script);
        }
        if (!this.region.isEmpty()) {
            result.append('-');
            result.append(this.region);
        }
        return result.toString();
    }

    public boolean equals(Object obj) {
        if (this != obj) {
            if (obj != null && obj.getClass() == getClass()) {
                LSR other = (LSR) obj;
                if (!this.language.equals(other.language) || !this.script.equals(other.script) || !this.region.equals(other.region)) {
                }
            }
            return false;
        }
        return true;
    }

    public int hashCode() {
        return Objects.hash(this.language, this.script, this.region);
    }
}
