package aegon.chrome.net;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class ProxyChangeListener$$Lambda$1 implements Runnable {
    private final ProxyChangeListener arg$1;

    private ProxyChangeListener$$Lambda$1(ProxyChangeListener proxyChangeListener) {
        this.arg$1 = proxyChangeListener;
    }

    public static Runnable lambdaFactory$(ProxyChangeListener proxyChangeListener) {
        return new ProxyChangeListener$$Lambda$1(proxyChangeListener);
    }

    @Override // java.lang.Runnable
    public final void run() {
        ProxyChangeListener.lambda$updateProxyConfigFromConnectivityManager$0(this.arg$1);
    }
}
