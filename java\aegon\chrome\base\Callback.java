package aegon.chrome.base;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@FunctionalInterface
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public interface Callback<T> {
    Runnable bind(T t);

    void onResult(T t);

    /* renamed from: aegon.chrome.base.Callback$-CC, reason: invalid class name */
    public final /* synthetic */ class CC {
        public static Runnable $default$bind(final Callback _this, final Object obj) {
            return new Runnable() { // from class: aegon.chrome.base.-$$Lambda$Callback$Ibjo25PBmrnKQEb2MgjDb_QFEto
                @Override // java.lang.Runnable
                public final void run() {
                    _this.onResult(obj);
                }
            };
        }
    }

    /* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
    public static abstract class Helper {
        static void onObjectResultFromNative(Callback callback, Object obj) {
            callback.onResult(obj);
        }

        static void onBooleanResultFromNative(Callback callback, boolean z) {
            callback.onResult(Boolean.valueOf(z));
        }

        static void onIntResultFromNative(Callback callback, int i) {
            callback.onResult(Integer.valueOf(i));
        }

        static void onTimeResultFromNative(Callback callback, long j) {
            callback.onResult(Long.valueOf(j));
        }

        static void runRunnable(Runnable runnable) {
            runnable.run();
        }
    }
}
