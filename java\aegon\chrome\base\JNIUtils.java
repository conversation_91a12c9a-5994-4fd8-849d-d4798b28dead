package aegon.chrome.base;

import aegon.chrome.base.annotations.MainDex;
import android.content.Context;
import android.text.TextUtils;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
@MainDex
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class JNIUtils {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static ClassLoader sJniClassLoader;
    private static Boolean sSelectiveJniRegistrationEnabled;

    private static ClassLoader getClassLoader() {
        ClassLoader classLoader = sJniClassLoader;
        return classLoader == null ? JNIUtils.class.getClassLoader() : classLoader;
    }

    public static ClassLoader getSplitClassLoader(String str) {
        Context applicationContext = ContextUtils.getApplicationContext();
        if (!TextUtils.isEmpty(str) && BundleUtils.isIsolatedSplitInstalled(applicationContext, str)) {
            return BundleUtils.createIsolatedSplitContext(applicationContext, str).getClassLoader();
        }
        return getClassLoader();
    }

    public static void setClassLoader(ClassLoader classLoader) {
        sJniClassLoader = classLoader;
    }

    public static boolean isSelectiveJniRegistrationEnabled() {
        if (sSelectiveJniRegistrationEnabled == null) {
            sSelectiveJniRegistrationEnabled = Boolean.FALSE;
        }
        return sSelectiveJniRegistrationEnabled.booleanValue();
    }

    public static void enableSelectiveJniRegistration() {
        sSelectiveJniRegistrationEnabled = Boolean.TRUE;
    }
}
