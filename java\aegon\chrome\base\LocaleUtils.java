package aegon.chrome.base;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Build;
import android.os.LocaleList;
import android.text.TextUtils;
import com.getui.gtc.extension.distribution.gbd.p150d.C1928e;
import java.util.ArrayList;
import java.util.Locale;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class LocaleUtils {
    private LocaleUtils() {
    }

    /* JADX WARN: Removed duplicated region for block: B:28:0x004f  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static java.lang.String getUpdatedLanguageForChromium(java.lang.String r6) {
        /*
            int r0 = r6.hashCode()
            r1 = 3365(0xd25, float:4.715E-42)
            r2 = 4
            r3 = 3
            r4 = 2
            r5 = 1
            if (r0 == r1) goto L45
            r1 = 3374(0xd2e, float:4.728E-42)
            if (r0 == r1) goto L3b
            r1 = 3391(0xd3f, float:4.752E-42)
            if (r0 == r1) goto L31
            r1 = 3405(0xd4d, float:4.771E-42)
            if (r0 == r1) goto L27
            r1 = 3704(0xe78, float:5.19E-42)
            if (r0 == r1) goto L1d
            goto L4f
        L1d:
            java.lang.String r0 = "tl"
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L4f
            r0 = 3
            goto L50
        L27:
            java.lang.String r0 = "jw"
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L4f
            r0 = 4
            goto L50
        L31:
            java.lang.String r0 = "ji"
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L4f
            r0 = 1
            goto L50
        L3b:
            java.lang.String r0 = "iw"
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L4f
            r0 = 0
            goto L50
        L45:
            java.lang.String r0 = "in"
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L4f
            r0 = 2
            goto L50
        L4f:
            r0 = -1
        L50:
            if (r0 == 0) goto L67
            if (r0 == r5) goto L64
            if (r0 == r4) goto L61
            if (r0 == r3) goto L5e
            if (r0 == r2) goto L5b
            return r6
        L5b:
            java.lang.String r6 = "jv"
            return r6
        L5e:
            java.lang.String r6 = "fil"
            return r6
        L61:
            java.lang.String r6 = "id"
            return r6
        L64:
            java.lang.String r6 = "yi"
            return r6
        L67:
            java.lang.String r6 = "he"
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: aegon.chrome.base.LocaleUtils.getUpdatedLanguageForChromium(java.lang.String):java.lang.String");
    }

    public static Locale getUpdatedLocaleForChromium(Locale locale) {
        String language = locale.getLanguage();
        String updatedLanguageForChromium = getUpdatedLanguageForChromium(language);
        return updatedLanguageForChromium.equals(language) ? locale : new Locale.Builder().setLocale(locale).setLanguage(updatedLanguageForChromium).build();
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x0024  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static java.lang.String getUpdatedLanguageForAndroid(java.lang.String r3) {
        /*
            int r0 = r3.hashCode()
            r1 = 101385(0x18c09, float:1.4207E-40)
            r2 = 1
            if (r0 == r1) goto L1a
            r1 = 115947(0x1c4eb, float:1.62476E-40)
            if (r0 == r1) goto L10
            goto L24
        L10:
            java.lang.String r0 = "und"
            boolean r0 = r3.equals(r0)
            if (r0 == 0) goto L24
            r0 = 0
            goto L25
        L1a:
            java.lang.String r0 = "fil"
            boolean r0 = r3.equals(r0)
            if (r0 == 0) goto L24
            r0 = 1
            goto L25
        L24:
            r0 = -1
        L25:
            if (r0 == 0) goto L2d
            if (r0 == r2) goto L2a
            return r3
        L2a:
            java.lang.String r3 = "tl"
            return r3
        L2d:
            java.lang.String r3 = ""
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: aegon.chrome.base.LocaleUtils.getUpdatedLanguageForAndroid(java.lang.String):java.lang.String");
    }

    public static Locale getUpdatedLocaleForAndroid(Locale locale) {
        String language = locale.getLanguage();
        String updatedLanguageForAndroid = getUpdatedLanguageForAndroid(language);
        return updatedLanguageForAndroid.equals(language) ? locale : new Locale.Builder().setLocale(locale).setLanguage(updatedLanguageForAndroid).build();
    }

    public static Locale forLanguageTagCompat(String str) {
        String[] strArrSplit = str.split("-");
        if (strArrSplit.length == 0) {
            return new Locale("");
        }
        String updatedLanguageForAndroid = getUpdatedLanguageForAndroid(strArrSplit[0]);
        if (updatedLanguageForAndroid.length() != 2 && updatedLanguageForAndroid.length() != 3) {
            return new Locale("");
        }
        if (strArrSplit.length == 1) {
            return new Locale(updatedLanguageForAndroid);
        }
        String str2 = strArrSplit[1];
        if (str2.length() != 2 && str2.length() != 3) {
            return new Locale(updatedLanguageForAndroid);
        }
        return new Locale(updatedLanguageForAndroid, str2);
    }

    public static Locale forLanguageTag(String str) {
        if (Build.VERSION.SDK_INT >= 21) {
            return getUpdatedLocaleForAndroid(Locale.forLanguageTag(str));
        }
        return forLanguageTagCompat(str);
    }

    public static String toLanguageTag(Locale locale) {
        String updatedLanguageForChromium = getUpdatedLanguageForChromium(locale.getLanguage());
        String country = locale.getCountry();
        if (updatedLanguageForChromium.equals("no") && country.equals("NO") && locale.getVariant().equals("NY")) {
            return "nn-NO";
        }
        if (country.isEmpty()) {
            return updatedLanguageForChromium;
        }
        return updatedLanguageForChromium + "-" + country;
    }

    public static String toLanguageTags(LocaleList localeList) {
        ArrayList arrayList = new ArrayList();
        for (int i = 0; i < localeList.size(); i++) {
            arrayList.add(toLanguageTag(getUpdatedLocaleForChromium(localeList.get(i))));
        }
        return TextUtils.join(C1928e.f5807a, arrayList);
    }

    public static String toLanguage(String str) {
        int iIndexOf = str.indexOf(45);
        return iIndexOf < 0 ? str : str.substring(0, iIndexOf);
    }

    public static boolean isBaseLanguageEqual(String str, String str2) {
        return TextUtils.equals(toLanguage(str), toLanguage(str2));
    }

    public static String getDefaultLocaleString() {
        return toLanguageTag(Locale.getDefault());
    }

    public static String getDefaultLocaleListString() {
        if (Build.VERSION.SDK_INT >= 24) {
            return toLanguageTags(LocaleList.getDefault());
        }
        return getDefaultLocaleString();
    }

    private static String getDefaultCountryCode() {
        CommandLine commandLine = CommandLine.getInstance();
        if (commandLine.hasSwitch(BaseSwitches.DEFAULT_COUNTRY_CODE_AT_INSTALL)) {
            return commandLine.getSwitchValue(BaseSwitches.DEFAULT_COUNTRY_CODE_AT_INSTALL);
        }
        return Locale.getDefault().getCountry();
    }

    public static String getConfigurationLanguage(Configuration configuration) {
        Locale locale = configuration.locale;
        return locale != null ? locale.toLanguageTag() : "";
    }

    public static String getContextLanguage(Context context) {
        return getConfigurationLanguage(context.getResources().getConfiguration());
    }

    public static void updateConfig(Context context, Configuration configuration, String str) {
        if (Build.VERSION.SDK_INT >= 24) {
            ApisN.setConfigLocales(context, configuration, str);
        } else {
            configuration.setLocale(Locale.forLanguageTag(str));
        }
    }

    public static void setDefaultLocalesFromConfiguration(Configuration configuration) {
        if (Build.VERSION.SDK_INT >= 24) {
            ApisN.setLocaleList(configuration);
        } else {
            Locale.setDefault(configuration.locale);
        }
    }

    static class ApisN {
        ApisN() {
        }

        static void setConfigLocales(Context context, Configuration configuration, String str) {
            configuration.setLocales(prependToLocaleList(str, context.getResources().getConfiguration().getLocales()));
        }

        static void setLocaleList(Configuration configuration) {
            LocaleList.setDefault(configuration.getLocales());
        }

        static LocaleList prependToLocaleList(String str, LocaleList localeList) {
            return LocaleList.forLanguageTags(String.format("%1$s,%2$s", str, localeList.toLanguageTags().replaceFirst(String.format("(^|,)%1$s$|%1$s,", str), "")));
        }
    }
}
