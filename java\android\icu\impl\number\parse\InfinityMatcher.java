package android.icu.impl.number.parse;

import android.icu.impl.StaticUnicodeSets;
import android.icu.impl.StringSegment;
import android.icu.text.DecimalFormatSymbols;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class InfinityMatcher extends SymbolMatcher {
    private static final InfinityMatcher DEFAULT = new InfinityMatcher();

    public static InfinityMatcher getInstance(DecimalFormatSymbols symbols) {
        String symbolString = symbols.getInfinity();
        if (ParsingUtils.safeContains(DEFAULT.uniSet, symbolString)) {
            return DEFAULT;
        }
        return new InfinityMatcher(symbolString);
    }

    private InfinityMatcher(String symbolString) {
        super(symbolString, DEFAULT.uniSet);
    }

    private InfinityMatcher() {
        super(StaticUnicodeSets.Key.INFINITY_SIGN);
    }

    @Override // android.icu.impl.number.parse.SymbolMatcher
    protected boolean isDisabled(ParsedNumber result) {
        return (result.flags & 128) != 0;
    }

    @Override // android.icu.impl.number.parse.SymbolMatcher
    protected void accept(StringSegment segment, ParsedNumber result) {
        result.flags |= 128;
        result.setCharsConsumed(segment);
    }

    public String toString() {
        return "<InfinityMatcher>";
    }
}
