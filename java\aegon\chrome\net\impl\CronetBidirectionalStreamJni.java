package aegon.chrome.net.impl;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.net.impl.CronetBidirectionalStream;
import java.nio.ByteBuffer;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class CronetBidirectionalStreamJni implements CronetBidirectionalStream.Natives {
    public static final JniStaticTestMocker<CronetBidirectionalStream.Natives> TEST_HOOKS = new JniStaticTestMocker<CronetBidirectionalStream.Natives>() { // from class: aegon.chrome.net.impl.CronetBidirectionalStreamJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(CronetBidirectionalStream.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                CronetBidirectionalStream.Natives unused = CronetBidirectionalStreamJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static CronetBidirectionalStream.Natives testInstance;

    CronetBidirectionalStreamJni() {
    }

    @Override // aegon.chrome.net.impl.CronetBidirectionalStream.Natives
    public long createBidirectionalStream(CronetBidirectionalStream cronetBidirectionalStream, long j, boolean z, boolean z2, boolean z3, int i, boolean z4, int i2) {
        return GEN_JNI.m60x6f1f1d3f(cronetBidirectionalStream, j, z, z2, z3, i, z4, i2);
    }

    @Override // aegon.chrome.net.impl.CronetBidirectionalStream.Natives
    public int start(long j, CronetBidirectionalStream cronetBidirectionalStream, String str, int i, String str2, String[] strArr, boolean z) {
        return GEN_JNI.org_chromium_net_impl_CronetBidirectionalStream_start(j, cronetBidirectionalStream, str, i, str2, strArr, z);
    }

    @Override // aegon.chrome.net.impl.CronetBidirectionalStream.Natives
    public void sendRequestHeaders(long j, CronetBidirectionalStream cronetBidirectionalStream) {
        GEN_JNI.m61x4e32b167(j, cronetBidirectionalStream);
    }

    @Override // aegon.chrome.net.impl.CronetBidirectionalStream.Natives
    public boolean readData(long j, CronetBidirectionalStream cronetBidirectionalStream, ByteBuffer byteBuffer, int i, int i2) {
        return GEN_JNI.org_chromium_net_impl_CronetBidirectionalStream_readData(j, cronetBidirectionalStream, byteBuffer, i, i2);
    }

    @Override // aegon.chrome.net.impl.CronetBidirectionalStream.Natives
    public boolean writevData(long j, CronetBidirectionalStream cronetBidirectionalStream, ByteBuffer[] byteBufferArr, int[] iArr, int[] iArr2, boolean z) {
        return GEN_JNI.org_chromium_net_impl_CronetBidirectionalStream_writevData(j, cronetBidirectionalStream, byteBufferArr, iArr, iArr2, z);
    }

    @Override // aegon.chrome.net.impl.CronetBidirectionalStream.Natives
    public void destroy(long j, CronetBidirectionalStream cronetBidirectionalStream, boolean z) {
        GEN_JNI.org_chromium_net_impl_CronetBidirectionalStream_destroy(j, cronetBidirectionalStream, z);
    }

    public static CronetBidirectionalStream.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            CronetBidirectionalStream.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.net.impl.CronetBidirectionalStream.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new CronetBidirectionalStreamJni();
    }
}
