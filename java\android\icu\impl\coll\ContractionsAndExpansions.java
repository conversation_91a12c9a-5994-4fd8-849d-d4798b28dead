package android.icu.impl.coll;

import android.icu.impl.Trie2;
import android.icu.text.UnicodeSet;
import android.icu.util.CharsTrie;
import java.util.Iterator;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class ContractionsAndExpansions {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private boolean addPrefixes;
    private UnicodeSet contractions;
    private CollationData data;
    private UnicodeSet expansions;
    private UnicodeSet ranges;
    private CESink sink;
    private String suffix;
    private int checkTailored = 0;
    private UnicodeSet tailored = new UnicodeSet();
    private StringBuilder unreversedPrefix = new StringBuilder();
    private long[] ces = new long[31];

    public interface CESink {
        void handleCE(long j);

        void handleExpansion(long[] jArr, int i, int i2);
    }

    public ContractionsAndExpansions(UnicodeSet con, UnicodeSet exp, CESink s, boolean prefixes) {
        this.contractions = con;
        this.expansions = exp;
        this.sink = s;
        this.addPrefixes = prefixes;
    }

    public void forData(CollationData d2) {
        if (d2.base != null) {
            this.checkTailored = -1;
        }
        this.data = d2;
        Iterator<Trie2.Range> trieIterator = d2.trie.iterator2();
        while (trieIterator.hasNext()) {
            Trie2.Range range = trieIterator.mo35924next();
            if (range.leadSurrogate) {
                break;
            } else {
                enumCnERange(range.startCodePoint, range.endCodePoint, range.value, this);
            }
        }
        if (d2.base == null) {
            return;
        }
        this.tailored.freeze();
        this.checkTailored = 1;
        CollationData collationData = d2.base;
        this.data = collationData;
        Iterator<Trie2.Range> trieIterator2 = collationData.trie.iterator2();
        while (trieIterator2.hasNext()) {
            Trie2.Range range2 = trieIterator2.mo35924next();
            if (!range2.leadSurrogate) {
                enumCnERange(range2.startCodePoint, range2.endCodePoint, range2.value, this);
            } else {
                return;
            }
        }
    }

    private void enumCnERange(int start, int end, int ce32, ContractionsAndExpansions cne) {
        int i = cne.checkTailored;
        if (i != 0) {
            if (i < 0) {
                if (ce32 == 192) {
                    return;
                } else {
                    cne.tailored.add(start, end);
                }
            } else if (start == end) {
                if (cne.tailored.contains(start)) {
                    return;
                }
            } else if (cne.tailored.containsSome(start, end)) {
                if (cne.ranges == null) {
                    cne.ranges = new UnicodeSet();
                }
                cne.ranges.set(start, end).removeAll(cne.tailored);
                int count = cne.ranges.getRangeCount();
                for (int i2 = 0; i2 < count; i2++) {
                    cne.handleCE32(cne.ranges.getRangeStart(i2), cne.ranges.getRangeEnd(i2), ce32);
                }
            }
        }
        cne.handleCE32(start, end, ce32);
    }

    public void forCodePoint(CollationData d2, int c2) {
        int ce32 = d2.getCE32(c2);
        if (ce32 == 192) {
            d2 = d2.base;
            ce32 = d2.getCE32(c2);
        }
        this.data = d2;
        handleCE32(c2, c2, ce32);
    }

    private void handleCE32(int start, int end, int ce32) {
        while ((ce32 & 255) >= 192) {
            switch (Collation.tagFromCE32(ce32)) {
                case 0:
                    return;
                case 1:
                    CESink cESink = this.sink;
                    if (cESink != null) {
                        cESink.handleCE(Collation.ceFromLongPrimaryCE32(ce32));
                        return;
                    }
                    return;
                case 2:
                    CESink cESink2 = this.sink;
                    if (cESink2 != null) {
                        cESink2.handleCE(Collation.ceFromLongSecondaryCE32(ce32));
                        return;
                    }
                    return;
                case 3:
                case 7:
                case 13:
                    throw new AssertionError((Object) String.format("Unexpected CE32 tag type %d for ce32=0x%08x", Integer.valueOf(Collation.tagFromCE32(ce32)), Integer.valueOf(ce32)));
                case 4:
                    if (this.sink != null) {
                        this.ces[0] = Collation.latinCE0FromCE32(ce32);
                        this.ces[1] = Collation.latinCE1FromCE32(ce32);
                        this.sink.handleExpansion(this.ces, 0, 2);
                    }
                    if (this.unreversedPrefix.length() == 0) {
                        addExpansions(start, end);
                        return;
                    }
                    return;
                case 5:
                    if (this.sink != null) {
                        int idx = Collation.indexFromCE32(ce32);
                        int length = Collation.lengthFromCE32(ce32);
                        for (int i = 0; i < length; i++) {
                            this.ces[i] = Collation.ceFromCE32(this.data.ce32s[idx + i]);
                        }
                        this.sink.handleExpansion(this.ces, 0, length);
                    }
                    if (this.unreversedPrefix.length() == 0) {
                        addExpansions(start, end);
                        return;
                    }
                    return;
                case 6:
                    if (this.sink != null) {
                        int idx2 = Collation.indexFromCE32(ce32);
                        this.sink.handleExpansion(this.data.ces, idx2, Collation.lengthFromCE32(ce32));
                    }
                    if (this.unreversedPrefix.length() == 0) {
                        addExpansions(start, end);
                        return;
                    }
                    return;
                case 8:
                    handlePrefixes(start, end, ce32);
                    return;
                case 9:
                    handleContractions(start, end, ce32);
                    return;
                case 10:
                    ce32 = this.data.ce32s[Collation.indexFromCE32(ce32)];
                    break;
                case 11:
                    ce32 = this.data.ce32s[0];
                    break;
                case 12:
                    if (this.sink != null) {
                        UTF16CollationIterator iter = new UTF16CollationIterator(this.data);
                        StringBuilder hangul = new StringBuilder(1);
                        for (int c2 = start; c2 <= end; c2++) {
                            hangul.setLength(0);
                            hangul.appendCodePoint(c2);
                            iter.setText(false, hangul, 0);
                            this.sink.handleExpansion(iter.getCEs(), 0, iter.fetchCEs() - 1);
                        }
                    }
                    if (this.unreversedPrefix.length() == 0) {
                        addExpansions(start, end);
                        return;
                    }
                    return;
                case 14:
                    return;
                case 15:
                    return;
            }
        }
        CESink cESink3 = this.sink;
        if (cESink3 != null) {
            cESink3.handleCE(Collation.ceFromSimpleCE32(ce32));
        }
    }

    private void handlePrefixes(int start, int end, int ce32) {
        int index = Collation.indexFromCE32(ce32);
        int ce322 = this.data.getCE32FromContexts(index);
        handleCE32(start, end, ce322);
        if (!this.addPrefixes) {
            return;
        }
        Iterator<CharsTrie.Entry> itIterator2 = new CharsTrie(this.data.contexts, index + 2).iterator2();
        while (itIterator2.hasNext()) {
            CharsTrie.Entry e = itIterator2.mo35924next();
            setPrefix(e.chars);
            addStrings(start, end, this.contractions);
            addStrings(start, end, this.expansions);
            handleCE32(start, end, e.value);
        }
        resetPrefix();
    }

    void handleContractions(int start, int end, int ce32) {
        int index = Collation.indexFromCE32(ce32);
        if ((ce32 & 256) == 0) {
            handleCE32(start, end, this.data.getCE32FromContexts(index));
        }
        Iterator<CharsTrie.Entry> itIterator2 = new CharsTrie(this.data.contexts, index + 2).iterator2();
        while (itIterator2.hasNext()) {
            CharsTrie.Entry e = itIterator2.mo35924next();
            this.suffix = e.chars.toString();
            addStrings(start, end, this.contractions);
            if (this.unreversedPrefix.length() != 0) {
                addStrings(start, end, this.expansions);
            }
            handleCE32(start, end, e.value);
        }
        this.suffix = null;
    }

    void addExpansions(int start, int end) {
        if (this.unreversedPrefix.length() == 0 && this.suffix == null) {
            UnicodeSet unicodeSet = this.expansions;
            if (unicodeSet != null) {
                unicodeSet.add(start, end);
                return;
            }
            return;
        }
        addStrings(start, end, this.expansions);
    }

    void addStrings(int start, int end, UnicodeSet set) {
        if (set == null) {
            return;
        }
        StringBuilder s = new StringBuilder(this.unreversedPrefix);
        do {
            s.appendCodePoint(start);
            String str = this.suffix;
            if (str != null) {
                s.append(str);
            }
            set.add(s);
            s.setLength(this.unreversedPrefix.length());
            start++;
        } while (start <= end);
    }

    private void setPrefix(CharSequence pfx) {
        this.unreversedPrefix.setLength(0);
        StringBuilder sb = this.unreversedPrefix;
        sb.append(pfx);
        sb.reverse();
    }

    private void resetPrefix() {
        this.unreversedPrefix.setLength(0);
    }
}
