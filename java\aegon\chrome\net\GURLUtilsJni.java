package aegon.chrome.net;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.net.GURLUtils;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class GURLUtilsJni implements GURLUtils.Natives {
    public static final JniStaticTestMocker<GURLUtils.Natives> TEST_HOOKS = new JniStaticTestMocker<GURLUtils.Natives>() { // from class: aegon.chrome.net.GURLUtilsJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(GURLUtils.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                GURLUtils.Natives unused = GURLUtilsJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static GURLUtils.Natives testInstance;

    @Override // aegon.chrome.net.GURLUtils.Natives
    public String getOrigin(String str) {
        return GEN_JNI.org_chromium_net_GURLUtils_getOrigin(str);
    }

    public static GURLUtils.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            GURLUtils.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.net.GURLUtils.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new GURLUtilsJni();
    }
}
