package aegon.chrome.base;

import aegon.chrome.base.Callback;

/* compiled from: lambda */
/* renamed from: aegon.chrome.base.-$$Lambda$AOL8pt032rTX4jFbGE2UEfshjrw, reason: invalid class name */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final /* synthetic */ class $$Lambda$AOL8pt032rTX4jFbGE2UEfshjrw implements Callback {
    private final /* synthetic */ Promise f$0;

    @Override // aegon.chrome.base.Callback
    public /* synthetic */ Runnable bind(T t) {
        return Callback.CC.$default$bind(this, t);
    }

    @Override // aegon.chrome.base.Callback
    public final void onResult(Object obj) {
        this.f$0.reject((Exception) obj);
    }
}
