package aegon.chrome.base;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class BaseSwitches {
    public static final String DEFAULT_COUNTRY_CODE_AT_INSTALL = "default-country-code";
    public static final String DISABLE_BEST_EFFORT_TASKS = "disable-best-effort-tasks";
    public static final String DISABLE_BREAKPAD = "disable-breakpad";
    public static final String DISABLE_DEV_SHM_USAGE = "disable-dev-shm-usage";
    public static final String DISABLE_FEATURES = "disable-features";
    public static final String DISABLE_HIGH_RES_TIMER = "disable-highres-timer";
    public static final String DISABLE_LOW_END_DEVICE_MODE = "disable-low-end-device-mode";
    public static final String DISABLE_USB_KEYBOARD_DETECT = "disable-usb-keyboard-detect";
    public static final String ENABLE_BACKGROUND_THREAD_POOL = "enable-background-thread-pool";
    public static final String ENABLE_CRASH_REPORTER = "enable-crash-reporter";
    public static final String ENABLE_CRASH_REPORTER_FOR_TESTING = "enable-crash-reporter-for-testing";
    public static final String ENABLE_FEATURES = "enable-features";
    public static final String ENABLE_IDLE_TRACING = "enable-idle-tracing";
    public static final String ENABLE_LOW_END_DEVICE_MODE = "enable-low-end-device-mode";
    public static final String ENABLE_REACHED_CODE_PROFILER = "enable-reached-code-profiler";
    public static final String ENABLE_THREAD_INSTRUCTION_COUNT = "enable-thread-instruction-count";
    public static final String FORCE_FIELD_TRIALS = "force-fieldtrials";
    public static final String FORCE_FIELD_TRIAL_PARAMS = "force-fieldtrial-params";
    public static final String FULL_MEMORY_CRASH_REPORT = "full-memory-crash-report";
    public static final String LOG_BEST_EFFORT_TASKS = "log-best-effort-tasks";
    public static final String NO_ERROR_DIALOGS = "noerrdialogs";
    public static final String PROFILING_AT_START = "profiling-at-start";
    public static final String PROFILING_FILE = "profiling-file";
    public static final String PROFILING_FLUSH = "profiling-flush";
    public static final String REACHED_CODE_SAMPLING_INTERVAL_US = "reached-code-sampling-interval-us";
    public static final String RENDERER_WAIT_FOR_JAVA_DEBUGGER = "renderer-wait-for-java-debugger";
    public static final String SCHEDULER_BOOST_URGENT = "scheduler-boost-urgent";
    public static final String TEST_CHILD_PROCESS = "test-child-process";
    public static final String TEST_DO_NOT_INITIALIZE_ICU = "test-do-not-initialize-icu";
    public static final String TRACE_TO_FILE = "trace-to-file";
    public static final String TRACE_TO_FILE_NAME = "trace-to-file-name";

    /* renamed from: V */
    public static final String f31V = "v";
    public static final String V_MODULE = "vmodule";
    public static final String WAIT_FOR_DEBUGGER = "wait-for-debugger";

    private BaseSwitches() {
    }
}
