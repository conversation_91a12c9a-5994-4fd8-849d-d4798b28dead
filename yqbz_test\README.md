# 元气桌面壁纸广告屏蔽模块

这是一个基于Xposed框架的广告屏蔽模块，专门用于屏蔽元气桌面壁纸应用中的所有广告。

## 功能特性

### 支持的广告类型
- ✅ 开屏广告（启动页广告）
- ✅ Banner横幅广告
- ✅ 插屏广告
- ✅ 激励视频广告
- ✅ 原生广告
- ✅ 全屏视频广告

### 支持的广告SDK
- ✅ 快手广告SDK (KwAd)
- ✅ 腾讯广告SDK (GDT)
- ✅ 字节跳动广告SDK (Gromore/Pangle)
- ✅ 其他第三方广告SDK

### 屏蔽策略
- 🚫 阻止广告SDK初始化
- 🚫 阻止广告请求和加载
- 🚫 阻止广告View显示
- 🚫 阻止广告Activity启动
- 🚫 过滤广告相关网络请求
- 🚫 隐藏广告容器

## 安装要求

1. **Root权限**: 设备必须已获得Root权限
2. **Xposed框架**: 需要安装Xposed框架或LSPosed
3. **目标应用**: 元气桌面壁纸 (com.cmcm.cfwallpaper)

## 安装步骤

### 1. 编译模块
```bash
cd yqbz_test
./gradlew assembleDebug
```

### 2. 安装APK
```bash
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 3. 激活模块
1. 打开Xposed Installer或LSPosed管理器
2. 进入"模块"页面
3. 勾选"元气桌面壁纸广告屏蔽模块"
4. 重启设备

### 4. 配置作用域（LSPosed用户）
1. 打开LSPosed管理器
2. 进入模块设置
3. 选择"元气桌面壁纸广告屏蔽模块"
4. 在作用域中添加"元气桌面壁纸"应用
5. 重启目标应用

## 使用方法

1. 确保模块已正确安装并激活
2. 重启设备（Xposed）或重启目标应用（LSPosed）
3. 打开元气桌面壁纸应用
4. 享受无广告体验

## 验证效果

模块激活后，可以通过以下方式验证是否生效：

1. **查看Xposed日志**: 在Xposed日志中搜索"YQBZAdBlocker"
2. **观察应用行为**: 启动应用时不再显示开屏广告
3. **检查网络请求**: 广告相关的网络请求被阻止

## 技术原理

### Hook策略
1. **SDK级别拦截**: 直接Hook广告SDK的核心方法
2. **View级别过滤**: 阻止广告View的添加和显示
3. **网络级别阻断**: 拦截广告相关的网络请求
4. **Activity级别控制**: 阻止广告Activity的启动

### 主要Hook点
```java
// 快手广告SDK
com.kwad.components.ad.splashscreen.a.loadSplashScreenAd()
com.kwad.components.ad.reward.g.a.loadRewardVideoAd()
com.kwad.components.ad.c.a.loadBannerAd()

// 腾讯广告SDK
com.qq.e.comm.plugin.splash.ANSplashAdViewAdapter.fetchAdOnly()
com.qq.e.comm.plugin.banner2.ANUnifiedBannerAdapter.onADEvent()

// 字节跳动广告SDK
com.alex.AlexGromoreSplashAdapter.show()
com.alex.AlexGromoreBannerAdapter.loadCustomNetworkAd()
```

## 文件结构

```
yqbz_test/
├── app/
│   ├── src/main/
│   │   ├── java/com/example/yqbz_test/
│   │   │   ├── AdBlockerModule.java      # 主Hook模块
│   │   │   ├── AdvancedAdBlocker.java    # 高级广告屏蔽
│   │   │   └── MainActivity.java         # 主界面
│   │   ├── assets/
│   │   │   └── xposed_init              # Xposed入口配置
│   │   └── AndroidManifest.xml          # 应用清单
│   ├── libs/
│   │   └── XposedBridgeApi-54.jar       # Xposed API
│   └── build.gradle.kts                 # 构建配置
└── README.md                            # 说明文档
```

## 注意事项

1. **兼容性**: 模块针对特定版本的元气桌面壁纸开发，可能需要根据应用更新调整
2. **稳定性**: 过度Hook可能影响应用稳定性，建议适度使用
3. **法律风险**: 请确保使用符合当地法律法规
4. **更新维护**: 目标应用更新后可能需要更新模块

## 故障排除

### 模块不生效
1. 检查Xposed框架是否正常工作
2. 确认模块已在Xposed中激活
3. 检查目标应用包名是否正确
4. 查看Xposed日志是否有错误信息

### 应用崩溃
1. 检查Hook的类和方法是否存在
2. 适当减少Hook点数量
3. 查看崩溃日志定位问题

### 部分广告仍然显示
1. 可能是新的广告SDK或方法
2. 需要分析应用更新内容
3. 添加新的Hook点

## 开发说明

### 添加新的Hook点
1. 分析目标应用的广告相关代码
2. 在对应的Hook方法中添加新的类和方法
3. 测试验证效果

### 调试技巧
1. 使用XposedBridge.log()输出调试信息
2. 通过Xposed日志查看Hook执行情况
3. 使用try-catch包装Hook代码避免崩溃

## 免责声明

本模块仅供学习和研究使用，使用者需自行承担使用风险。开发者不对因使用本模块造成的任何损失负责。

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
