package com.example.yqbz_test;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

/**
 * 高级广告屏蔽器
 * 提供更精细的广告检测和屏蔽功能
 */
public class AdvancedAdBlocker {
    
    private static final String TAG = "YQBZAdvancedAdBlocker";
    
    // 广告相关的包名关键词
    private static final List<String> AD_PACKAGE_KEYWORDS = Arrays.asList(
        "kwad", "qq.e", "alex", "gromore", "bytedance", "tencent", "anythink",
        "admob", "facebook", "unity", "vungle", "ironsource", "applovin",
        "mintegral", "sigmob", "baidu", "xiaomi", "oppo", "vivo"
    );
    
    // 广告相关的类名关键词
    private static final List<String> AD_CLASS_KEYWORDS = Arrays.asList(
        "ad", "Ad", "AD", "splash", "Splash", "banner", "Banner",
        "reward", "Reward", "interstitial", "Interstitial", "native", "Native",
        "SplashAd", "BannerAd", "RewardAd", "InterstitialAd", "NativeAd",
        "AdView", "AdContainer", "AdLayout", "AdFrame", "AdWebView"
    );
    
    // 广告相关的方法名关键词
    private static final List<String> AD_METHOD_KEYWORDS = Arrays.asList(
        "loadAd", "showAd", "displayAd", "fetchAd", "requestAd",
        "onAdLoaded", "onAdShown", "onAdClicked", "onAdClosed",
        "loadSplash", "showSplash", "loadBanner", "showBanner",
        "loadReward", "showReward", "loadInterstitial", "showInterstitial"
    );
    
    /**
     * 初始化高级广告屏蔽
     */
    public static void initAdvancedAdBlocking(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook Activity的onCreate方法，检测广告Activity
            hookActivityMethods(lpparam);
            
            // Hook View相关方法
            hookViewMethods(lpparam);
            
            // Hook WebView相关方法
            hookWebViewMethods(lpparam);
            
            // Hook网络请求方法
            hookNetworkMethods(lpparam);
            
            XposedBridge.log(TAG + ": 高级广告屏蔽初始化完成");
            
        } catch (Throwable t) {
            XposedBridge.log(TAG + ": 高级广告屏蔽初始化失败: " + t.getMessage());
        }
    }
    
    /**
     * Hook Activity相关方法
     */
    private static void hookActivityMethods(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            XposedHelpers.findAndHookMethod(
                Activity.class,
                "onCreate",
                android.os.Bundle.class,
                new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        Activity activity = (Activity) param.thisObject;
                        String className = activity.getClass().getName();
                        
                        if (isAdActivity(className)) {
                            XposedBridge.log(TAG + ": 阻止广告Activity启动: " + className);
                            activity.finish();
                            param.setResult(null);
                        }
                    }
                }
            );
            
            XposedHelpers.findAndHookMethod(
                Activity.class,
                "setContentView",
                View.class,
                new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        View view = (View) param.args[0];
                        if (view != null && isAdView(view.getClass().getName())) {
                            XposedBridge.log(TAG + ": 阻止设置广告View: " + view.getClass().getName());
                            param.setResult(null);
                        }
                    }
                }
            );
            
        } catch (Throwable t) {
            XposedBridge.log(TAG + ": Hook Activity方法失败: " + t.getMessage());
        }
    }
    
    /**
     * Hook View相关方法
     */
    private static void hookViewMethods(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook ViewGroup的addView方法
            XposedHelpers.findAndHookMethod(
                ViewGroup.class,
                "addView",
                View.class,
                new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        View view = (View) param.args[0];
                        if (view != null && isAdView(view.getClass().getName())) {
                            XposedBridge.log(TAG + ": 阻止添加广告View: " + view.getClass().getName());
                            param.setResult(null);
                        }
                    }
                }
            );
            
            // Hook View的setVisibility方法
            XposedHelpers.findAndHookMethod(
                View.class,
                "setVisibility",
                int.class,
                new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        View view = (View) param.thisObject;
                        int visibility = (Integer) param.args[0];
                        
                        if (visibility == View.VISIBLE && isAdView(view.getClass().getName())) {
                            XposedBridge.log(TAG + ": 阻止广告View显示: " + view.getClass().getName());
                            param.args[0] = View.GONE;
                        }
                    }
                }
            );
            
        } catch (Throwable t) {
            XposedBridge.log(TAG + ": Hook View方法失败: " + t.getMessage());
        }
    }
    
    /**
     * Hook WebView相关方法
     */
    private static void hookWebViewMethods(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook WebView的loadUrl方法
            XposedHelpers.findAndHookMethod(
                "android.webkit.WebView",
                lpparam.classLoader,
                "loadUrl",
                String.class,
                new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        String url = (String) param.args[0];
                        if (url != null && isAdUrl(url)) {
                            XposedBridge.log(TAG + ": 阻止加载广告URL: " + url);
                            param.setResult(null);
                        }
                    }
                }
            );
            
        } catch (Throwable t) {
            XposedBridge.log(TAG + ": Hook WebView方法失败: " + t.getMessage());
        }
    }
    
    /**
     * Hook网络请求方法
     */
    private static void hookNetworkMethods(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook OkHttp的网络请求
            XposedHelpers.findAndHookMethod(
                "okhttp3.RealCall",
                lpparam.classLoader,
                "execute",
                new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        Object request = XposedHelpers.getObjectField(param.thisObject, "originalRequest");
                        if (request != null) {
                            Object url = XposedHelpers.callMethod(request, "url");
                            if (url != null && isAdUrl(url.toString())) {
                                XposedBridge.log(TAG + ": 阻止广告网络请求: " + url.toString());
                                param.setResult(null);
                            }
                        }
                    }
                }
            );
            
        } catch (Throwable t) {
            XposedBridge.log(TAG + ": Hook网络方法失败: " + t.getMessage());
        }
    }
    
    /**
     * 判断是否为广告Activity
     */
    private static boolean isAdActivity(String className) {
        if (className == null) return false;
        
        for (String keyword : AD_CLASS_KEYWORDS) {
            if (className.toLowerCase().contains(keyword.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 判断是否为广告View
     */
    private static boolean isAdView(String className) {
        if (className == null) return false;
        
        for (String keyword : AD_CLASS_KEYWORDS) {
            if (className.toLowerCase().contains(keyword.toLowerCase())) {
                return true;
            }
        }
        
        for (String keyword : AD_PACKAGE_KEYWORDS) {
            if (className.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 判断是否为广告URL
     */
    private static boolean isAdUrl(String url) {
        if (url == null) return false;
        
        String[] adDomains = {
            "googleads", "googlesyndication", "doubleclick", "adsystem",
            "facebook.com/tr", "connect.facebook.net", "analytics.google.com",
            "google-analytics.com", "googletagmanager.com", "googletagservices.com",
            "bytedance.com", "pangle.io", "toutiao.com", "snssdk.com",
            "qq.com/gdt", "e.qq.com", "ugdtimg.com", "gdtimg.com",
            "kuaishou.com", "kwai.com", "ksapisrv.com", "kwimgs.com",
            "unity3d.com", "unityads.unity3d.com", "applvn.com", "applovin.com"
        };
        
        String lowerUrl = url.toLowerCase();
        for (String domain : adDomains) {
            if (lowerUrl.contains(domain)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 动态Hook所有包含广告关键词的方法
     */
    public static void hookAllAdMethods(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // 遍历所有已加载的类
            ClassLoader classLoader = lpparam.classLoader;
            
            // 这里可以添加更多动态检测逻辑
            XposedBridge.log(TAG + ": 动态广告方法Hook完成");
            
        } catch (Throwable t) {
            XposedBridge.log(TAG + ": 动态Hook失败: " + t.getMessage());
        }
    }
}
