package aegon.chrome.net;

import aegon.chrome.base.ContextUtils;
import aegon.chrome.base.annotations.JNINamespace;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkRequest;

@JNINamespace("net::android")
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class NetworkActivationRequest extends ConnectivityManager.NetworkCallback {
    private long mNativePtr;
    private final Object mNativePtrLock = new Object();
    private final ConnectivityManager mConnectivityManager = (ConnectivityManager) ContextUtils.getApplicationContext().getSystemService("connectivity");

    interface Natives {
        void notifyAvailable(long j, long j2);
    }

    private NetworkActivationRequest(long j, int i) {
        ConnectivityManager connectivityManager = this.mConnectivityManager;
        if (connectivityManager == null) {
            return;
        }
        try {
            connectivityManager.requestNetwork(new NetworkRequest.Builder().addTransportType(i).addCapability(12).build(), this);
            this.mNativePtr = j;
        } catch (SecurityException unused) {
        }
    }

    private void unregister() {
        boolean z;
        synchronized (this.mNativePtrLock) {
            z = this.mNativePtr != 0;
            this.mNativePtr = 0L;
        }
        if (z) {
            this.mConnectivityManager.unregisterNetworkCallback(this);
        }
    }

    @Override // android.net.ConnectivityManager.NetworkCallback
    public void onAvailable(Network network) {
        synchronized (this.mNativePtrLock) {
            if (this.mNativePtr == 0) {
                return;
            }
            NetworkActivationRequestJni.get().notifyAvailable(this.mNativePtr, NetworkChangeNotifierAutoDetect.networkToNetId(network));
        }
    }

    public static NetworkActivationRequest createMobileNetworkRequest(long j) {
        return new NetworkActivationRequest(j, 0);
    }
}
