package aegon.chrome.net.impl;

import java.nio.ByteBuffer;
import java.nio.channels.ReadableByteChannel;
import java.p654io.FileInputStream;
import java.p654io.IOException;
import java.p654io.InputStream;
import java.util.concurrent.atomic.AtomicBoolean;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
final class InputStreamChannel implements ReadableByteChannel {
    private static final int MAX_TMP_BUFFER_SIZE = 16384;
    private static final int MIN_TMP_BUFFER_SIZE = 4096;
    private final InputStream mInputStream;
    private final AtomicBoolean mIsOpen = new AtomicBoolean(true);

    private InputStreamChannel(InputStream inputStream) {
        this.mInputStream = inputStream;
    }

    static ReadableByteChannel wrap(InputStream inputStream) {
        if (inputStream instanceof FileInputStream) {
            return ((FileInputStream) inputStream).getChannel();
        }
        return new InputStreamChannel(inputStream);
    }

    @Override // java.nio.channels.ReadableByteChannel
    public final int read(ByteBuffer byteBuffer) throws IOException {
        if (byteBuffer.hasArray()) {
            int i = this.mInputStream.read(byteBuffer.array(), byteBuffer.arrayOffset() + byteBuffer.position(), byteBuffer.remaining());
            if (i <= 0) {
                return i;
            }
            byteBuffer.position(byteBuffer.position() + i);
            return i;
        }
        byte[] bArr = new byte[Math.min(16384, Math.min(Math.max(this.mInputStream.available(), 4096), byteBuffer.remaining()))];
        int i2 = this.mInputStream.read(bArr);
        if (i2 > 0) {
            byteBuffer.put(bArr, 0, i2);
        }
        return i2;
    }

    @Override // java.nio.channels.Channel
    public final boolean isOpen() {
        return this.mIsOpen.get();
    }

    @Override // java.nio.channels.Channel, java.p654io.Closeable, java.lang.AutoCloseable
    /* renamed from: close */
    public final void lambda$new$0() throws IOException {
        if (this.mIsOpen.compareAndSet(true, false)) {
            this.mInputStream.lambda$new$0();
        }
    }
}
