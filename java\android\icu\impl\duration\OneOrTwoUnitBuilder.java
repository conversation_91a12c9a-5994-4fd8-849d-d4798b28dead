package android.icu.impl.duration;

import android.icu.impl.duration.BasicPeriodBuilderFactory;

/* compiled from: BasicPeriodBuilderFactory.java */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
class OneOrTwoUnitBuilder extends PeriodBuilderImpl {
    OneOrTwoUnitBuilder(BasicPeriodBuilderFactory.Settings settings) {
        super(settings);
    }

    public static OneOrTwoUnitBuilder get(BasicPeriodBuilderFactory.Settings settings) {
        if (settings == null) {
            return null;
        }
        return new OneOrTwoUnitBuilder(settings);
    }

    @Override // android.icu.impl.duration.PeriodBuilderImpl
    protected PeriodBuilder withSettings(BasicPeriodBuilderFactory.Settings settingsToUse) {
        return get(settingsToUse);
    }

    @Override // android.icu.impl.duration.PeriodBuilderImpl
    protected Period handleCreate(long duration, long referenceDate, boolean inPast) {
        Period period = null;
        short uset = this.settings.effectiveSet();
        for (int i = 0; i < TimeUnit.units.length; i++) {
            if (((1 << i) & uset) != 0) {
                TimeUnit unit = TimeUnit.units[i];
                long unitDuration = approximateDurationOf(unit);
                if (duration >= unitDuration || period != null) {
                    double count = duration / unitDuration;
                    if (period != null) {
                        if (count >= 1.0d) {
                            return period.and((float) count, unit);
                        }
                        return period;
                    }
                    if (count >= 2.0d) {
                        Period period2 = Period.m89at((float) count, unit);
                        return period2;
                    }
                    period = Period.m89at(1.0f, unit).inPast(inPast);
                    duration -= unitDuration;
                }
            }
        }
        return period;
    }
}
