package aegon.chrome.base.natives;

import p001K.C0000S;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class GEN_JNI {
    public static final boolean REQUIRE_MOCK = false;
    public static final boolean TESTING_ENABLED = false;

    public static void org_chromium_base_ApplicationStatus_onApplicationStateChange(int i) {
        C0000S.MiAkQ_SU(i);
    }

    public static void org_chromium_base_CommandLine_init(String[] strArr) {
        C0000S.MDkrKi31(strArr);
    }

    public static boolean org_chromium_base_CommandLine_hasSwitch(String str) {
        return C0000S.MsCvypjU(str);
    }

    public static String org_chromium_base_CommandLine_getSwitchValue(String str) {
        return C0000S.MZJ2lrZY(str);
    }

    public static String[] org_chromium_base_CommandLine_getSwitchesFlattened() {
        return C0000S.MHzche6O();
    }

    public static void org_chromium_base_CommandLine_appendSwitch(String str) {
        C0000S.M5K_ewhl(str);
    }

    public static void org_chromium_base_CommandLine_appendSwitchWithValue(String str, String str2) {
        C0000S.MUoYiNbY(str, str2);
    }

    public static void org_chromium_base_CommandLine_appendSwitchesAndArguments(String[] strArr) {
        C0000S.MPquHBNa(strArr);
    }

    public static void org_chromium_base_CommandLine_removeSwitch(String str) {
        C0000S.M1cMYXGO(str);
    }

    public static int org_chromium_base_CpuFeatures_getCoreCount() {
        return C0000S.MOiBJ1qS();
    }

    public static long org_chromium_base_CpuFeatures_getCpuFeatures() {
        return C0000S.ML0T8q1U();
    }

    public static void org_chromium_base_EarlyTraceEvent_recordEarlyBeginEvent(String str, long j, int i, long j2) {
        C0000S.MrWG2uUW(str, j, i, j2);
    }

    public static void org_chromium_base_EarlyTraceEvent_recordEarlyEndEvent(String str, long j, int i, long j2) {
        C0000S.MmyrhqXB(str, j, i, j2);
    }

    public static void org_chromium_base_EarlyTraceEvent_recordEarlyToplevelBeginEvent(String str, long j, int i, long j2) {
        C0000S.M7UXCmoq(str, j, i, j2);
    }

    public static void org_chromium_base_EarlyTraceEvent_recordEarlyToplevelEndEvent(String str, long j, int i, long j2) {
        C0000S.MRlw2LEn(str, j, i, j2);
    }

    public static void org_chromium_base_EarlyTraceEvent_recordEarlyAsyncBeginEvent(String str, long j, long j2) {
        C0000S.M_Gv8TwM(str, j, j2);
    }

    public static void org_chromium_base_EarlyTraceEvent_recordEarlyAsyncEndEvent(String str, long j, long j2) {
        C0000S.MrKsqeCD(str, j, j2);
    }

    public static boolean org_chromium_base_FeatureList_isInitialized() {
        return C0000S.MFTJCvBh();
    }

    public static boolean org_chromium_base_Features_isEnabled(long j) {
        return C0000S.MRiRQ_Ey(j);
    }

    public static boolean org_chromium_base_Features_getFieldTrialParamByFeatureAsBoolean(long j, String str, boolean z) {
        return C0000S.M8R55Xut(j, str, z);
    }

    public static String org_chromium_base_FieldTrialList_findFullName(String str) {
        return C0000S.MdC43qwX(str);
    }

    public static boolean org_chromium_base_FieldTrialList_trialExists(String str) {
        return C0000S.Mmqqda9c(str);
    }

    public static String org_chromium_base_FieldTrialList_getVariationParameter(String str, String str2) {
        return C0000S.MZWMOP4I(str, str2);
    }

    public static void org_chromium_base_FieldTrialList_logActiveTrials() {
        C0000S.MHz6Fn06();
    }

    public static boolean org_chromium_base_FieldTrialList_createFieldTrial(String str, String str2) {
        return C0000S.MGqzwlIM(str, str2);
    }

    public static boolean org_chromium_base_ImportantFileWriterAndroid_writeFileAtomically(String str, byte[] bArr) {
        return C0000S.MsOKBrZ5(str, bArr);
    }

    public static void org_chromium_base_JavaExceptionReporter_reportJavaException(boolean z, Throwable th) {
        C0000S.MLlibBXh(z, th);
    }

    public static void org_chromium_base_JavaExceptionReporter_reportJavaStackTrace(String str) {
        C0000S.MmS4zlEt(str);
    }

    public static void org_chromium_base_JavaHandlerThread_initializeThread(long j, long j2) {
        C0000S.MJcct7gJ(j, j2);
    }

    public static void org_chromium_base_JavaHandlerThread_onLooperStopped(long j) {
        C0000S.MYwg$x8E(j);
    }

    public static void org_chromium_base_MemoryPressureListener_onMemoryPressure(int i) {
        C0000S.MZJzyjAa(i);
    }

    public static void org_chromium_base_PathService_override(int i, String str) {
        C0000S.M6H_IiaF(i, str);
    }

    public static void org_chromium_base_PowerMonitor_onBatteryChargingChanged() {
        C0000S.MCImhGql();
    }

    public static long org_chromium_base_TimeUtils_getTimeTicksNowUs() {
        return C0000S.MklbOJun();
    }

    public static void org_chromium_base_TraceEvent_registerEnabledObserver() {
        C0000S.MFFzPOVw();
    }

    public static void org_chromium_base_TraceEvent_startATrace(String str) {
        C0000S.MRN$Vid3(str);
    }

    public static void org_chromium_base_TraceEvent_stopATrace() {
        C0000S.MOgCa3d$();
    }

    public static void org_chromium_base_TraceEvent_setupATraceStartupTrace(String str) {
        C0000S.MlFM5bdC(str);
    }

    public static void org_chromium_base_TraceEvent_instant(String str, String str2) {
        C0000S.ML40H8ed(str, str2);
    }

    public static void org_chromium_base_TraceEvent_begin(String str, String str2) {
        C0000S.M9XfPu17(str, str2);
    }

    public static void org_chromium_base_TraceEvent_end(String str, String str2) {
        C0000S.Mw73xTww(str, str2);
    }

    public static void org_chromium_base_TraceEvent_beginToplevel(String str) {
        C0000S.M_y76mct(str);
    }

    public static void org_chromium_base_TraceEvent_endToplevel(String str) {
        C0000S.MLJecZJ9(str);
    }

    public static void org_chromium_base_TraceEvent_startAsync(String str, long j) {
        C0000S.MHopMqLX(str, j);
    }

    public static void org_chromium_base_TraceEvent_finishAsync(String str, long j) {
        C0000S.MffNhCLU(str, j);
    }

    /* renamed from: org_chromium_base_jank_1tracker_JankMetricUMARecorder_recordJankMetrics */
    public static void m47x83a5c755(String str, long[] jArr, long[] jArr2, long[] jArr3, int i) {
        C0000S.MJ46uzUz(str, jArr, jArr2, jArr3, i);
    }

    /* renamed from: org_chromium_base_metrics_NativeUmaRecorder_recordBooleanHistogram */
    public static long m48xb68b8438(String str, long j, boolean z) {
        return C0000S.MtKTTHie(str, j, z);
    }

    /* renamed from: org_chromium_base_metrics_NativeUmaRecorder_recordExponentialHistogram */
    public static long m49x315909bb(String str, long j, int i, int i2, int i3, int i4) {
        return C0000S.MILRV9Ch(str, j, i, i2, i3, i4);
    }

    /* renamed from: org_chromium_base_metrics_NativeUmaRecorder_recordLinearHistogram */
    public static long m50x61f57623(String str, long j, int i, int i2, int i3, int i4) {
        return C0000S.M$oMD214(str, j, i, i2, i3, i4);
    }

    /* renamed from: org_chromium_base_metrics_NativeUmaRecorder_recordSparseHistogram */
    public static long m51x5bbdc588(String str, long j, int i) {
        return C0000S.Mk1ai9mx(str, j, i);
    }

    public static void org_chromium_base_metrics_NativeUmaRecorder_recordUserAction(String str, long j) {
        C0000S.MTDsfZGe(str, j);
    }

    /* renamed from: org_chromium_base_metrics_RecordHistogram_getHistogramValueCountForTesting */
    public static int m54x9f662db(String str, int i) {
        return C0000S.M1gJHszj(str, i);
    }

    /* renamed from: org_chromium_base_metrics_RecordHistogram_getHistogramTotalCountForTesting */
    public static int m53x7c4ad8a8(String str) {
        return C0000S.M4mrObfZ(str);
    }

    /* renamed from: org_chromium_base_metrics_RecordHistogram_forgetHistogramForTesting */
    public static void m52xa86ffe16(String str) {
        C0000S.MxVTgTny(str);
    }

    /* renamed from: org_chromium_base_metrics_RecordUserAction_addActionCallbackForTesting */
    public static long m55x3f62a0b2(Object obj) {
        return C0000S.MH0bOwlk(obj);
    }

    /* renamed from: org_chromium_base_metrics_RecordUserAction_removeActionCallbackForTesting */
    public static void m56x3ebb9757(long j) {
        C0000S.MJl0LdjQ(j);
    }

    public static String org_chromium_base_metrics_StatisticsRecorderAndroid_toJson(int i) {
        return C0000S.MvO$oy3r(i);
    }

    public static void org_chromium_base_task_PostTask_postDelayedTask(int i, boolean z, boolean z2, byte b2, byte[] bArr, Object obj, long j, String str) {
        C0000S.MTILOhAQ(i, z, z2, b2, bArr, obj, j, str);
    }

    public static long org_chromium_base_task_TaskRunnerImpl_init(int i, int i2, boolean z, boolean z2, byte b2, byte[] bArr) {
        return C0000S.M5_IQXaH(i, i2, z, z2, b2, bArr);
    }

    public static void org_chromium_base_task_TaskRunnerImpl_destroy(long j) {
        C0000S.MERCiIV8(j);
    }

    public static void org_chromium_base_task_TaskRunnerImpl_postDelayedTask(long j, Object obj, long j2, String str) {
        C0000S.MGnQU$47(j, obj, j2, str);
    }

    public static boolean org_chromium_base_task_TaskRunnerImpl_belongsToCurrentThread(long j) {
        return C0000S.MdFi6sVQ(j);
    }

    public static String org_chromium_net_GURLUtils_getOrigin(String str) {
        return C0000S.MpCt7siL(str);
    }

    public static boolean org_chromium_net_HttpUtil_isAllowedHeader(String str, String str2) {
        return C0000S.MorcXgQd(str, str2);
    }

    public static void org_chromium_net_NetworkActivationRequest_notifyAvailable(long j, long j2) {
        C0000S.MJRUHS0T(j, j2);
    }

    /* renamed from: org_chromium_net_NetworkChangeNotifier_notifyConnectionTypeChanged */
    public static void m57xcc4996a8(long j, Object obj, int i, long j2) {
        C0000S.MbPIImnU(j, obj, i, j2);
    }

    public static void org_chromium_net_NetworkChangeNotifier_notifyMaxBandwidthChanged(long j, Object obj, int i) {
        C0000S.Mt26m31j(j, obj, i);
    }

    public static void org_chromium_net_NetworkChangeNotifier_notifyOfNetworkConnect(long j, Object obj, long j2, int i) {
        C0000S.MBT1i5cd(j, obj, j2, i);
    }

    /* renamed from: org_chromium_net_NetworkChangeNotifier_notifyOfNetworkSoonToDisconnect */
    public static void m58x39dfa115(long j, Object obj, long j2) {
        C0000S.MiJIMrTb(j, obj, j2);
    }

    public static void org_chromium_net_NetworkChangeNotifier_notifyOfNetworkDisconnect(long j, Object obj, long j2) {
        C0000S.MDpuHJTB(j, obj, j2);
    }

    /* renamed from: org_chromium_net_NetworkChangeNotifier_notifyPurgeActiveNetworkList */
    public static void m59x8a4d36cf(long j, Object obj, long[] jArr) {
        C0000S.MpF$179U(j, obj, jArr);
    }

    public static void org_chromium_net_ProxyChangeListener_proxySettingsChangedTo(long j, Object obj, String str, int i, String str2, String[] strArr) {
        C0000S.MyoFZt$2(j, obj, str, i, str2, strArr);
    }

    public static void org_chromium_net_ProxyChangeListener_proxySettingsChanged(long j, Object obj) {
        C0000S.MCIk73GZ(j, obj);
    }

    public static void org_chromium_net_X509Util_notifyKeyChainChanged() {
        C0000S.MGVAvp19();
    }

    /* renamed from: org_chromium_net_impl_CronetBidirectionalStream_createBidirectionalStream */
    public static long m60x6f1f1d3f(Object obj, long j, boolean z, boolean z2, boolean z3, int i, boolean z4, int i2) {
        return C0000S.MqTDYvZd(obj, j, z, z2, z3, i, z4, i2);
    }

    public static int org_chromium_net_impl_CronetBidirectionalStream_start(long j, Object obj, String str, int i, String str2, String[] strArr, boolean z) {
        return C0000S.McDUim_I(j, obj, str, i, str2, strArr, z);
    }

    /* renamed from: org_chromium_net_impl_CronetBidirectionalStream_sendRequestHeaders */
    public static void m61x4e32b167(long j, Object obj) {
        C0000S.MGLIR7Sc(j, obj);
    }

    public static boolean org_chromium_net_impl_CronetBidirectionalStream_readData(long j, Object obj, Object obj2, int i, int i2) {
        return C0000S.Md_rPmgC(j, obj, obj2, i, i2);
    }

    public static boolean org_chromium_net_impl_CronetBidirectionalStream_writevData(long j, Object obj, Object[] objArr, int[] iArr, int[] iArr2, boolean z) {
        return C0000S.MwJCBTMQ(j, obj, objArr, iArr, iArr2, z);
    }

    public static void org_chromium_net_impl_CronetBidirectionalStream_destroy(long j, Object obj, boolean z) {
        C0000S.MS2l1kNx(j, obj, z);
    }

    public static void org_chromium_net_impl_CronetLibraryLoader_cronetInitOnInitThread() {
        C0000S.MROCxiBo();
    }

    public static String org_chromium_net_impl_CronetLibraryLoader_getCronetVersion() {
        return C0000S.M6xubM8G();
    }

    /* renamed from: org_chromium_net_impl_CronetUploadDataStream_attachUploadDataToRequest */
    public static long m62xe897b21c(Object obj, long j, long j2) {
        return C0000S.MA4X1aZa(obj, j, j2);
    }

    /* renamed from: org_chromium_net_impl_CronetUploadDataStream_createAdapterForTesting */
    public static long m63x948d6832(Object obj) {
        return C0000S.MnDEFloP(obj);
    }

    /* renamed from: org_chromium_net_impl_CronetUploadDataStream_createUploadDataStreamForTesting */
    public static long m64xfda3a116(Object obj, long j, long j2) {
        return C0000S.MymnNC4_(obj, j, j2);
    }

    public static void org_chromium_net_impl_CronetUploadDataStream_onReadSucceeded(long j, Object obj, int i, boolean z) {
        C0000S.MpWH3VIr(j, obj, i, z);
    }

    public static void org_chromium_net_impl_CronetUploadDataStream_onRewindSucceeded(long j, Object obj) {
        C0000S.MFpRjSMv(j, obj);
    }

    public static void org_chromium_net_impl_CronetUploadDataStream_destroy(long j) {
        C0000S.MMW1G0N1(j);
    }

    public static long org_chromium_net_impl_CronetUrlRequest_createRequestAdapter(Object obj, long j, String str, int i, boolean z, boolean z2, boolean z3, boolean z4, int i2, boolean z5, int i3, int i4) {
        return C0000S.MuOIsMvf(obj, j, str, i, z, z2, z3, z4, i2, z5, i3, i4);
    }

    public static boolean org_chromium_net_impl_CronetUrlRequest_setHttpMethod(long j, Object obj, String str) {
        return C0000S.M51RPBJe(j, obj, str);
    }

    public static boolean org_chromium_net_impl_CronetUrlRequest_addRequestHeader(long j, Object obj, String str, String str2) {
        return C0000S.MvHusd1J(j, obj, str, str2);
    }

    public static void org_chromium_net_impl_CronetUrlRequest_start(long j, Object obj) {
        C0000S.MabZ5m6r(j, obj);
    }

    public static void org_chromium_net_impl_CronetUrlRequest_followDeferredRedirect(long j, Object obj) {
        C0000S.Mhp54Oqs(j, obj);
    }

    public static boolean org_chromium_net_impl_CronetUrlRequest_readData(long j, Object obj, Object obj2, int i, int i2) {
        return C0000S.MfCxA8r3(j, obj, obj2, i, i2);
    }

    public static void org_chromium_net_impl_CronetUrlRequest_destroy(long j, Object obj, boolean z) {
        C0000S.M4znfYdB(j, obj, z);
    }

    public static void org_chromium_net_impl_CronetUrlRequest_getStatus(long j, Object obj, Object obj2) {
        C0000S.MgIIMpT9(j, obj, obj2);
    }

    /* renamed from: org_chromium_net_impl_CronetUrlRequestContext_createRequestContextConfig */
    public static long m67xc1b4431a(String str, String str2, boolean z, String str3, boolean z2, boolean z3, boolean z4, int i, long j, String str4, long j2, boolean z5, boolean z6, int i2) {
        return C0000S.MB3ntV7V(str, str2, z, str3, z2, z3, z4, i, j, str4, j2, z5, z6, i2);
    }

    public static void org_chromium_net_impl_CronetUrlRequestContext_addQuicHint(long j, String str, int i, int i2) {
        C0000S.MyRIv1Ij(j, str, i, i2);
    }

    public static void org_chromium_net_impl_CronetUrlRequestContext_addPkp(long j, String str, byte[][] bArr, boolean z, long j2) {
        C0000S.Muq3ic6p(j, str, bArr, z, j2);
    }

    /* renamed from: org_chromium_net_impl_CronetUrlRequestContext_createRequestContextAdapter */
    public static long m66xf78fde77(long j) {
        return C0000S.M135Cu0D(j);
    }

    public static int org_chromium_net_impl_CronetUrlRequestContext_setMinLogLevel(int i) {
        return C0000S.MnO2u2DQ(i);
    }

    public static byte[] org_chromium_net_impl_CronetUrlRequestContext_getHistogramDeltas() {
        return C0000S.M7CZ_Klr();
    }

    public static void org_chromium_net_impl_CronetUrlRequestContext_destroy(long j, Object obj) {
        C0000S.MeBvNXm5(j, obj);
    }

    public static boolean org_chromium_net_impl_CronetUrlRequestContext_startNetLogToFile(long j, Object obj, String str, boolean z) {
        return C0000S.MgwJQAH1(j, obj, str, z);
    }

    public static void org_chromium_net_impl_CronetUrlRequestContext_startNetLogToDisk(long j, Object obj, String str, boolean z, int i) {
        C0000S.MTULt02u(j, obj, str, z, i);
    }

    public static void org_chromium_net_impl_CronetUrlRequestContext_stopNetLog(long j, Object obj) {
        C0000S.MKFm_qQ7(j, obj);
    }

    /* renamed from: org_chromium_net_impl_CronetUrlRequestContext_initRequestContextOnInitThread */
    public static void m68xfc16f265(long j, Object obj) {
        C0000S.M6Dz0nZ5(j, obj);
    }

    /* renamed from: org_chromium_net_impl_CronetUrlRequestContext_configureNetworkQualityEstimatorForTesting */
    public static void m65x303bda2c(long j, Object obj, boolean z, boolean z2, boolean z3) {
        C0000S.M6sIJDgy(j, obj, z, z2, z3);
    }

    /* renamed from: org_chromium_net_impl_CronetUrlRequestContext_provideRTTObservations */
    public static void m69xdbfcaef4(long j, Object obj, boolean z) {
        C0000S.MpnFLFF2(j, obj, z);
    }

    /* renamed from: org_chromium_net_impl_CronetUrlRequestContext_provideThroughputObservations */
    public static void m70x86c48316(long j, Object obj, boolean z) {
        C0000S.MnPUhNKP(j, obj, z);
    }
}
