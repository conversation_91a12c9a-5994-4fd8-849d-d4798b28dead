package aegon.chrome.base.task;

import java.util.concurrent.Executor;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class AsyncTask$$Lambda$2 implements Executor {
    private static final AsyncTask$$Lambda$2 instance = new AsyncTask$$Lambda$2();

    private AsyncTask$$Lambda$2() {
    }

    @Override // java.util.concurrent.Executor
    public final void execute(Runnable runnable) {
        PostTask.postTask(TaskTraits.BEST_EFFORT_MAY_BLOCK, runnable);
    }
}
