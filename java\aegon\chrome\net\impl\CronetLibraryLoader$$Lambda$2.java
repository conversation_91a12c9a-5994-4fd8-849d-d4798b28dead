package aegon.chrome.net.impl;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex */
final /* synthetic */ class CronetLibraryLoader$$Lambda$2 implements Runnable {
    private static final CronetLibraryLoader$$Lambda$2 instance = new CronetLibraryLoader$$Lambda$2();

    private CronetLibraryLoader$$Lambda$2() {
    }

    @Override // java.lang.Runnable
    public final void run() {
        CronetLibraryLoaderJni.get().cronetInitOnInitThread();
    }
}
