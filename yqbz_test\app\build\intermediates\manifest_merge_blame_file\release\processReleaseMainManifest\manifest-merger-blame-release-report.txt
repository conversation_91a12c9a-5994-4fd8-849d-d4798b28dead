1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.yqbz_test"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.yqbz_test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.yqbz_test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:5:5-36:19
18        android:allowBackup="true"
18-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:7:9-65
21        android:extractNativeLibs="false"
22        android:fullBackupContent="@xml/backup_rules"
22-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:8:9-54
23        android:icon="@mipmap/ic_launcher"
23-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:9:9-43
24        android:label="@string/app_name"
24-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:10:9-41
25        android:roundIcon="@mipmap/ic_launcher_round"
25-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:11:9-54
26        android:supportsRtl="true"
26-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:12:9-35
27        android:theme="@style/Theme.Yqbz_test" >
27-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:13:9-47
28
29        <!-- Main Activity -->
30        <activity
30-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:16:9-23:20
31            android:name="com.example.yqbz_test.MainActivity"
31-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:17:13-41
32            android:exported="true" >
32-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:18:13-36
33            <intent-filter>
33-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:19:13-22:29
34                <action android:name="android.intent.action.MAIN" />
34-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:20:17-69
34-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:20:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:21:17-77
36-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:21:27-74
37            </intent-filter>
38        </activity>
39
40        <!-- Xposed Module Metadata -->
41        <meta-data
41-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:26:9-28:36
42            android:name="xposedmodule"
42-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:27:13-40
43            android:value="true" />
43-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:28:13-33
44        <meta-data
44-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:29:9-31:44
45            android:name="xposeddescription"
45-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:30:13-45
46            android:value="元气桌面壁纸广告屏蔽模块" />
46-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:31:13-41
47        <meta-data
47-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:32:9-34:34
48            android:name="xposedminversion"
48-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:33:13-44
49            android:value="54" />
49-->D:\桌面\元气桌面壁纸\yqbz_test\app\src\main\AndroidManifest.xml:34:13-31
50
51        <provider
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
52            android:name="androidx.startup.InitializationProvider"
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
53            android:authorities="com.example.yqbz_test.androidx-startup"
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
54            android:exported="false" >
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
55            <meta-data
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.emoji2.text.EmojiCompatInitializer"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
57                android:value="androidx.startup" />
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd49396ca2dafe9ce896b76ae0dc7821\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
59-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd49396ca2dafe9ce896b76ae0dc7821\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
60                android:value="androidx.startup" />
60-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd49396ca2dafe9ce896b76ae0dc7821\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
63                android:value="androidx.startup" />
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
64        </provider>
65
66        <receiver
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
67            android:name="androidx.profileinstaller.ProfileInstallReceiver"
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
68            android:directBootAware="false"
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
69            android:enabled="true"
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
70            android:exported="true"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
71            android:permission="android.permission.DUMP" >
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
73                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
76                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
79                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
82                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
83            </intent-filter>
84        </receiver>
85    </application>
86
87</manifest>
