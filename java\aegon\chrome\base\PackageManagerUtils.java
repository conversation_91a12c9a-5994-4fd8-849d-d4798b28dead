package aegon.chrome.base;

import android.content.Intent;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.TransactionTooLargeException;
import com.yxcorp.utility.uri.UriUtil;
import java.util.Collections;
import java.util.List;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class PackageManagerUtils {
    public static final Intent BROWSER_INTENT = new Intent().setAction("android.intent.action.VIEW").addCategory("android.intent.category.BROWSABLE").setData(Uri.fromParts(UriUtil.HTTP_SCHEME, "", null));
    private static final String TAG = "PackageManagerUtils";

    public static ResolveInfo resolveActivity(Intent intent, int i) {
        try {
            StrictModeContext strictModeContextAllowDiskWrites = StrictModeContext.allowDiskWrites();
            try {
                ResolveInfo resolveInfoResolveActivity = ContextUtils.getApplicationContext().getPackageManager().resolveActivity(intent, i);
                if (strictModeContextAllowDiskWrites != null) {
                    strictModeContextAllowDiskWrites.lambda$new$0();
                }
                return resolveInfoResolveActivity;
            } catch (Throwable th) {
                if (strictModeContextAllowDiskWrites != null) {
                    try {
                        strictModeContextAllowDiskWrites.lambda$new$0();
                    } catch (Throwable unused) {
                    }
                }
                throw th;
            }
        } catch (RuntimeException e) {
            handleExpectedExceptionsOrRethrow(e, intent);
            return null;
        }
    }

    public static List<ResolveInfo> queryIntentActivities(Intent intent, int i) {
        try {
            StrictModeContext strictModeContextAllowDiskReads = StrictModeContext.allowDiskReads();
            try {
                List<ResolveInfo> listQueryIntentActivities = ContextUtils.getApplicationContext().getPackageManager().queryIntentActivities(intent, i);
                if (strictModeContextAllowDiskReads != null) {
                    strictModeContextAllowDiskReads.lambda$new$0();
                }
                return listQueryIntentActivities;
            } catch (Throwable th) {
                if (strictModeContextAllowDiskReads != null) {
                    try {
                        strictModeContextAllowDiskReads.lambda$new$0();
                    } catch (Throwable unused) {
                    }
                }
                throw th;
            }
        } catch (RuntimeException e) {
            handleExpectedExceptionsOrRethrow(e, intent);
            return Collections.emptyList();
        }
    }

    public static Intent getQueryInstalledHomeLaunchersIntent() {
        return new Intent("android.intent.action.MAIN").addCategory("android.intent.category.HOME");
    }

    public static ResolveInfo resolveDefaultWebBrowserActivity() {
        return resolveActivity(BROWSER_INTENT, 65536);
    }

    public static List<ResolveInfo> queryAllWebBrowsersInfo() {
        return queryIntentActivities(BROWSER_INTENT, 983040);
    }

    public static List<ResolveInfo> queryAllLaunchersInfo() {
        return queryIntentActivities(getQueryInstalledHomeLaunchersIntent(), 131072);
    }

    private static void handleExpectedExceptionsOrRethrow(RuntimeException runtimeException, Intent intent) {
        if ((runtimeException instanceof NullPointerException) || (runtimeException.getCause() instanceof TransactionTooLargeException)) {
            Log.m43e(TAG, "Could not resolve Activity for intent " + intent.toString(), runtimeException);
            return;
        }
        throw runtimeException;
    }
}
