package aegon.chrome.base.compat;

import android.app.Activity;
import android.app.AlarmManager;
import android.app.Notification;
import android.app.PendingIntent;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Icon;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkInfo;
import android.os.BatteryManager;
import android.os.PowerManager;
import android.os.Process;
import android.os.UserManager;
import android.security.NetworkSecurityPolicy;
import android.view.ActionMode;
import android.view.Display;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.webkit.WebView;
import android.webkit.WebViewClient;

/* JADX WARN: Classes with same name are omitted:
  D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes21.dex
 */
/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public final class ApiHelperForM {
    public static int getPendingIntentImmutableFlag() {
        return 67108864;
    }

    private ApiHelperForM() {
    }

    public static void onPageCommitVisible(WebViewClient webViewClient, WebView webView, String str) {
        webViewClient.onPageCommitVisible(webView, str);
    }

    public static boolean isProcess64Bit() {
        return Process.is64Bit();
    }

    public static Network getBoundNetworkForProcess(ConnectivityManager connectivityManager) {
        return connectivityManager.getBoundNetworkForProcess();
    }

    public static long getNetworkHandle(Network network) {
        return network.getNetworkHandle();
    }

    public static Network getActiveNetwork(ConnectivityManager connectivityManager) {
        try {
            return connectivityManager.getActiveNetwork();
        } catch (Exception unused) {
            return null;
        }
    }

    public static NetworkInfo getNetworkInfo(ConnectivityManager connectivityManager, Network network) {
        try {
            return connectivityManager.getNetworkInfo(network);
        } catch (Exception unused) {
            return null;
        }
    }

    public static void requestActivityPermissions(Activity activity, String[] strArr, int i) {
        activity.requestPermissions(strArr, i);
    }

    public static boolean shouldShowRequestPermissionRationale(Activity activity, String str) {
        return activity.shouldShowRequestPermissionRationale(str);
    }

    public static boolean isPermissionRevokedByPolicy(Activity activity, String str) {
        return activity.getPackageManager().isPermissionRevokedByPolicy(str, activity.getPackageName());
    }

    public static boolean isCleartextTrafficPermitted() {
        return NetworkSecurityPolicy.getInstance().isCleartextTrafficPermitted();
    }

    public static boolean isSystemUser(UserManager userManager) {
        return userManager.isSystemUser();
    }

    public static void invalidateContentRectOnActionMode(ActionMode actionMode) {
        actionMode.invalidateContentRect();
    }

    public static void onWindowFocusChangedOnActionMode(ActionMode actionMode, boolean z) {
        actionMode.onWindowFocusChanged(z);
    }

    public static int getActionModeType(ActionMode actionMode) {
        return actionMode.getType();
    }

    public static long getDefaultActionModeHideDuration() {
        return ViewConfiguration.getDefaultActionModeHideDuration();
    }

    public static void hideActionMode(ActionMode actionMode, long j) {
        actionMode.hide(j);
    }

    public static void reportNetworkConnectivity(ConnectivityManager connectivityManager, Network network, boolean z) {
        connectivityManager.reportNetworkConnectivity(network, z);
    }

    public static int getActionButton(MotionEvent motionEvent) {
        return motionEvent.getActionButton();
    }

    public static void setAlarmManagerExactAndAllowWhileIdle(AlarmManager alarmManager, int i, long j, PendingIntent pendingIntent) {
        alarmManager.setExactAndAllowWhileIdle(i, j, pendingIntent);
    }

    public static Display.Mode[] getDisplaySupportedModes(Display display) {
        return display.getSupportedModes();
    }

    public static Display.Mode getDisplayMode(Display display) {
        return display.getMode();
    }

    public static int getModePhysicalWidth(Display.Mode mode) {
        return mode.getPhysicalWidth();
    }

    public static int getModePhysicalHeight(Display.Mode mode) {
        return mode.getPhysicalHeight();
    }

    public static boolean isCharging(BatteryManager batteryManager) {
        return batteryManager.isCharging();
    }

    public static Icon createIconWithBitmap(Bitmap bitmap) {
        return Icon.createWithBitmap(bitmap);
    }

    public static boolean isDeviceIdleMode(PowerManager powerManager) {
        return powerManager.isDeviceIdleMode();
    }

    public static Notification.Builder setSmallIcon(Notification.Builder builder, Icon icon) {
        return builder.setSmallIcon(icon);
    }

    public static Icon createIconWithResource(Context context, int i) {
        return Icon.createWithResource(context, i);
    }

    public static <T> T getSystemService(Context context, Class<T> cls) {
        return (T) context.getSystemService(cls);
    }

    public static Notification.Action.Builder newNotificationActionBuilder(Icon icon, CharSequence charSequence, PendingIntent pendingIntent) {
        return new Notification.Action.Builder(icon, charSequence, pendingIntent);
    }
}
