package aegon.chrome.net.impl;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.net.impl.CronetUrlRequestContext;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class CronetUrlRequestContextJni implements CronetUrlRequestContext.Natives {
    public static final JniStaticTestMocker<CronetUrlRequestContext.Natives> TEST_HOOKS = new JniStaticTestMocker<CronetUrlRequestContext.Natives>() { // from class: aegon.chrome.net.impl.CronetUrlRequestContextJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(CronetUrlRequestContext.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                CronetUrlRequestContext.Natives unused = CronetUrlRequestContextJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static CronetUrlRequestContext.Natives testInstance;

    CronetUrlRequestContextJni() {
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public long createRequestContextConfig(String str, String str2, boolean z, String str3, boolean z2, boolean z3, boolean z4, int i, long j, String str4, long j2, boolean z5, boolean z6, int i2) {
        return GEN_JNI.m67xc1b4431a(str, str2, z, str3, z2, z3, z4, i, j, str4, j2, z5, z6, i2);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public void addQuicHint(long j, String str, int i, int i2) {
        GEN_JNI.org_chromium_net_impl_CronetUrlRequestContext_addQuicHint(j, str, i, i2);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public void addPkp(long j, String str, byte[][] bArr, boolean z, long j2) {
        GEN_JNI.org_chromium_net_impl_CronetUrlRequestContext_addPkp(j, str, bArr, z, j2);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public long createRequestContextAdapter(long j) {
        return GEN_JNI.m66xf78fde77(j);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public int setMinLogLevel(int i) {
        return GEN_JNI.org_chromium_net_impl_CronetUrlRequestContext_setMinLogLevel(i);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public byte[] getHistogramDeltas() {
        return GEN_JNI.org_chromium_net_impl_CronetUrlRequestContext_getHistogramDeltas();
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public void destroy(long j, CronetUrlRequestContext cronetUrlRequestContext) {
        GEN_JNI.org_chromium_net_impl_CronetUrlRequestContext_destroy(j, cronetUrlRequestContext);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public boolean startNetLogToFile(long j, CronetUrlRequestContext cronetUrlRequestContext, String str, boolean z) {
        return GEN_JNI.org_chromium_net_impl_CronetUrlRequestContext_startNetLogToFile(j, cronetUrlRequestContext, str, z);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public void startNetLogToDisk(long j, CronetUrlRequestContext cronetUrlRequestContext, String str, boolean z, int i) {
        GEN_JNI.org_chromium_net_impl_CronetUrlRequestContext_startNetLogToDisk(j, cronetUrlRequestContext, str, z, i);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public void stopNetLog(long j, CronetUrlRequestContext cronetUrlRequestContext) {
        GEN_JNI.org_chromium_net_impl_CronetUrlRequestContext_stopNetLog(j, cronetUrlRequestContext);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public void initRequestContextOnInitThread(long j, CronetUrlRequestContext cronetUrlRequestContext) {
        GEN_JNI.m68xfc16f265(j, cronetUrlRequestContext);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public void configureNetworkQualityEstimatorForTesting(long j, CronetUrlRequestContext cronetUrlRequestContext, boolean z, boolean z2, boolean z3) {
        GEN_JNI.m65x303bda2c(j, cronetUrlRequestContext, z, z2, z3);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public void provideRTTObservations(long j, CronetUrlRequestContext cronetUrlRequestContext, boolean z) {
        GEN_JNI.m69xdbfcaef4(j, cronetUrlRequestContext, z);
    }

    @Override // aegon.chrome.net.impl.CronetUrlRequestContext.Natives
    public void provideThroughputObservations(long j, CronetUrlRequestContext cronetUrlRequestContext, boolean z) {
        GEN_JNI.m70x86c48316(j, cronetUrlRequestContext, z);
    }

    public static CronetUrlRequestContext.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            CronetUrlRequestContext.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.net.impl.CronetUrlRequestContext.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new CronetUrlRequestContextJni();
    }
}
