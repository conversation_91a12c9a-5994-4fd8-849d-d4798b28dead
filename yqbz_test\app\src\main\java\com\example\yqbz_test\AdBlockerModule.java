package com.example.yqbz_test;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

/**
 * 元气桌面壁纸广告屏蔽模块
 * 目标包名: com.cmcm.cfwallpaper
 */
public class AdBlockerModule implements IXposedHookLoadPackage {
    
    private static final String TARGET_PACKAGE = "com.cmcm.cfwallpaper";
    private static final String TAG = "YQBZAdBlocker";
    
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        if (!TARGET_PACKAGE.equals(lpparam.packageName)) {
            return;
        }
        
        XposedBridge.log(TAG + ": 开始Hook元气桌面壁纸广告");
        
        // Hook快手广告SDK
        hookKuaishouAds(lpparam);
        
        // Hook腾讯广告SDK
        hookTencentAds(lpparam);
        
        // Hook字节跳动广告SDK
        hookByteDanceAds(lpparam);
        
        // Hook通用广告显示方法
        hookGenericAdMethods(lpparam);

        // 启用高级广告屏蔽
        AdvancedAdBlocker.initAdvancedAdBlocking(lpparam);
        AdvancedAdBlocker.hookAllAdMethods(lpparam);

        XposedBridge.log(TAG + ": 广告Hook设置完成");
    }
    
    /**
     * Hook快手广告SDK
     */
    private void hookKuaishouAds(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook开屏广告加载器
            hookClassSafely("com.kwad.components.ad.splashscreen.a", "loadSplashScreenAd", lpparam);

            // Hook开屏广告显示
            hookClassSafely("com.kwad.components.ad.splashscreen.presenter.b", "lU", lpparam);
            hookClassSafely("com.kwad.components.ad.splashscreen.presenter.b", "xO", lpparam);

            // Hook激励视频广告
            hookClassSafely("com.kwad.components.ad.reward.g.a", "loadRewardVideoAd", lpparam);

            // Hook Banner广告
            hookClassSafely("com.kwad.components.ad.c.a", "loadBannerAd", lpparam);

            // Hook全屏视频广告
            hookClassSafely("com.kwad.components.ad.fullscreen.d", "onAdShow", lpparam);
            hookClassSafely("com.kwad.components.ad.fullscreen.KsFullScreenVideoActivityProxy", "onCreate", lpparam);

            // Hook原生广告
            hookClassSafely("com.kwad.components.ad.nativead.a", "loadNativeAd", lpparam);

            // Hook广告SDK初始化
            hookClassSafely("com.kwad.sdk.l", "init", lpparam);
            hookClassSafely("com.kwad.sdk.KsAdSDKImpl", "init", lpparam);

            // Hook广告显示相关的Presenter
            hookClassSafely("com.kwad.components.ad.splashscreen.presenter.l", "xN", lpparam);
            hookClassSafely("com.kwad.components.ad.splashscreen.presenter.n", "onClick", lpparam);
            hookClassSafely("com.kwad.components.ad.splashscreen.presenter.s", "lU", lpparam);

            // Hook广告WebView
            hookClassSafely("com.kwad.sdk.core.webview.KsAdWebView", "loadUrl", lpparam);

            XposedBridge.log(TAG + ": 快手广告Hook完成");

        } catch (Throwable t) {
            XposedBridge.log(TAG + ": Hook快手广告失败: " + t.getMessage());
        }
    }
    
    /**
     * Hook腾讯广告SDK
     */
    private void hookTencentAds(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook腾讯开屏广告
            hookClassSafely("com.qq.e.comm.plugin.splash.ANSplashAdViewAdapter", "fetchAdOnly", lpparam);
            hookClassSafely("com.qq.e.comm.plugin.splash.ANSplashAdViewAdapter", "fetchFullScreenAdOnly", lpparam);
            hookClassSafely("com.qq.e.comm.plugin.splash.ANSplashAdViewAdapter", "setADListener", lpparam);

            // Hook腾讯Banner广告
            hookClassSafely("com.qq.e.comm.plugin.banner2.ANUnifiedBannerAdapter", "onADEvent", lpparam);
            hookClassSafely("com.qq.e.comm.plugin.banner2.ANUnifiedBannerAdapter", "loadAD", lpparam);

            // Hook腾讯插屏广告
            hookClassSafely("com.qq.e.comm.plugin.intersitial2.ANInterstitialAdAdapter", "onADEvent", lpparam);
            hookClassSafely("com.qq.e.comm.plugin.intersitial2.ANInterstitialAdAdapter", "loadAd", lpparam);
            hookClassSafely("com.qq.e.comm.plugin.l30", "show", lpparam);
            hookClassSafely("com.qq.e.comm.plugin.l30", "loadAd", lpparam);

            // Hook腾讯广告工厂
            hookClassSafely("com.qq.e.comm.plugin.POFactoryImpl", "getInstance", lpparam);
            hookClassSafely("com.qq.e.comm.plugin.POFactoryImpl", "getNativeSplashAdView", lpparam);
            hookClassSafely("com.qq.e.comm.plugin.POFactoryImpl", "getUnifiedBannerViewDelegate", lpparam);
            hookClassSafely("com.qq.e.comm.plugin.POFactoryImpl", "getUnifiedInterstitialADDelegate", lpparam);
            hookClassSafely("com.qq.e.comm.plugin.POFactoryImpl", "getRewardVideoADDelegate", lpparam);

            // Hook腾讯广告尺寸计算
            hookClassSafely("com.qq.e.comm.plugin.c2", "a", lpparam);

            XposedBridge.log(TAG + ": 腾讯广告Hook完成");

        } catch (Throwable t) {
            XposedBridge.log(TAG + ": Hook腾讯广告失败: " + t.getMessage());
        }
    }
    
    /**
     * Hook字节跳动广告SDK
     */
    private void hookByteDanceAds(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook字节跳动开屏广告
            hookClassSafely("com.alex.AlexGromoreSplashAdapter", "show", lpparam);
            hookClassSafely("com.alex.AlexGromoreSplashAdapter", "loadCustomNetworkAd", lpparam);
            hookClassSafely("com.alex.AlexGromoreSplashAdapter", "startBiddingRequest", lpparam);

            // Hook字节跳动Banner广告
            hookClassSafely("com.alex.AlexGromoreBannerAdapter", "loadCustomNetworkAd", lpparam);
            hookClassSafely("com.alex.AlexGromoreBannerAdapter", "startBiddingRequest", lpparam);

            // Hook字节跳动激励视频广告
            hookClassSafely("com.alex.AlexGromoreRewardedVideoAdapter", "loadCustomNetworkAd", lpparam);
            hookClassSafely("com.alex.AlexGromoreRewardedVideoAdapter", "isAdReady", lpparam);

            // Hook字节跳动插屏广告
            hookClassSafely("com.alex.AlexGromoreInterstitialAdapter", "loadCustomNetworkAd", lpparam);

            // Hook字节跳动原生广告
            hookClassSafely("com.alex.AlexGromoreNativeAdapter", "loadCustomNetworkAd", lpparam);
            hookClassSafely("com.alex.AlexGromoreNativeExpressHandler", "loadAd", lpparam);

            // Hook字节跳动SDK初始化
            hookClassSafely("com.alex.AlexGromoreInitV2Manager", "initSDK", lpparam);
            hookClassSafely("com.alex.AlexGromoreInitV2Manager", "getInstance", lpparam);

            // Hook字节跳动广告配置
            hookClassSafely("com.alex.AlexGromoreConfig", "setMediationConfig", lpparam);

            XposedBridge.log(TAG + ": 字节跳动广告Hook完成");

        } catch (Throwable t) {
            XposedBridge.log(TAG + ": Hook字节跳动广告失败: " + t.getMessage());
        }
    }
    
    /**
     * Hook通用广告显示方法
     */
    private void hookGenericAdMethods(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook View的addView方法，过滤广告View
            XposedHelpers.findAndHookMethod(
                ViewGroup.class,
                "addView",
                View.class,
                new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        View view = (View) param.args[0];
                        if (view != null) {
                            String className = view.getClass().getName();
                            // 检查是否为广告相关的View
                            if (isAdView(className)) {
                                XposedBridge.log(TAG + ": 阻止广告View添加: " + className);
                                param.setResult(null);
                            }
                        }
                    }
                }
            );
            
            XposedBridge.log(TAG + ": 通用广告Hook完成");
            
        } catch (Throwable t) {
            XposedBridge.log(TAG + ": Hook通用广告方法失败: " + t.getMessage());
        }
    }
    
    /**
     * 安全地Hook类方法
     */
    private void hookClassSafely(String className, String methodName, XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> clazz = XposedHelpers.findClass(className, lpparam.classLoader);
            XposedHelpers.findAndHookMethod(clazz, methodName, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    XposedBridge.log(TAG + ": 阻止广告方法调用: " + className + "." + methodName);
                    param.setResult(null);
                }
            });
        } catch (Throwable t) {
            // 静默处理，避免日志过多
        }
    }

    /**
     * 安全地Hook带参数的类方法
     */
    private void hookClassSafelyWithParams(String className, String methodName,
                                         XC_LoadPackage.LoadPackageParam lpparam, Object... parameterTypes) {
        try {
            Class<?> clazz = XposedHelpers.findClass(className, lpparam.classLoader);
            XposedHelpers.findAndHookMethod(clazz, methodName, parameterTypes, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    XposedBridge.log(TAG + ": 阻止广告方法调用: " + className + "." + methodName);
                    param.setResult(null);
                }
            });
        } catch (Throwable t) {
            // 静默处理，避免日志过多
        }
    }

    /**
     * 判断是否为广告相关的View
     */
    private boolean isAdView(String className) {
        if (className == null) return false;

        String[] adKeywords = {
            "ad", "Ad", "AD", "splash", "Splash", "banner", "Banner",
            "reward", "Reward", "interstitial", "Interstitial", "native", "Native",
            "kwad", "qq.e", "alex", "gromore", "bytedance", "tencent", "anythink",
            "SplashAd", "BannerAd", "RewardAd", "InterstitialAd", "NativeAd",
            "AdView", "AdContainer", "AdLayout", "AdFrame"
        };

        for (String keyword : adKeywords) {
            if (className.contains(keyword)) {
                return true;
            }
        }

        return false;
    }
}
