package aegon.chrome.base;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
public class NativeLibraryLoadedStatus {
    private static NativeLibraryLoadedStatusProvider sProvider;

    public interface NativeLibraryLoadedStatusProvider {
        boolean areMainDexNativeMethodsReady();

        boolean areNativeMethodsReady();
    }

    public static void checkLoaded(boolean z) {
        NativeLibraryLoadedStatusProvider nativeLibraryLoadedStatusProvider;
        boolean zAreNativeMethodsReady;
        if (aegon.chrome.build.BuildConfig.ENABLE_ASSERTS && (nativeLibraryLoadedStatusProvider = sProvider) != null) {
            if (z) {
                zAreNativeMethodsReady = nativeLibraryLoadedStatusProvider.areMainDexNativeMethodsReady();
            } else {
                zAreNativeMethodsReady = nativeLibraryLoadedStatusProvider.areNativeMethodsReady();
            }
            if (!zAreNativeMethodsReady) {
                throw new JniException("Native method called before the native library was ready.");
            }
        }
    }

    public static void setProvider(NativeLibraryLoadedStatusProvider nativeLibraryLoadedStatusProvider) {
        sProvider = nativeLibraryLoadedStatusProvider;
    }

    public static NativeLibraryLoadedStatusProvider getProviderForTesting() {
        return sProvider;
    }
}
