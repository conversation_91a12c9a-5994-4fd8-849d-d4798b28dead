package android.icu.impl.data;

import java.util.ListResourceBundle;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class HolidayBundle_fr extends ListResourceBundle {
    private static final Object[][] fContents = {new Object[]{"All Saints' Day", "Toussaint"}, new Object[]{"Armistice Day", "Jour de l'Armistice"}, new Object[]{"Ascension", "Ascension"}, new Object[]{"Bastille Day", "Fête de la Bastille"}, new Object[]{"Benito Juárez Day", "Fête de <PERSON>"}, new Object[]{"Boxing Day", "Lendemain de Noël"}, new Object[]{"Christmas Eve", "Veille de Noël"}, new Object[]{"Christmas", "Noël"}, new Object[]{"Easter Monday", "Pâques lundi"}, new Object[]{"Easter Sunday", "Pâques"}, new Object[]{"Epiphany", "l'Épiphanie"}, new Object[]{"Flag Day", "Fête du Drapeau"}, new Object[]{"Good Friday", "Vendredi Saint"}, new Object[]{"Halloween", "Veille de la Toussaint"}, new Object[]{"All Saints' Day", "Toussaint"}, new Object[]{"Independence Day", "Fête Indépendance"}, new Object[]{"Maundy Thursday", "Jeudi Saint"}, new Object[]{"Mother's Day", "Fête des mères"}, new Object[]{"National Day", "Fête Nationale"}, new Object[]{"New Year's Day", "Jour de l'an"}, new Object[]{"Palm Sunday", "les Rameaux"}, new Object[]{"Pentecost", "Pentecôte"}, new Object[]{"Shrove Tuesday", "Mardi Gras"}, new Object[]{"St. Stephen's Day", "Saint-Étienne"}, new Object[]{"Victoria Day", "Fête de la Victoria"}, new Object[]{"Victory Day", "Fête de la Victoire"}};

    @Override // java.util.ListResourceBundle
    public synchronized Object[][] getContents() {
        return fContents;
    }
}
