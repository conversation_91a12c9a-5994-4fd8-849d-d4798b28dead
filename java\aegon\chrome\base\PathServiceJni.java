package aegon.chrome.base;

import aegon.chrome.base.PathService;
import aegon.chrome.base.natives.GEN_JNI;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class PathServiceJni implements PathService.Natives {
    public static final JniStaticTestMocker<PathService.Natives> TEST_HOOKS = new JniStaticTestMocker<PathService.Natives>() { // from class: aegon.chrome.base.PathServiceJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(PathService.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                PathService.Natives unused = PathServiceJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static PathService.Natives testInstance;

    PathServiceJni() {
    }

    @Override // aegon.chrome.base.PathService.Natives
    public void override(int i, String str) {
        GEN_JNI.org_chromium_base_PathService_override(i, str);
    }

    public static PathService.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            PathService.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.base.PathService.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new PathServiceJni();
    }
}
