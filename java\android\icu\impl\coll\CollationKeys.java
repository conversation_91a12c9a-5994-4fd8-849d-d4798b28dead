package android.icu.impl.coll;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationKeys {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    private static final int CASE_LOWER_FIRST_COMMON_HIGH = 13;
    private static final int CASE_LOWER_FIRST_COMMON_LOW = 1;
    private static final int CASE_LOWER_FIRST_COMMON_MAX_COUNT = 7;
    private static final int CASE_LOWER_FIRST_COMMON_MIDDLE = 7;
    private static final int CASE_UPPER_FIRST_COMMON_HIGH = 15;
    private static final int CASE_UPPER_FIRST_COMMON_LOW = 3;
    private static final int CASE_UPPER_FIRST_COMMON_MAX_COUNT = 13;
    private static final int QUAT_COMMON_HIGH = 252;
    private static final int QUAT_COMMON_LOW = 28;
    private static final int QUAT_COMMON_MAX_COUNT = 113;
    private static final int QUAT_COMMON_MIDDLE = 140;
    private static final int QUAT_SHIFTED_LIMIT_BYTE = 27;
    static final int SEC_COMMON_HIGH = 69;
    private static final int SEC_COMMON_LOW = 5;
    private static final int SEC_COMMON_MAX_COUNT = 33;
    private static final int SEC_COMMON_MIDDLE = 37;
    private static final int TER_LOWER_FIRST_COMMON_HIGH = 69;
    private static final int TER_LOWER_FIRST_COMMON_LOW = 5;
    private static final int TER_LOWER_FIRST_COMMON_MAX_COUNT = 33;
    private static final int TER_LOWER_FIRST_COMMON_MIDDLE = 37;
    private static final int TER_ONLY_COMMON_HIGH = 197;
    private static final int TER_ONLY_COMMON_LOW = 5;
    private static final int TER_ONLY_COMMON_MAX_COUNT = 97;
    private static final int TER_ONLY_COMMON_MIDDLE = 101;
    private static final int TER_UPPER_FIRST_COMMON_HIGH = 197;
    private static final int TER_UPPER_FIRST_COMMON_LOW = 133;
    private static final int TER_UPPER_FIRST_COMMON_MAX_COUNT = 33;
    private static final int TER_UPPER_FIRST_COMMON_MIDDLE = 165;
    public static final LevelCallback SIMPLE_LEVEL_FALLBACK = new LevelCallback();
    private static final int[] levelMasks = {2, 6, 22, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54};

    public static abstract class SortKeyByteSink {
        private int appended_ = 0;
        protected byte[] buffer_;

        protected abstract void AppendBeyondCapacity(byte[] bArr, int i, int i2, int i3);

        protected abstract boolean Resize(int i, int i2);

        public SortKeyByteSink(byte[] dest) {
            this.buffer_ = dest;
        }

        public void setBufferAndAppended(byte[] dest, int app) {
            this.buffer_ = dest;
            this.appended_ = app;
        }

        public void Append(byte[] bytes, int n) {
            if (n <= 0 || bytes == null) {
                return;
            }
            int length = this.appended_;
            this.appended_ += n;
            byte[] bArr = this.buffer_;
            int available = bArr.length - length;
            if (n <= available) {
                System.arraycopy((Object) bytes, 0, (Object) bArr, length, n);
            } else {
                AppendBeyondCapacity(bytes, 0, n, length);
            }
        }

        public void Append(int b2) {
            int i = this.appended_;
            if (i < this.buffer_.length || Resize(1, i)) {
                this.buffer_[this.appended_] = (byte) b2;
            }
            this.appended_++;
        }

        public int NumberOfBytesAppended() {
            return this.appended_;
        }

        public int GetRemainingCapacity() {
            return this.buffer_.length - this.appended_;
        }

        public boolean Overflowed() {
            return this.appended_ > this.buffer_.length;
        }
    }

    public static class LevelCallback {
        boolean needToWrite(int level) {
            return true;
        }
    }

    private static final class SortKeyLevel {
        static final /* synthetic */ boolean $assertionsDisabled = false;
        private static final int INITIAL_CAPACITY = 40;
        byte[] buffer = new byte[40];
        int len = 0;

        SortKeyLevel() {
        }

        boolean isEmpty() {
            return this.len == 0;
        }

        int length() {
            return this.len;
        }

        byte getAt(int index) {
            return this.buffer[index];
        }

        byte[] data() {
            return this.buffer;
        }

        void appendByte(int b2) {
            if (this.len < this.buffer.length || ensureCapacity(1)) {
                byte[] bArr = this.buffer;
                int i = this.len;
                this.len = i + 1;
                bArr[i] = (byte) b2;
            }
        }

        void appendWeight16(int w) {
            byte b0 = (byte) (w >>> 8);
            byte b1 = (byte) w;
            int appendLength = b1 == 0 ? 1 : 2;
            if (this.len + appendLength <= this.buffer.length || ensureCapacity(appendLength)) {
                byte[] bArr = this.buffer;
                int i = this.len;
                int i2 = i + 1;
                this.len = i2;
                bArr[i] = b0;
                if (b1 != 0) {
                    this.len = i2 + 1;
                    bArr[i2] = b1;
                }
            }
        }

        void appendWeight32(long w) {
            int appendLength = 4;
            byte[] bytes = {(byte) (w >>> 24), (byte) (w >>> 16), (byte) (w >>> 8), (byte) w};
            if (bytes[1] == 0) {
                appendLength = 1;
            } else if (bytes[2] == 0) {
                appendLength = 2;
            } else if (bytes[3] == 0) {
                appendLength = 3;
            }
            if (this.len + appendLength <= this.buffer.length || ensureCapacity(appendLength)) {
                byte[] bArr = this.buffer;
                int i = this.len;
                int i2 = i + 1;
                this.len = i2;
                bArr[i] = bytes[0];
                if (bytes[1] != 0) {
                    int i3 = i2 + 1;
                    this.len = i3;
                    bArr[i2] = bytes[1];
                    if (bytes[2] != 0) {
                        int i4 = i3 + 1;
                        this.len = i4;
                        bArr[i3] = bytes[2];
                        if (bytes[3] != 0) {
                            this.len = i4 + 1;
                            bArr[i4] = bytes[3];
                        }
                    }
                }
            }
        }

        void appendReverseWeight16(int w) {
            byte b0 = (byte) (w >>> 8);
            byte b1 = (byte) w;
            int appendLength = b1 == 0 ? 1 : 2;
            if (this.len + appendLength <= this.buffer.length || ensureCapacity(appendLength)) {
                if (b1 == 0) {
                    byte[] bArr = this.buffer;
                    int i = this.len;
                    this.len = i + 1;
                    bArr[i] = b0;
                    return;
                }
                byte[] bArr2 = this.buffer;
                int i2 = this.len;
                bArr2[i2] = b1;
                bArr2[i2 + 1] = b0;
                this.len = i2 + 2;
            }
        }

        void appendTo(SortKeyByteSink sink) {
            sink.Append(this.buffer, this.len - 1);
        }

        private boolean ensureCapacity(int appendCapacity) {
            int newCapacity = this.buffer.length * 2;
            int altCapacity = this.len + (appendCapacity * 2);
            if (newCapacity < altCapacity) {
                newCapacity = altCapacity;
            }
            if (newCapacity < 200) {
                newCapacity = 200;
            }
            byte[] newbuf = new byte[newCapacity];
            System.arraycopy((Object) this.buffer, 0, (Object) newbuf, 0, this.len);
            this.buffer = newbuf;
            return true;
        }
    }

    private static SortKeyLevel getSortKeyLevel(int levels, int level) {
        if ((levels & level) != 0) {
            return new SortKeyLevel();
        }
        return null;
    }

    private CollationKeys() {
    }

    /* JADX WARN: Removed duplicated region for block: B:138:0x024c  */
    /* JADX WARN: Removed duplicated region for block: B:184:0x02e1  */
    /* JADX WARN: Removed duplicated region for block: B:187:0x02e7  */
    /* JADX WARN: Removed duplicated region for block: B:243:0x039b  */
    /* JADX WARN: Removed duplicated region for block: B:246:0x03a1  */
    /* JADX WARN: Removed duplicated region for block: B:309:0x046a  */
    /* JADX WARN: Removed duplicated region for block: B:313:0x03f8 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:71:0x0132  */
    /* JADX WARN: Removed duplicated region for block: B:79:0x0156  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static void writeSortKeyUpToQuaternary(android.icu.impl.coll.CollationIterator r37, boolean[] r38, android.icu.impl.coll.CollationSettings r39, android.icu.impl.coll.CollationKeys.SortKeyByteSink r40, int r41, android.icu.impl.coll.CollationKeys.LevelCallback r42, boolean r43) {
        /*
            Method dump skipped, instructions count: 1151
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationKeys.writeSortKeyUpToQuaternary(android.icu.impl.coll.CollationIterator, boolean[], android.icu.impl.coll.CollationSettings, android.icu.impl.coll.CollationKeys$SortKeyByteSink, int, android.icu.impl.coll.CollationKeys$LevelCallback, boolean):void");
    }
}
