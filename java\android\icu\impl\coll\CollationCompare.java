package android.icu.impl.coll;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public final class CollationCompare {
    static final /* synthetic */ boolean $assertionsDisabled = false;

    /* JADX WARN: Code restructure failed: missing block: B:102:0x016a, code lost:
    
        if (r28 != 1) goto L104;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static int compareUpToQuaternary(android.icu.impl.coll.CollationIterator r36, android.icu.impl.coll.CollationIterator r37, android.icu.impl.coll.CollationSettings r38) {
        /*
            Method dump skipped, instructions count: 852
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: android.icu.impl.coll.CollationCompare.compareUpToQuaternary(android.icu.impl.coll.CollationIterator, android.icu.impl.coll.CollationIterator, android.icu.impl.coll.CollationSettings):int");
    }
}
