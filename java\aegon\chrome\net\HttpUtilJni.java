package aegon.chrome.net;

import aegon.chrome.base.JniStaticTestMocker;
import aegon.chrome.base.NativeLibraryLoadedStatus;
import aegon.chrome.base.natives.GEN_JNI;
import aegon.chrome.net.HttpUtil;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes29.dex */
class HttpUtilJni implements HttpUtil.Natives {
    public static final JniStaticTestMocker<HttpUtil.Natives> TEST_HOOKS = new JniStaticTestMocker<HttpUtil.Natives>() { // from class: aegon.chrome.net.HttpUtilJni.1
        @Override // aegon.chrome.base.JniStaticTestMocker
        public void setInstanceForTesting(HttpUtil.Natives natives) {
            if (GEN_JNI.TESTING_ENABLED) {
                HttpUtil.Natives unused = HttpUtilJni.testInstance = natives;
                return;
            }
            throw new RuntimeException("Tried to set a JNI mock when mocks aren't enabled!");
        }
    };
    private static HttpUtil.Natives testInstance;

    HttpUtilJni() {
    }

    @Override // aegon.chrome.net.HttpUtil.Natives
    public boolean isAllowedHeader(String str, String str2) {
        return GEN_JNI.org_chromium_net_HttpUtil_isAllowedHeader(str, str2);
    }

    public static HttpUtil.Natives get() {
        if (GEN_JNI.TESTING_ENABLED) {
            HttpUtil.Natives natives = testInstance;
            if (natives != null) {
                return natives;
            }
            if (GEN_JNI.REQUIRE_MOCK) {
                throw new UnsupportedOperationException("No mock found for the native implementation for org.chromium.net.HttpUtil.Natives. The current configuration requires all native implementations to have a mock instance.");
            }
        }
        NativeLibraryLoadedStatus.checkLoaded(false);
        return new HttpUtilJni();
    }
}
