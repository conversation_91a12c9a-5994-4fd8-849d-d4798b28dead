<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.1" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/yqbz_test/AdvancedAdBlocker.java"
            line="231"
            column="27"
            startOffset="8438"
            endLine="231"
            endColumn="38"
            endOffset="8449"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/yqbz_test/AdvancedAdBlocker.java"
            line="231"
            column="58"
            startOffset="8469"
            endLine="231"
            endColumn="69"
            endOffset="8480"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/yqbz_test/AdvancedAdBlocker.java"
            line="246"
            column="27"
            startOffset="8804"
            endLine="246"
            endColumn="38"
            endOffset="8815"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/yqbz_test/AdvancedAdBlocker.java"
            line="246"
            column="58"
            startOffset="8835"
            endLine="246"
            endColumn="69"
            endOffset="8846"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/yqbz_test/AdvancedAdBlocker.java"
            line="276"
            column="31"
            startOffset="9847"
            endLine="276"
            endColumn="42"
            endOffset="9858"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.11.1 is available: 8.13.0">
        <fix-replace
            description="Replace with 8.13.0"
            family="Update versions"
            oldString="8.11.1"
            replacement="8.13.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="15"
            endOffset="25"/>
        <map>
            <entry
                name="coordinate"
                string="com.android.application"/>
            <entry
                name="revision"
                string="8.13.0"/>
        </map>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.12.0 is available: 1.13.0">
        <fix-replace
            description="Change to 1.13.0"
            family="Update versions"
            oldString="1.12.0"
            replacement="1.13.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="7"
            column="12"
            startOffset="120"
            endLine="7"
            endColumn="20"
            endOffset="128"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/yqbz_test/MainActivity.java"
            line="19"
            column="26"
            startOffset="473"
            endLine="35"
            endColumn="41"
            endOffset="1032"/>
    </incident>

</incidents>
