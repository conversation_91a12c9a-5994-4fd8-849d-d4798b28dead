package android.icu.impl.duration.impl;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
interface RecordReader {
    boolean bool(String str);

    boolean[] boolArray(String str);

    char character(String str);

    char[] characterArray(String str);

    boolean close();

    byte namedIndex(String str, String[] strArr);

    byte[] namedIndexArray(String str, String[] strArr);

    boolean open(String str);

    String string(String str);

    String[] stringArray(String str);

    String[][] stringTable(String str);
}
