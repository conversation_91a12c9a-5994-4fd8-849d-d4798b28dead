package android.icu.impl.number;

import android.icu.impl.FormattedStringBuilder;
import android.icu.impl.StandardPlural;
import android.icu.impl.number.AffixUtils;
import android.icu.impl.number.Modifier;
import android.icu.number.NumberFormatter;
import android.icu.text.DecimalFormatSymbols;
import android.icu.text.NumberFormat;
import android.icu.text.PluralRules;
import android.icu.util.Currency;
import java.text.Format;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class MutablePatternModifier implements Modifier, AffixUtils.SymbolProvider, MicroPropsGenerator {
    static final /* synthetic */ boolean $assertionsDisabled = false;
    Currency currency;
    StringBuilder currentAffix;
    NumberFormat.Field field;
    final boolean isStrong;
    MicroPropsGenerator parent;
    AffixPatternProvider patternInfo;
    boolean perMilleReplacesPercent;
    StandardPlural plural;
    PluralRules rules;
    NumberFormatter.SignDisplay signDisplay;
    int signum;
    DecimalFormatSymbols symbols;
    NumberFormatter.UnitWidth unitWidth;

    public MutablePatternModifier(boolean isStrong) {
        this.isStrong = isStrong;
    }

    public void setPatternInfo(AffixPatternProvider patternInfo, NumberFormat.Field field) {
        this.patternInfo = patternInfo;
        this.field = field;
    }

    public void setPatternAttributes(NumberFormatter.SignDisplay signDisplay, boolean perMille) {
        this.signDisplay = signDisplay;
        this.perMilleReplacesPercent = perMille;
    }

    public void setSymbols(DecimalFormatSymbols symbols, Currency currency, NumberFormatter.UnitWidth unitWidth, PluralRules rules) {
        this.symbols = symbols;
        this.currency = currency;
        this.unitWidth = unitWidth;
        this.rules = rules;
    }

    public void setNumberProperties(int signum, StandardPlural plural) {
        this.signum = signum;
        this.plural = plural;
    }

    public boolean needsPlurals() {
        return this.patternInfo.containsSymbolType(-7);
    }

    public ImmutablePatternModifier createImmutable() {
        return createImmutableAndChain(null);
    }

    public ImmutablePatternModifier createImmutableAndChain(MicroPropsGenerator parent) {
        FormattedStringBuilder a2 = new FormattedStringBuilder();
        FormattedStringBuilder b2 = new FormattedStringBuilder();
        if (needsPlurals()) {
            AdoptingModifierStore pm = new AdoptingModifierStore();
            for (StandardPlural plural : StandardPlural.VALUES) {
                setNumberProperties(1, plural);
                pm.setModifier(1, plural, createConstantModifier(a2, b2));
                setNumberProperties(0, plural);
                pm.setModifier(0, plural, createConstantModifier(a2, b2));
                setNumberProperties(-1, plural);
                pm.setModifier(-1, plural, createConstantModifier(a2, b2));
            }
            pm.freeze();
            return new ImmutablePatternModifier(pm, this.rules, parent);
        }
        setNumberProperties(1, null);
        Modifier positive = createConstantModifier(a2, b2);
        setNumberProperties(0, null);
        Modifier zero = createConstantModifier(a2, b2);
        setNumberProperties(-1, null);
        Modifier negative = createConstantModifier(a2, b2);
        return new ImmutablePatternModifier(new AdoptingModifierStore(positive, zero, negative), null, parent);
    }

    private ConstantMultiFieldModifier createConstantModifier(FormattedStringBuilder a2, FormattedStringBuilder b2) {
        insertPrefix(a2.clear(), 0);
        insertSuffix(b2.clear(), 0);
        if (this.patternInfo.hasCurrencySign()) {
            return new CurrencySpacingEnabledModifier(a2, b2, !this.patternInfo.hasBody(), this.isStrong, this.symbols);
        }
        return new ConstantMultiFieldModifier(a2, b2, !this.patternInfo.hasBody(), this.isStrong);
    }

    public static class ImmutablePatternModifier implements MicroPropsGenerator {
        final MicroPropsGenerator parent;

        /* renamed from: pm */
        final AdoptingModifierStore f81pm;
        final PluralRules rules;

        ImmutablePatternModifier(AdoptingModifierStore pm, PluralRules rules, MicroPropsGenerator parent) {
            this.f81pm = pm;
            this.rules = rules;
            this.parent = parent;
        }

        @Override // android.icu.impl.number.MicroPropsGenerator
        public MicroProps processQuantity(DecimalQuantity quantity) {
            MicroProps micros = this.parent.processQuantity(quantity);
            applyToMicros(micros, quantity);
            return micros;
        }

        public void applyToMicros(MicroProps micros, DecimalQuantity quantity) {
            if (this.rules == null) {
                micros.modMiddle = this.f81pm.getModifierWithoutPlural(quantity.signum());
            } else {
                StandardPlural pluralForm = RoundingUtils.getPluralSafe(micros.rounder, this.rules, quantity);
                micros.modMiddle = this.f81pm.getModifier(quantity.signum(), pluralForm);
            }
        }
    }

    public MicroPropsGenerator addToChain(MicroPropsGenerator parent) {
        this.parent = parent;
        return this;
    }

    @Override // android.icu.impl.number.MicroPropsGenerator
    public MicroProps processQuantity(DecimalQuantity fq) {
        MicroProps micros = this.parent.processQuantity(fq);
        if (needsPlurals()) {
            StandardPlural pluralForm = RoundingUtils.getPluralSafe(micros.rounder, this.rules, fq);
            setNumberProperties(fq.signum(), pluralForm);
        } else {
            setNumberProperties(fq.signum(), null);
        }
        micros.modMiddle = this;
        return micros;
    }

    @Override // android.icu.impl.number.Modifier
    public int apply(FormattedStringBuilder output, int leftIndex, int rightIndex) {
        int overwriteLen;
        int prefixLen = insertPrefix(output, leftIndex);
        int suffixLen = insertSuffix(output, rightIndex + prefixLen);
        if (this.patternInfo.hasBody()) {
            overwriteLen = 0;
        } else {
            int overwriteLen2 = output.splice(leftIndex + prefixLen, rightIndex + prefixLen, "", 0, 0, null);
            overwriteLen = overwriteLen2;
        }
        int overwriteLen3 = rightIndex + prefixLen;
        CurrencySpacingEnabledModifier.applyCurrencySpacing(output, leftIndex, prefixLen, overwriteLen3 + overwriteLen, suffixLen, this.symbols);
        return prefixLen + overwriteLen + suffixLen;
    }

    @Override // android.icu.impl.number.Modifier
    public int getPrefixLength() {
        prepareAffix(true);
        int result = AffixUtils.unescapedCount(this.currentAffix, true, this);
        return result;
    }

    @Override // android.icu.impl.number.Modifier
    public int getCodePointCount() {
        prepareAffix(true);
        int result = AffixUtils.unescapedCount(this.currentAffix, false, this);
        prepareAffix(false);
        return result + AffixUtils.unescapedCount(this.currentAffix, false, this);
    }

    @Override // android.icu.impl.number.Modifier
    public boolean isStrong() {
        return this.isStrong;
    }

    @Override // android.icu.impl.number.Modifier
    public boolean containsField(Format.Field field) {
        return false;
    }

    @Override // android.icu.impl.number.Modifier
    public Modifier.Parameters getParameters() {
        return null;
    }

    @Override // android.icu.impl.number.Modifier
    public boolean semanticallyEquivalent(Modifier other) {
        return false;
    }

    private int insertPrefix(FormattedStringBuilder sb, int position) {
        prepareAffix(true);
        int length = AffixUtils.unescape(this.currentAffix, sb, position, this, this.field);
        return length;
    }

    private int insertSuffix(FormattedStringBuilder sb, int position) {
        prepareAffix(false);
        int length = AffixUtils.unescape(this.currentAffix, sb, position, this, this.field);
        return length;
    }

    private void prepareAffix(boolean isPrefix) {
        if (this.currentAffix == null) {
            this.currentAffix = new StringBuilder();
        }
        PatternStringUtils.patternInfoToStringBuilder(this.patternInfo, isPrefix, this.signum, this.signDisplay, this.plural, this.perMilleReplacesPercent, this.currentAffix);
    }

    @Override // android.icu.impl.number.AffixUtils.SymbolProvider
    public CharSequence getSymbol(int type) {
        int selector = 3;
        switch (type) {
            case -9:
                return this.currency.getName(this.symbols.getULocale(), 3, (boolean[]) null);
            case -8:
                return "�";
            case -7:
                return this.currency.getName(this.symbols.getULocale(), 2, this.plural.getKeyword(), (boolean[]) null);
            case -6:
                return this.currency.getCurrencyCode();
            case -5:
                if (this.unitWidth == NumberFormatter.UnitWidth.ISO_CODE) {
                    return this.currency.getCurrencyCode();
                }
                if (this.unitWidth == NumberFormatter.UnitWidth.HIDDEN) {
                    return "";
                }
                if (this.unitWidth != NumberFormatter.UnitWidth.NARROW) {
                    selector = 0;
                }
                return this.currency.getName(this.symbols.getULocale(), selector, (boolean[]) null);
            case -4:
                return this.symbols.getPerMillString();
            case -3:
                return this.symbols.getPercentString();
            case -2:
                return this.symbols.getPlusSignString();
            case -1:
                return this.symbols.getMinusSignString();
            default:
                throw new AssertionError();
        }
    }
}
