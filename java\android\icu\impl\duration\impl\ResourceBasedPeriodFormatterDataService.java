package android.icu.impl.duration.impl;

import android.icu.impl.ICUData;
import android.icu.util.ICUUncheckedIOException;
import com.getui.gtc.extension.distribution.gbd.p156g.p157a.C1965e;
import java.p654io.BufferedReader;
import java.p654io.IOException;
import java.p654io.InputStream;
import java.p654io.InputStreamReader;
import java.p654io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.MissingResourceException;

/* loaded from: D:\桌面\元气桌面壁纸\com.cmcm.cfwallpaper\classes78.dex */
public class ResourceBasedPeriodFormatterDataService extends PeriodFormatterDataService {
    private static final String PATH = "data/";
    private static final ResourceBasedPeriodFormatterDataService singleton = new ResourceBasedPeriodFormatterDataService();
    private Collection<String> availableLocales;
    private PeriodFormatterData lastData = null;
    private String lastLocale = null;
    private Map<String, PeriodFormatterData> cache = new HashMap();

    public static ResourceBasedPeriodFormatterDataService getInstance() {
        return singleton;
    }

    private ResourceBasedPeriodFormatterDataService() {
        List<String> localeNames = new ArrayList<>();
        InputStream is = ICUData.getRequiredStream(getClass(), "data/index.txt");
        try {
            try {
                BufferedReader br = new BufferedReader(new InputStreamReader(is, C1965e.f6503z));
                while (true) {
                    String string = br.readLine();
                    if (string == null) {
                        break;
                    }
                    String string2 = string.trim();
                    if (!string2.startsWith("#") && string2.length() != 0) {
                        localeNames.add(string2);
                    }
                }
                br.lambda$new$0();
                this.availableLocales = Collections.unmodifiableList(localeNames);
            } catch (IOException e) {
                throw new IllegalStateException("IO Error reading data/index.txt: " + e.toString());
            }
        } finally {
            try {
                is.lambda$new$0();
            } catch (IOException e2) {
            }
        }
    }

    @Override // android.icu.impl.duration.impl.PeriodFormatterDataService
    public PeriodFormatterData get(String localeName) {
        int x = localeName.indexOf(64);
        if (x != -1) {
            localeName = localeName.substring(0, x);
        }
        synchronized (this) {
            if (this.lastLocale != null && this.lastLocale.equals(localeName)) {
                return this.lastData;
            }
            PeriodFormatterData ld = this.cache.get(localeName);
            if (ld == null) {
                String ln = localeName;
                while (true) {
                    if (!this.availableLocales.contains(ln)) {
                        int ix = ln.lastIndexOf("_");
                        if (ix > -1) {
                            ln = ln.substring(0, ix);
                        } else if (!"test".equals(ln)) {
                            ln = "test";
                        } else {
                            ln = null;
                            break;
                        }
                    } else {
                        break;
                    }
                }
                if (ln != null) {
                    String name = "data/pfd_" + ln + ".xml";
                    try {
                        try {
                            InputStreamReader reader = new InputStreamReader(ICUData.getRequiredStream(getClass(), name), C1965e.f6503z);
                            DataRecord dr = DataRecord.read(ln, new XMLRecordReader(reader));
                            reader.lambda$new$0();
                            if (dr != null) {
                                ld = new PeriodFormatterData(localeName, dr);
                            }
                            this.cache.put(localeName, ld);
                        } catch (IOException e) {
                            throw new ICUUncheckedIOException("Failed to close() resource " + name, e);
                        }
                    } catch (UnsupportedEncodingException e2) {
                        throw new MissingResourceException("Unhandled encoding for resource " + name, name, "");
                    }
                } else {
                    throw new MissingResourceException("Duration data not found for  " + localeName, PATH, localeName);
                }
            }
            this.lastData = ld;
            this.lastLocale = localeName;
            return ld;
        }
    }

    @Override // android.icu.impl.duration.impl.PeriodFormatterDataService
    public Collection<String> getAvailableLocales() {
        return this.availableLocales;
    }
}
